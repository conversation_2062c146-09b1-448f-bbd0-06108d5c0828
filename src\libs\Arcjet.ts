import arcjet, { shield } from '@arcjet/next';
import { z } from 'zod';
import { logger } from './Logger';

const envSchema = z.object({
  ARCJET_KEY: z.string()
});

const env = envSchema.parse(process.env);

// Re-export the rules to simplify imports inside handlers
export { detectBot, fixedWindow, protectSignup, request, sensitiveInfo, shield, slidingWindow } from '@arcjet/next';

// Create a base Arcjet instance which can be imported and extended in each route.
export default arcjet({
  // Get your site key from https://launch.arcjet.com/Q6eLbRE
  key: env.ARCJET_KEY,
  // Identify the user by their IP address
  characteristics: ['ip.src'],
  rules: [
    // Protect against common attacks with Arcjet Shield
    shield({
      mode: 'LIVE' // will block requests. Use "DRY_RUN" to log only
    })
    // Other rules are added in different routes
  ],
  log: logger
});
