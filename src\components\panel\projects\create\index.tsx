'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { useCreateProject } from '@/services/api/user/projects';
import { typeItems } from '../type-list';
// 👇 Add the fields to the schema
const formSchema = z.object({
  name: z.string().min(3, 'Project name is required').max(255, 'Reached max project name length 255'),
  description: z
    .union([
      z.string().length(0), // Allow empty string
      z
        .string()
        .min(3, 'Description must be at least 3 characters')
        .max(255, 'Description cannot exceed 255 characters')
    ])
    .optional()
    .transform(val => val || undefined),
  url: z.string().url('Invalid URL format').optional().or(z.literal('')),
  type: z.coerce.number().default(4),
  soft_limit: z
    .union([
      z.number().min(0).max(999999.99),
      z
        .string()
        .regex(/^\d*(\.\d{0,2})?$/, 'Must be a number with up to 2 decimal places')
        .transform(Number)
    ])
    .pipe(z.number().min(0, 'Must be positive')),
  hard_limit: z
    .union([
      z.number().min(0).max(999999.99),
      z
        .string()
        .regex(/^\d*(\.\d{0,2})?$/, 'Must be a number with up to 2 decimal places')
        .transform(Number)
    ])
    .pipe(z.number().min(0, 'Must be positive'))
});

type FormValues = z.infer<typeof formSchema>;

export function CreateProject() {
  const router = useRouter();

  const queryClient = useQueryClient();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      url: '',
      type: 4,
      soft_limit: 0.0,
      hard_limit: 0.0
    }
  });

  const { mutate, isPending } = useCreateProject({
    onSuccess: () => {
      toast.success('Project created successfully');
      router.push('/panel/projects');

      queryClient.refetchQueries({ queryKey: [API_QUERY_KEY.PROJECTS_LIST] });
    },
    onError: () => {
      toast.error('Failed to create project');
    }
  });

  const onSubmit = (values: FormValues) => {
    const submissionData = {
      ...values,
      url: values.url || undefined,
      description: values.description || undefined
    };

    mutate({ body: submissionData });
  };

  return (
    <div className='flex flex-col gap-6  mx-auto container space-y-6 p-4 md:p-8'>
      {/* Back Button */}
      <div className='flex gap-2 mb-5 items-end justify-between'>
        <div>
          <h4 className='text-gray-900 text-2xl  dark:text-primary mb-1'>New Project</h4>
          <p className='text-gray-600'>Create new Project</p>
        </div>
        <Link href='/panel/projects'>
          <Button
            variant='ghost'
            size='sm'
            className='flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground'
          >
            <ArrowLeft className='w-4 h-4' />
            Back to Projects
          </Button>
        </Link>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader className='pb-2'>
          <CardTitle className='text-xl font-semibold'>New Project</CardTitle>
        </CardHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className='grid gap-6'>
              <div className='grid grid-cols-2'>
                {/* Project Name */}
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex'>
                        <FormLabel>Name</FormLabel>
                        <span className='text-destructive  ml-1'>*</span>
                      </div>
                      <FormControl>
                        <Input
                          placeholder='Enter project name'
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="What's this project about?"
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* URL */}
                <FormField
                  control={form.control}
                  name='url'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>URL</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='https://example.com'
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Type */}
                <FormField
                  control={form.control}
                  name='type'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} disabled={isPending} defaultValue={String(field.value)}>
                          <SelectTrigger className='w-full'>
                            <SelectValue placeholder='Select a type' />
                          </SelectTrigger>
                          <SelectContent>
                            {typeItems.map(item => (
                              <SelectItem key={item.key} value={item.key}>
                                <div className='flex items-center gap-2'>
                                  {item.icon}
                                  <span>{item.value}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Soft Limit */}
                <FormField
                  control={form.control}
                  name='soft_limit'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Soft Limit</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground'>
                            $
                          </span>
                          <Input
                            type='number'
                            step='0.01'
                            disabled={isPending}
                            className='rounded-lg pl-6'
                            min={0}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Hard Limit */}
                <FormField
                  control={form.control}
                  name='hard_limit'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hard Limit</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground'>
                            $
                          </span>
                          <Input
                            type='number'
                            step='0.01'
                            disabled={isPending}
                            className='rounded-lg pl-6'
                            min={0}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='flex w-auto justify-end'>
                <Button type='submit' disabled={isPending}>
                  {isPending ? (
                    <div className='flex items-center gap-2'>
                      <CyrcleSvg />
                      Creating...
                    </div>
                  ) : (
                    'Create Project'
                  )}
                </Button>
              </div>
            </CardContent>
          </form>
        </Form>
      </Card>
    </div>
  );
}
