'use client';

import Link from 'next/link';
import BlogPosts from '@/components/blog/blog';
import { IMAGE_URL } from '@/configs/image-url';

const stats = [
  { label: 'Avg. platform uptime', value: '99.99%' },
  { label: 'Latency to major brokers', value: '<1ms' },
  { label: 'Trades executed monthly', value: '+2 million' },
  { label: 'Active VPS instances', value: '+3,200' }
];
const values = [
  {
    name: 'Strive for excellence',
    description:
      'We aim to deliver a world-class experience in everything we do — from infrastructure to support. Quality and consistency are never compromised.'
  },
  {
    name: 'Share your knowledge',
    description:
      'We believe in openness. Whether it’s insights, tips, or tools, sharing what we know helps everyone grow and strengthens the team.'
  },
  {
    name: 'Keep learning',
    description:
      'Technology evolves, and so do we. We stay curious, improve constantly, and encourage each other to build on our strengths.'
  },
  {
    name: 'Support each other',
    description:
      'We’re in this together. Whether it’s a colleague or a client, we lend a hand, offer guidance, and lead with empathy.'
  },
  {
    name: 'Own your work',
    description:
      'We take pride in our responsibilities. We’re proactive, accountable, and we follow through — especially when it counts.'
  },
  {
    name: 'Balance matters',
    description:
      'We work hard, but we also know when to switch off. Downtime fuels creativity, clarity, and long-term performance.'
  }
];
// const team = [
//   {
//     name: 'Michael Foster',
//     role: 'Co-Founder / CTO',
//     imageUrl:
//       'https://images.unsplash.com/photo-*************-f4e0f30006d5?ixlib=rb-=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80'
//   }
//   // More people...
// ];

const timeline = [
  {
    name: 'Start Idea',
    description:
      'A spark of inspiration. Navigating challenges and exploring fresh possibilities, despite the odds. A journey begins, full of ambition and grit.',
    date: 'Aug 2022',
    dateTime: '2022-08'
  },
  {
    name: 'Launch MVP',
    description:
      'Pushed past hurdles to bring the first version to life. Honest effort, real feedback, and a proper step forward in building something that matters.',
    date: 'Jun 2023',
    dateTime: '2023-06'
  },
  {
    name: 'Funded Company',
    description:
      'Secured backing to take things to the next level. A real show of belief in the vision. Time to scale and make a real impact.',
    date: 'Jan 2025',
    dateTime: '2025-01'
  },
  {
    name: 'Global launch',
    description:
      'The big moment. Reaching audiences around the world with a product built to last. A proud milestone, marking the beginning of a much larger chapter.',
    date: 'Jun 2025',
    dateTime: '2025-06'
  }
];
const jobOpenings = [
  {
    id: 1,
    role: 'Golang Developer',
    href: '#',
    description:
      'We’re after a sharp and capable Golang developer to help us build high-performance systems. You’ll be working with a small, agile team delivering clean, efficient code in real-world applications.',
    salary: '£65,000 – £85,000 GBP',
    location: 'London, UK (Hybrid)'
  },
  {
    id: 2,
    role: 'Social Media Manager',
    href: '#',
    description:
      'Looking for a creative and organised social media expert to lead our online presence. From TikTok to LinkedIn, you’ll craft compelling content, grow engagement, and bring our brand to life.',
    salary: '£35,000 – £50,000 GBP',
    location: 'Remote (UK-based)'
  },
  {
    id: 3,
    role: 'Growth Manager',
    href: '#',
    description:
      'We’re on the hunt for a data-savvy Growth Manager who thrives on experimentation and scale. You’ll spot opportunities, run clever campaigns, and drive user acquisition like a pro.',
    salary: '£55,000 – £75,000 GBP + bonus',
    location: 'Manchester, UK'
  },
  {
    id: 4,
    role: 'Business Developer',
    href: '#',
    description:
      'We need a commercially minded Business Developer with a knack for building strong relationships. If you’ve got a head for strategy and a gift for closing deals, we’d love to hear from you.',
    salary: '£45,000 – £60,000 GBP + commission',
    location: 'Birmingham, UK (On-site/Hybrid)'
  }
];

export default function About() {
  return (
    <div className='bg-white'>
      <main className='isolate'>
        {/* Hero section */}
        <div className='relative isolate -z-10'>
          <svg
            className='absolute inset-x-0 top-0 -z-10 h-[64rem] w-full stroke-gray-200 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]'
            aria-hidden='true'
          >
            <defs>
              <pattern
                id='1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84'
                width={200}
                height={200}
                x='50%'
                y={-1}
                patternUnits='userSpaceOnUse'
              >
                <path d='M.5 200V.5H200' fill='none' />
              </pattern>
            </defs>
            <svg x='50%' y={-1} className='overflow-visible fill-gray-50'>
              <path
                d='M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z'
                strokeWidth={0}
              />
            </svg>
            <rect width='100%' height='100%' strokeWidth={0} fill='url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)' />
          </svg>
          <div
            className='absolute left-1/2 right-0 top-0 -z-10 -ml-24 transform-gpu overflow-hidden blur-3xl lg:ml-24 xl:ml-48'
            aria-hidden='true'
          >
            <div
              className='aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30'
              style={{
                clipPath:
                  'polygon(63.1% 29.5%, 100% 17.1%, 76.6% 3%, 48.4% 0%, 44.6% 4.7%, 54.5% 25.3%, 59.8% 49%, 55.2% 57.8%, 44.4% 57.2%, 27.8% 47.9%, 35.1% 81.5%, 0% 97.7%, 39.2% 100%, 35.2% 81.4%, 97.2% 52.8%, 63.1% 29.5%)'
              }}
            />
          </div>
          <div className='overflow-hidden'>
            <div className='mx-auto max-w-7xl px-6 pb-32 pt-36 sm:pt-60 lg:px-8 lg:pt-32'>
              <div className='mx-auto max-w-2xl gap-x-14 lg:mx-0 lg:flex lg:max-w-none lg:items-center'>
                <div className='w-full max-w-xl lg:shrink-0 xl:max-w-2xl'>
                  <h1 className='text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl'>
                    The Future of Forex Trading Starts Here
                  </h1>
                  <p className='relative mt-6 text-lg leading-8 text-gray-600 sm:max-w-md lg:max-w-none'>
                    At TradeVPS.net, we’re transforming the way traders conquer the markets. Our passionate team of
                    expert engineers is dedicated to building a cutting-edge trading ecosystem powered by innovation,
                    precision, and unparalleled performance. Designed for Forex, stock, and crypto traders, our
                    AI-driven, cloud-native platform delivers professional-grade tools, ultra-low latency, and
                    enterprise-grade security—empowering you to trade smarter, faster, and from anywhere in the world.
                  </p>
                </div>
                <div className='mt-14 flex justify-end gap-8 sm:-mt-44 sm:justify-start sm:pl-20 lg:mt-0 lg:pl-0'>
                  <div className='ml-auto w-44 flex-none space-y-8 pt-32 sm:ml-0 sm:pt-80 lg:order-last lg:pt-36 xl:order-none xl:pt-80'>
                    <div className='relative'>
                      <img
                        src='https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&h=528&q=80'
                        alt=''
                        className='aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg'
                      />
                      <div className='pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10' />
                    </div>
                  </div>
                  <div className='mr-auto w-44 flex-none space-y-8 sm:mr-0 sm:pt-52 lg:pt-36'>
                    <div className='relative'>
                      <img
                        src='https://images.unsplash.com/photo-1485217988980-11786ced9454?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&h=528&q=80'
                        alt=''
                        className='aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg'
                      />
                      <div className='pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10' />
                    </div>
                    <div className='relative'>
                      <img
                        src='https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&crop=focalpoint&fp-x=.4&w=396&h=528&q=80'
                        alt=''
                        className='aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg'
                      />
                      <div className='pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10' />
                    </div>
                  </div>
                  <div className='w-44 flex-none space-y-8 pt-32 sm:pt-0'>
                    <div className='relative'>
                      <img
                        src='https://images.unsplash.com/photo-1670272504528-790c24957dda?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&crop=left&w=400&h=528&q=80'
                        alt=''
                        className='aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg'
                      />
                      <div className='pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10' />
                    </div>
                    <div className='relative'>
                      <img
                        src='https://images.unsplash.com/photo-1670272505284-8faba1c31f7d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&h=528&q=80'
                        alt=''
                        className='aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg'
                      />
                      <div className='pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10' />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timeline section */}
        <div className='mx-auto -mt-8 max-w-7xl px-6 lg:px-8'>
          <div className='mx-auto grid max-w-2xl grid-cols-1 gap-8 overflow-hidden lg:mx-0 lg:max-w-none lg:grid-cols-4'>
            {timeline.map(item => (
              <div key={item.name}>
                <time
                  dateTime={item.dateTime}
                  className='flex items-center text-sm font-semibold leading-6 text-indigo-600'
                >
                  <svg viewBox='0 0 4 4' className='mr-4 h-1 w-1 flex-none' aria-hidden='true'>
                    <circle cx={2} cy={2} r={2} fill='currentColor' />
                  </svg>
                  {item.date}
                  <div
                    className='absolute -ml-2 h-px w-screen -translate-x-full bg-gray-900/10 sm:-ml-4 lg:static lg:-mr-6 lg:ml-8 lg:w-auto lg:flex-auto lg:translate-x-0'
                    aria-hidden='true'
                  />
                </time>
                <p className='mt-6 text-lg font-semibold leading-8 tracking-tight text-gray-900'>{item.name}</p>
                <p className='mt-1 text-base leading-7 text-gray-600'>{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Content section */}
        <div className='mx-auto mt-32 max-w-7xl px-6 lg:px-8'>
          <div className='mx-auto max-w-2xl lg:mx-0 lg:max-w-none'>
            <h2 className='text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl'>Our mission</h2>
            <div className='mt-6 flex flex-col gap-x-8 gap-y-20 lg:flex-row'>
              <div className='lg:w-full lg:max-w-2xl lg:flex-auto'>
                <p className='text-xl leading-8 text-gray-600'>
                  Designed with performance in mind, TradeVPS ensures seamless execution and low-latency access for
                  traders who demand reliability. Whether you’re running expert advisors or managing complex strategies,
                  our infrastructure keeps you connected – fast, secure, and consistent.
                </p>
                <div className='mt-10 max-w-xl text-base leading-7 text-gray-700'>
                  <p>
                    Enjoy optimised server speeds, dependable uptime, and real-time connectivity to your trading
                    platforms. TradeVPS gives you the edge with systems built to handle high-frequency trading and 24/7
                    market action. No delays, no compromise – just professional-grade infrastructure.
                  </p>
                  <p className='mt-10'>
                    Whether you're trading Forex, crypto, or indices, our UK-hosted servers offer a smooth, stable
                    experience backed by expert support. Focus on your trades while we handle the tech – it’s what we do
                    best. Trusted by traders, built for performance.
                  </p>
                </div>
              </div>
              <div className='lg:flex lg:flex-auto lg:justify-center'>
                <dl className='w-64 space-y-8 xl:w-80'>
                  {stats.map(stat => (
                    <div key={stat.label} className='flex flex-col-reverse gap-y-4'>
                      <dt className='text-base leading-7 text-gray-600'>{stat.label}</dt>
                      <dd className='text-5xl font-semibold tracking-tight text-gray-900'>{stat.value}</dd>
                    </div>
                  ))}
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Image section */}
        <div className='mt-32 sm:mt-40 xl:mx-auto xl:max-w-7xl xl:px-8'>
          <img
            src='https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2832&q=80'
            alt=''
            className='aspect-[5/2] w-full object-cover xl:rounded-3xl'
          />
        </div>

        {/* Values section */}
        <div className='mx-auto mt-32 max-w-7xl px-6 sm:mt-40 lg:px-8'>
          <div className='mx-auto max-w-2xl lg:mx-0'>
            <h2 className='text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl'>Our Values</h2>
            <p className='mt-6 text-lg leading-8 text-gray-600'>
              At TradeVPS, our principles guide how we build, support, and grow — both as a team and with our clients.
              These values shape everything from the way we deliver service to the way we work together.
            </p>
          </div>
          <dl className='mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 text-base leading-7 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3'>
            {values.map(value => (
              <div key={value.name}>
                <dt className='font-semibold text-gray-900'>{value.name}</dt>
                <dd className='mt-1 text-gray-600'>{value.description}</dd>
              </div>
            ))}
          </dl>
        </div>

        {/* Logo cloud */}
        <div className='relative isolate -z-10 mt-32 sm:mt-48'>
          <div className='absolute inset-x-0 top-1/2 -z-10 flex -translate-y-1/2 justify-center overflow-hidden [mask-image:radial-gradient(50%_45%_at_50%_55%,white,transparent)]'>
            <svg className='h-[40rem] w-[80rem] flex-none stroke-gray-200' aria-hidden='true'>
              <defs>
                <pattern
                  id='e9033f3e-f665-41a6-84ef-756f6778e6fe'
                  width={200}
                  height={200}
                  x='50%'
                  y='50%'
                  patternUnits='userSpaceOnUse'
                  patternTransform='translate(-100 0)'
                >
                  <path d='M.5 200V.5H200' fill='none' />
                </pattern>
              </defs>
              <svg x='50%' y='50%' className='overflow-visible fill-gray-50'>
                <path d='M-300 0h201v201h-201Z M300 200h201v201h-201Z' strokeWidth={0} />
              </svg>
              <rect width='100%' height='100%' strokeWidth={0} fill='url(#e9033f3e-f665-41a6-84ef-756f6778e6fe)' />
            </svg>
          </div>
          <div className='mx-auto max-w-7xl px-6 lg:px-8'>
            <h2 className='text-center text-lg font-semibold leading-8 text-gray-900'>
              Proudly partnering with industry leaders worldwide
            </h2>
            <div className='mx-auto mt-18 grid max-w-lg grid-cols-4 items-center gap-x-8 gap-y-10 sm:max-w-xl sm:grid-cols-6 sm:gap-x-10 lg:mx-0 lg:max-w-none lg:grid-cols-5'>
              <img
                className='col-span-2 max-h-8 w-full object-contain lg:col-span-1'
                src={IMAGE_URL.MICROSOFT}
                alt='Microsoft'
                width={158}
                height={48}
              />
              <img
                className='col-span-2 max-h-8 w-full object-contain lg:col-span-1'
                src={IMAGE_URL.GOOGLE}
                alt='Google'
                width={158}
                height={48}
              />
              <img
                className='col-span-2 max-h-8 w-full object-contain lg:col-span-1'
                src={IMAGE_URL.AWS}
                alt='Amazon Web Services'
                width={158}
                height={48}
              />
              <img
                className='col-span-2 max-h-9 w-full object-contain sm:col-start-2 lg:col-span-1'
                src={IMAGE_URL.CLOUDFLARE}
                alt='Cloudflare'
                width={158}
                height={48}
              />
              <img
                className='col-span-2 col-start-2 max-h-5 w-full object-contain sm:col-start-auto lg:col-span-1'
                src={IMAGE_URL.HETZNER}
                alt='Hetzner'
                width={158}
                height={48}
              />
            </div>
          </div>
        </div>

        {/* Team section */}
        {/* <div className='mx-auto mt-32 max-w-7xl px-6 sm:mt-48 lg:px-8'>
          <div className='mx-auto max-w-2xl lg:mx-0'>
            <h2 className='text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl'>Our team</h2>
            <p className='mt-6 text-lg leading-8 text-gray-600'>
              Sit facilis neque ab nulla vel. Cum eos in laudantium. Temporibus eos totam in dolorum. Nemo vel facere
              repellendus ut eos dolores similique.
            </p>
          </div>
          <ul className='mx-auto mt-20 grid max-w-2xl grid-cols-2 gap-x-8 gap-y-16 text-center sm:grid-cols-3 md:grid-cols-4 lg:mx-0 lg:max-w-none lg:grid-cols-5 xl:grid-cols-6'>
            {team.map(person => (
              <li key={person.name}>
                <img className='mx-auto h-24 w-24 rounded-full' src={person.imageUrl} alt='' />
                <h3 className='mt-6 text-base font-semibold leading-7 tracking-tight text-gray-900'>{person.name}</h3>
                <p className='text-sm leading-6 text-gray-600'>{person.role}</p>
              </li>
            ))}
          </ul>
        </div> */}

        {/* Content section */}
        <div className='mx-auto mt-32 max-w-7xl px-6 sm:mt-40 lg:px-8'>
          <div className='mx-auto flex max-w-2xl flex-col items-end justify-between gap-16 lg:mx-0 lg:max-w-none lg:flex-row'>
            <div className='w-full lg:max-w-lg lg:flex-auto'>
              <h2 className='text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl'>
                We’re always on the lookout for talented individuals to join our team
              </h2>
              <p className='mt-6 text-xl leading-8 text-gray-600'>
                If you’re passionate about technology and keen to make an impact, we’d love to hear from you. Join us as
                we build reliable, high-performance solutions for traders worldwide.
              </p>
              <img
                src={IMAGE_URL.OFFICE}
                alt='Office'
                className='mt-16 aspect-[6/5] w-full rounded-2xl bg-gray-50 object-cover lg:aspect-auto lg:h-[34.5rem]'
              />
            </div>
            <div className='w-full lg:max-w-xl lg:flex-auto'>
              <h3 className='sr-only'>Job openings</h3>
              <ul className='-my-8 divide-y divide-gray-100'>
                {jobOpenings.map(opening => (
                  <li key={opening.id} className='py-8'>
                    <dl className='relative flex flex-wrap gap-x-3'>
                      <dt className='sr-only'>Role</dt>
                      <dd className='w-full flex-none text-lg font-semibold tracking-tight text-gray-900'>
                        <a href={opening.href}>
                          {opening.role}
                          <span className='absolute inset-0' aria-hidden='true' />
                        </a>
                      </dd>
                      <dt className='sr-only'>Description</dt>
                      <dd className='mt-2 w-full flex-none text-base leading-7 text-gray-600'>{opening.description}</dd>
                      <dt className='sr-only'>Salary</dt>
                      <dd className='mt-4 text-base font-semibold leading-7 text-gray-900'>{opening.salary}</dd>
                      <dt className='sr-only'>Location</dt>
                      <dd className='mt-4 flex items-center gap-x-3 text-base leading-7 text-gray-500'>
                        <svg viewBox='0 0 2 2' className='h-0.5 w-0.5 flex-none fill-gray-300' aria-hidden='true'>
                          <circle cx={1} cy={1} r={1} />
                        </svg>
                        {opening.location}
                      </dd>
                    </dl>
                  </li>
                ))}
              </ul>
              <div className='mt-8 flex border-t border-gray-100 pt-8'>
                <Link href='/careers' className='text-sm font-semibold leading-6 text-indigo-600 hover:text-indigo-500'>
                  View all openings
                  {' '}
                  <span aria-hidden='true'>&rarr;</span>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Blog section */}
        <div className='mx-auto mt-32 max-w-7xl px-6 sm:mt-40 lg:px-8'>
          <BlogPosts postLimit={3} />
        </div>
      </main>
    </div>
  );
}
