import { AppWindow, ChevronRight, Grid2X2, Monitor, Plus, Server } from 'lucide-react';
import Link from 'next/link';
import ExpandableLayout from '@/components/layout/expandable-layout';
import { NotificationButton } from '@/components/notification/bell-button';
import { AppSidebar } from '@/components/panel/app-sidebar';

import { BreadcrumbNav } from '@/components/panel/breadcrumb-nav';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { AuthenticationLayout } from '@/layouts/authentication';

import { PanelTemplate } from '@/templates/panel/PanelTemplate';

const deployMenuItems = [
  {
    title: 'Trading Terminal',
    icon: <AppWindow className='h-4 w-4' />,
    price: '$3.99',
    href: PAGE_ROUTES.PANEL_TRADING_TERMINALS_DEPLOY
  },
  {
    title: 'Trading VPS',
    icon: <Server className='h-4 w-4' />,
    price: '$9.99',
    href: PAGE_ROUTES.PANEL_TRADING_VPS
  },
  {
    title: 'Trading Desktop',
    icon: <Monitor className='h-4 w-4' />,
    price: '$13.99',
    href: PAGE_ROUTES.PANEL_TRADING_DESKTOPS
  },
  {
    title: 'Windows Server',
    icon: <Grid2X2 className='h-4 w-4' />,
    price: '$28.99',
    href: PAGE_ROUTES.PANEL_TRADING_WINDOWS
  }
];
export default function DashboardLayout(props: { children: React.ReactNode; params: Promise<{ locale: string }> }) {
  return (
    <PanelTemplate>
      <AuthenticationLayout>
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset>
            <header className='flex h-16 shrink-0 items-center gap-2'>
              <div className='flex items-center gap-2 px-4 w-full'>
                <SidebarTrigger className='-ml-1' />
                <Separator orientation='vertical' className='mr-2 h-4' />
                <div className='w-full max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 items-center'>
                  <BreadcrumbNav />
                  <div className='flex items-center justify-end py-4 gap-4'>
                    <NotificationButton />
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant='blue_primary'
                          size='sm'
                          className='relative group cursor-pointer text-sm overflow-hidden transition-all duration-300'
                        >
                          <div className='absolute inset-0 w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 group-hover:opacity-90' />
                          <div className='absolute inset-0 w-1/2 h-full transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 bg-gradient-to-r from-transparent via-white/20 to-transparent' />
                          <span className='relative flex items-center'>
                            <Plus className='me-1 size-4 group-hover:rotate-90 transition-transform duration-300' />
                            Deploy
                            <ChevronRight className='ml-1 size-3 group-hover:translate-x-1 transition-transform duration-300' />
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end' className='w-64 p-2 animate-in fade-in-0 zoom-in-95'>
                        {deployMenuItems.map(item => (
                          <DropdownMenuItem
                            key={item.title}
                            className='flex items-center justify-between p-2 cursor-pointer group hover:bg-blue-50 rounded-md transition-colors duration-150'
                            asChild
                          >
                            <Link href={item.href}>
                              <div className='flex items-center gap-2'>
                                <div className='p-1 rounded-md bg-blue-100 text-blue-600 group-hover:bg-blue-200 transition-colors duration-150'>
                                  {item.icon}
                                </div>
                                <span className='font-medium'>{item.title}</span>
                              </div>
                              <span className='text-sm text-gray-500 group-hover:text-blue-600 transition-colors duration-150'>
                                {item.price}
                              </span>
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </header>
            <ExpandableLayout children={props.children} />
          </SidebarInset>
        </SidebarProvider>
      </AuthenticationLayout>
    </PanelTemplate>
  );
}
