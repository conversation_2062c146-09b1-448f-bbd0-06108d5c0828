import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { ITableQuery } from '@/types/table-query';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';
import { usePaginationFactory } from '@/hooks/api/use-pagination-factory';
import { SERVER } from '@/libs/Axios';

// type IRegion = {
//   id: string;
//   name: string;
// };
// type ITerminalDetail = {
//   id: string;
//   name: string;
//   region: string;
//   server_name: string;
//   login: string;
//   status: number | null;
//   created_at: string;
//   updated_at: string;
// };

export type ITerminal = {
  id: string;
  name: string;
  platform: string;
  region: string;
  login: string;
  status: 'active' | 'inactive' | 'pending';
  description: string;
};
export type IVPSDetails = {
  id: string;
  name: string;
  sur: string | null;
  provider: number | null;
  region: number | null;
  cpu: number | null;
  memory: number | null;
  storage: number | null;
  storage_class: string | null;
  auto_scale_cpu: number | null;
  auto_scale_memory: number | null;
  charge: number | null;
  soft_limit: number | null;
  hard_limit: number | null;
  ip: number | null;
  has_static_ip: boolean;
  ip_version: number | null;
  metapylot_token: number | null;
  status: number | null;
  synced_at: number | null;
  created_at: string;
  updated_at: string;
};

// Type for VPS list items that includes both VPS and terminal-like properties
export type IVPSListItem = {
  id: string;
  name: string;
  server_name?: string; // Optional as it might not always be present
  login?: string; // Optional as it might not always be present
  region: number | null;
  status: number | null;
  provider: number | null;
  ip: number | null;
  created_at: string;
  updated_at: string;
};
export const useVpsListFetch = (query: ITableQuery, options?: { enabled: boolean }) => {
  return usePaginationFactory({
    url: API_ROUTES.VPS_LIST,
    page: query.page,
    perPage: query.per_page,
    queryKey: [API_QUERY_KEY.TRADE_VPS, JSON.stringify(query)], // ✅ فقط برای cache
    query, // ✅ برای ساختن پارامتر واقعی
    ...options
  });
};
export const useTradeVpsDeployMutation = (
  options?: Pick<IUseMutationFactoryProps<ITerminal>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<ITerminal>({
    url: API_ROUTES.DEPLOY_VPS,
    method: 'POST',
    ...options
  });
};

export const useCahgeVpsPasswordMutation = (
  options?: Pick<IUseMutationFactoryProps<ITerminal>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<ITerminal>({
    url: API_ROUTES.CHANG_VPS_PASSWORD,
    method: 'POST',
    ...options
  });
};
export const useGetVpsDtails = (vps_id: string, options?: { enabled: boolean }) => {
  return useQuery({
    queryKey: [API_QUERY_KEY.VPS_DETAILS, vps_id],
    queryFn: async () => {
      const response = await SERVER.get<IVPSDetails>(`${API_ROUTES.VPS_DETAILS}/${vps_id}`);
      return response.data;
    },
    enabled: !!vps_id,
    ...options
  });
};
