declare module 'globe.gl' {
  type GlobeInstance = {
    hexPolygonsData: (features: any) => GlobeInstance;
    hexPolygonResolution: (resolution: number) => GlobeInstance;
    hexPolygonMargin: (margin: number) => GlobeInstance;
    hexPolygonColor: (color: string | ((d: any) => string)) => GlobeInstance;
    width: (width: number) => GlobeInstance;
    height: (height: number) => GlobeInstance;
    backgroundColor: (color: string) => GlobeInstance;
    showGlobe: (show: boolean) => GlobeInstance;
    showAtmosphere: (show: boolean) => GlobeInstance;
    atmosphereColor: (color: string) => GlobeInstance;
    atmosphereAltitude: (altitude: number) => GlobeInstance;
    pointOfView: (pov: { lat: number; lng: number; altitude: number }) => GlobeInstance;
    globeMaterial: (material: any) => GlobeInstance;
    arcsData: (data: any[]) => GlobeInstance;
    arcColor: (color: string | ((d: any) => string)) => GlobeInstance;
    arcDashLength: (length: number | ((d: any) => number)) => GlobeInstance;
    arcDashGap: (gap: number | ((d: any) => number)) => GlobeInstance;
    arcDashAnimateTime: (time: number) => GlobeInstance;
    arcStroke: (stroke: number) => GlobeInstance;
    arcsTransitionDuration: (duration: number) => GlobeInstance;
    arcDashInitialGap: (gap: number | ((d: any) => number)) => GlobeInstance;
    arcAltitude: (altitude: number | ((d: any) => number)) => GlobeInstance;
    arcAltitudeAutoScale: (scale: number) => GlobeInstance;
    pointsData: (data: any[]) => GlobeInstance;
    pointColor: (color: string | ((d: any) => string)) => GlobeInstance;
    pointAltitude: (altitude: number) => GlobeInstance;
    pointRadius: (radius: number) => GlobeInstance;
    pointsMerge: (merge: boolean) => GlobeInstance;
    pointGlow: (glow: boolean) => GlobeInstance;
    labelsData: (data: any[]) => GlobeInstance;
    labelText: (text: string | ((d: any) => string)) => GlobeInstance;
    labelSize: (size: number) => GlobeInstance;
    labelDotRadius: (radius: number) => GlobeInstance;
    labelColor: (color: string | ((d: any) => string)) => GlobeInstance;
    labelResolution: (resolution: number) => GlobeInstance;
    rotation: (rotation: { x: number; y: number; z: number }) => { x: number; y: number; z: number };
    htmlElementsData: (data: any[]) => GlobeInstance;
    htmlElement: (element: (d: any) => HTMLElement) => GlobeInstance;
    htmlElementsTransitionDuration: (duration: number) => GlobeInstance;
    _destructor?: () => void;
  };

  export default function Globe(): (container: HTMLElement) => GlobeInstance;
}
