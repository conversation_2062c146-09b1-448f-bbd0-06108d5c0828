import type { IUseLogout } from './use-logout.type';
import { PAGE_ROUTES } from 'configs/page-routes';
import { useUser } from 'hooks/user/user';
import { useRouter } from 'next/navigation';
import { useLogoutClientMutate } from 'services/api/auth/use-logout-mutate';

export function useLogout(): IUseLogout {
  const router = useRouter();
  const user = useUser();
  const onLogoutUser = () => {
    router.replace(PAGE_ROUTES.LOGIN);
    user.logout();
  };
  const logoutMutate = useLogoutClientMutate({
    onSuccess: onLogoutUser,
    onError: onLogoutUser
  });

  const logout = () => logoutMutate.mutate();

  return {
    logout
  };
}
