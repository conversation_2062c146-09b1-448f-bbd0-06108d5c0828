// import { LocaleSwitcher } from "@/components/LocaleSwitcher";
// import Header from "@/components/marketing/layout/header";
import MarketPlaceTemplate from '@/templates/market-place/MarketPlaceTemplate';

// import { getTranslations, setRequestLocale } from "next-intl/server";
// import Link from "next/link";
export default async function Layout(props: { children: React.ReactNode }) {
  // const { locale } = await props.params;
  // setRequestLocale(locale);
  // const t = await getTranslations({
  // locale,
  // namespace: "RootLayout",
  // });
  return (
    <div>
      <MarketPlaceTemplate>{props.children}</MarketPlaceTemplate>
    </div>
  );
}
