// import { LocaleSwitcher } from "@/components/LocaleSwitcher";
// import Header from "@/components/marketing/layout/header";
import { MarketingTemplate } from '@/templates/marketing/MarketingTemplate';
// import { getTranslations, setRequestLocale } from "next-intl/server";
// import Link from "next/link";

export default async function Layout(props: { children: React.ReactNode; params: Promise<{ locale: string }> }) {
  // const { locale } = await props.params;
  // setRequestLocale(locale);
  // const t = await getTranslations({
  // locale,
  // namespace: "RootLayout",
  // });

  return (
    <>
      <MarketingTemplate>{props.children}</MarketingTemplate>
    </>
  );
}
