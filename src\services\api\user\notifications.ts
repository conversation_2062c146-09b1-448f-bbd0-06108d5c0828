import type { IUseMutationFactoryProps } from '@/hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { INotification } from '@/types/notification';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { API_ROUTES } from '@/configs/api-routes';
import { useMutationFactory } from '@/hooks/api/use-mutation-factory';
import { usePaginationFactory } from '@/hooks/api/use-pagination-factory';

type NotificationResponse = {
  ok: boolean;
  msg: string;
  data: INotification[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
};

export const useNotificationsList = (page: number, per_page: number, unread: boolean) => {
  return usePaginationFactory<NotificationResponse, NotificationResponse>({
    url: API_ROUTES.USER_NOTIFICATIONS,
    queryKey: [API_QUERY_KEY.USER_NOTIFICATIONS, page, per_page, String(unread)],
    page,
    enabled: true,
    showError: true,
    perPage: per_page,
    query: {
      page,
      per_page,
      unread
    }
  });
};
export const useMarkAllNotificationsAsReadMutation = (
  options?: Pick<IUseMutationFactoryProps<NotificationResponse>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<NotificationResponse>({
    url: API_ROUTES.MARK_ALL_NOTIFICATION_AS_READ,
    method: 'POST',
    refetchQueries: [{ queryKey: [API_QUERY_KEY.USER_NOTIFICATIONS] }],
    ...options
  });
};
export const useMarkAsReadMutation = (
  options?: Pick<IUseMutationFactoryProps<NotificationResponse>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<NotificationResponse>({
    url: API_ROUTES.MARK_NOTIFICATION_AS_READ,
    method: 'POST',
    refetchQueries: [{ queryKey: [API_QUERY_KEY.USER_NOTIFICATIONS] }],
    ...options
  });
};
