import { Check, ChevronDown } from 'lucide-react';

import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import PaginationAction from './action';

const CustomPagination = ({
  perpage,
  totalPage,
  pageNumber,
  paginationText,
  handlePerpage,
  handlePage
}: {
  perpage: number;
  totalPage: number;
  pageNumber: number;
  paginationText: string;
  handlePerpage: (value: number) => void;
  handlePage: (value: number) => void;
}) => {
  const perPageOptions = [5, 10, 15, 20, 50, 100];

  return (
    <div className='flex w-full flex-col md:flex-row justify-between items-center gap-5 md:gap-4'>
      <div className='flex items-center px-5 pt-1'>
        <p className='text-sm text-muted-foreground w-[100]'>Rows per page</p>
        <DropdownMenu>
          <DropdownMenuTrigger className='text' asChild>
            <Button variant='outline' className='text-muted-foreground w-[75] flex justify-between'>
              {perpage}
              <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className='w-[150px] pt-1' align='end'>
            {perPageOptions.map(option => (
              <DropdownMenuItem
                key={option}
                onClick={() => handlePerpage(option)}
                className={`capitalize px-2 rounded-b-sm text cursor-pointer py-2 hover:bg-blue-200  
            `}
              >
                {perpage === option ? <Check /> : null}
                <p className={`w-full pl-${perpage === option ? 0 : 6}`}>
                  {' '}
                  {option}
                </p>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className='flex justify-end items-center'>
        <div className=' text-muted-foreground text w-[115px]'>
          {totalPage ? <p className=' text-[14px]'>{paginationText}</p> : null}
        </div>
        <PaginationAction pageCount={totalPage} pageIndex={pageNumber} setPageIndex={handlePage} />
      </div>
    </div>
  );
};

export default CustomPagination;
