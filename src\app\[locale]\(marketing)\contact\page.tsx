'use client';

import { BuildingOffice2Icon, EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useContactFormMutation } from '@/services/api/contact/use-contact-form';

const FormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters').max(255),
  email: z.string().email('Invalid email').max(255),
  phone: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val) {
          return true;
        }
        return /^\+?[1-9]\d{1,14}$/.test(val);
      },
      {
        message: 'Please enter a valid phone number'
      }
    ),
  message: z.string().min(3, 'Message must be at least 3 characters').max(255)
});

type FormData = z.infer<typeof FormSchema>;

const formFields = [
  { label: 'Full Name', name: 'name' as const, type: 'text', placeholder: 'John Doe', span: 2 },
  {
    label: 'Email',
    name: 'email' as const,
    type: 'email',
    placeholder: '<EMAIL>',
    span: 2
  },
  {
    label: 'Phone number',
    name: 'phone' as const,
    type: 'tel',
    placeholder: '+123456789',
    span: 2
  }
];

export default function ContactPage() {
  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      message: ''
    }
  });

  const { mutate: submitContactForm, isPending } = useContactFormMutation({
    onSuccess: () => {
      form.reset();
    }
  });

  function onSubmit(data: FormData) {
    submitContactForm({ body: data });
  }

  return (
    <div className='relative isolate bg-background'>
      <div className='mx-auto grid max-w-7xl grid-cols-1 lg:grid-cols-2'>
        <div className='relative px-6 pb-20 pt-24 sm:pt-32 lg:static lg:px-8 lg:py-48'>
          <div className='mx-auto max-w-xl lg:mx-0 lg:max-w-lg'>
            <div className='absolute inset-y-0 left-0 -z-10 w-full rounded-lg overflow-hidden bg-muted ring-1 ring-border lg:w-1/2'>
              <svg
                className='absolute inset-0 h-full w-full stroke-muted-foreground/10 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]'
                aria-hidden='true'
              >
                <defs>
                  <pattern
                    id='83fd4e5a-9d52-42fc-97b6-718e5d7ee527'
                    width={200}
                    height={200}
                    x='100%'
                    y={-1}
                    patternUnits='userSpaceOnUse'
                  >
                    <path d='M130 200V.5M.5 .5H200' fill='none' />
                  </pattern>
                </defs>
                <rect width='100%' height='100%' strokeWidth={0} fill='hsl(var(--background))' />
                <svg x='100%' y={-1} className='overflow-visible fill-muted'>
                  <path d='M-470.5 0h201v201h-201Z' strokeWidth={0} />
                </svg>
                <rect width='100%' height='100%' strokeWidth={0} fill='url(#83fd4e5a-9d52-42fc-97b6-718e5d7ee527)' />
              </svg>
            </div>
            <h2 className='text-3xl font-bold tracking-tight text-foreground'>Get in touch</h2>
            <p className='mt-6 text-lg leading-8 text-muted-foreground'>
              Have a question or need assistance? Our team is here to help. Contact us via the form, email, or phone,
              and we’ll respond promptly.
            </p>
            <dl className='mt-10 space-y-4 text-base leading-7 text-muted-foreground'>
              <div className='flex gap-x-4'>
                <dt className='flex-none'>
                  <span className='sr-only'>Address</span>
                  <BuildingOffice2Icon className='h-7 w-6 text-muted-foreground' aria-hidden='true' />
                </dt>
                <dd>
                  86-90 Paul Street London,
                  <br />
                  EC2A 4NE, England
                </dd>
              </div>
              <div className='flex gap-x-4'>
                <dt className='flex-none'>
                  <span className='sr-only'>Telephone</span>
                  <PhoneIcon className='h-7 w-6 text-muted-foreground' aria-hidden='true' />
                </dt>
                <dd>
                  <a className='hover:text-foreground transition-colors' href='tel:+442045384248'>
                    +44 20 453 84 248
                  </a>
                </dd>
              </div>
              <div className='flex gap-x-4'>
                <dt className='flex-none'>
                  <span className='sr-only'>Email</span>
                  <EnvelopeIcon className='h-7 w-6 text-muted-foreground' aria-hidden='true' />
                </dt>
                <dd>
                  <a className='hover:text-foreground transition-colors' href='mailto:<EMAIL>'>
                    <EMAIL>
                  </a>
                </dd>
              </div>
            </dl>
          </div>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='px-6 pb-24 pt-20 sm:pb-32 lg:px-8 lg:py-48'>
            <div className='mx-auto max-w-xl lg:mr-0 lg:max-w-lg'>
              <div className='grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2'>
                {formFields.map(({ label, name, type, placeholder, span = 1 }) => (
                  <FormField
                    key={name}
                    control={form.control}
                    name={name}
                    render={({ field }) => (
                      <FormItem className={`${span === 2 ? 'sm:col-span-2' : ''} space-y-2.5`}>
                        <FormLabel className='text-foreground'>{label}</FormLabel>
                        <FormControl>
                          <Input
                            type={type}
                            placeholder={placeholder}
                            className='bg-background border-border'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className='text-destructive text-sm' />
                      </FormItem>
                    )}
                  />
                ))}
                <FormField
                  control={form.control}
                  name='message'
                  render={({ field }) => (
                    <FormItem className='sm:col-span-2 space-y-2.5'>
                      <FormLabel className='text-foreground'>Message</FormLabel>
                      <FormControl>
                        <Textarea
                          rows={4}
                          placeholder='Your message here...'
                          className='bg-background border-border resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='text-destructive text-sm' />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className='mt-8 flex justify-end'>
              <Button type='submit' variant='default' disabled={isPending}>
                {isPending ? 'Sending...' : 'Send message'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
