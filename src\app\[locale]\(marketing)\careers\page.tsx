'use client';

import { motion } from 'framer-motion';
import {
  Briefcase,
  Building2,
  ChevronRight,
  Clock,
  Code2,
  Database,
  GraduationCap,
  HeartHandshake,
  Laptop,
  LifeBuoy,
  LineChart,
  Plane,
  Share2,
  Smile,
  Sun,
  Users2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const benefits = [
  {
    icon: <Sun className='h-6 w-6' />,
    title: 'Flexible Hours',
    description: 'Work when you`re most productive with our flexible scheduling policy',
    color: 'bg-amber-500/10 text-amber-500',
    gradient: 'hover:bg-gradient-to-br hover:from-amber-500/20 hover:to-amber-500/5'
  },
  {
    icon: <Plane className='h-6 w-6' />,
    title: 'Paid Time Off',
    description: 'Generous vacation policy with paid holidays and sick leave',
    color: 'bg-blue-500/10 text-blue-500',
    gradient: 'hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-blue-500/5'
  },
  {
    icon: <Laptop className='h-6 w-6' />,
    title: 'Remote Work',
    description: 'Work from anywhere with our remote-first approach',
    color: 'bg-green-500/10 text-green-500',
    gradient: 'hover:bg-gradient-to-br hover:from-green-500/20 hover:to-green-500/5'
  },
  {
    icon: <GraduationCap className='h-6 w-6' />,
    title: 'Learning Budget',
    description: 'Annual budget for courses, conferences, and learning materials',
    color: 'bg-purple-500/10 text-purple-500',
    gradient: 'hover:bg-gradient-to-br hover:from-purple-500/20 hover:to-purple-500/5'
  },
  {
    icon: <LifeBuoy className='h-6 w-6' />,
    title: 'Health Insurance',
    description: 'Comprehensive health, dental, and vision coverage',
    color: 'bg-red-500/10 text-red-500',
    gradient: 'hover:bg-gradient-to-br hover:from-red-500/20 hover:to-red-500/5'
  },
  {
    icon: <Users2 className='h-6 w-6' />,
    title: 'Team Events',
    description: 'Regular team building activities and social events',
    color: 'bg-indigo-500/10 text-indigo-500',
    gradient: 'hover:bg-gradient-to-br hover:from-indigo-500/20 hover:to-indigo-500/5'
  }
];

const openPositions = [
  {
    title: 'Senior ReactJS Developer',
    department: 'Engineering',
    location: 'Remote',
    type: 'Full-time',
    description: 'Lead frontend development using React, Next.js, and modern web technologies',
    requirements: ['5+ years React experience', 'Strong TypeScript skills', 'Experience with Next.js'],
    icon: <Code2 className='h-6 w-6' />
  },
  {
    title: 'Senior Golang Developer',
    department: 'Engineering',
    location: 'Remote',
    type: 'Full-time',
    description: 'Build high-performance backend services and distributed systems',
    requirements: ['5+ years Go experience', 'Microservices architecture', 'Cloud platforms'],
    icon: <Database className='h-6 w-6' />
  },
  {
    title: 'Business Development Manager',
    department: 'Sales',
    location: 'Hybrid',
    type: 'Full-time',
    description: 'Drive business growth and develop strategic partnerships',
    requirements: ['7+ years in B2B sales', 'Strong network', 'FinTech experience'],
    icon: <LineChart className='h-6 w-6' />
  },
  {
    title: 'Social Media Manager',
    department: 'Marketing',
    location: 'Remote',
    type: 'Full-time',
    description: 'Create and execute social media strategy across platforms',
    requirements: ['3+ years in social media', 'Content creation', 'Analytics'],
    icon: <Share2 className='h-6 w-6' />
  }
];

const cultureItems = [
  {
    icon: <Building2 className='h-8 w-8' />,
    title: 'Innovation First',
    description: 'We encourage creative thinking and welcome new ideas from everyone.',
    color: 'bg-cyan-500/10 text-cyan-500',
    gradient: 'hover:bg-gradient-to-br hover:from-cyan-500/20 hover:to-cyan-500/5'
  },
  {
    icon: <HeartHandshake className='h-8 w-8' />,
    title: 'Collaborative Spirit',
    description: 'Work alongside passionate individuals who share your drive for excellence.',
    color: 'bg-pink-500/10 text-pink-500',
    gradient: 'hover:bg-gradient-to-br hover:from-pink-500/20 hover:to-pink-500/5'
  },
  {
    icon: <Smile className='h-8 w-8' />,
    title: 'Work-Life Balance',
    description: 'We value your time and ensure you have space for what matters most.',
    color: 'bg-yellow-500/10 text-yellow-500',
    gradient: 'hover:bg-gradient-to-br hover:from-yellow-500/20 hover:to-yellow-500/5'
  }
];

export default function CareersPage() {
  return (
    <div className='min-h-screen bg-background'>
      <div className='relative overflow-hidden bg-gradient-to-br from-primary/10 via-purple-500/5 to-blue-500/10'>
        <div className='absolute inset-0 bg-grid-primary/[0.03] dark:bg-grid-primary/[0.03]' />
        <div className='absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent' />

        <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-24'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className='text-center max-w-3xl mx-auto'
          >
            <h1 className='text-6xl md:text-7xl font-bold text-foreground'>Join Our Team</h1>
            <p className='mt-8 text-xl text-muted-foreground leading-relaxed'>
              Help us build the future of trading infrastructure. We're looking for exceptional people who are
              passionate about making an impact.
            </p>
            <div className='mt-12 flex flex-col sm:flex-row gap-6 justify-center'>
              <Button size='lg' className='px-8 py-6 text-lg font-medium'>
                View Open Positions
                <ChevronRight className='ml-2 h-5 w-5' />
              </Button>
              <Button size='lg' variant='outline' className='px-8 py-6 text-lg font-medium border-2'>
                Learn About Culture
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <section className='py-24 bg-primary dark:bg-background text-primary-foreground dark:text-foreground'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className='text-center mb-16'
          >
            <h2 className='text-4xl font-bold mb-6 text-white dark:text-foreground'>Our Culture</h2>
            <p className='text-xl text-white/80 dark:text-muted-foreground max-w-3xl mx-auto'>
              We foster an environment of innovation, collaboration, and continuous growth.
            </p>
          </motion.div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
            {cultureItems.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <Card className='h-full bg-white/10 dark:bg-card border-white/20 dark:border-border backdrop-blur-sm hover:bg-white/15 dark:hover:bg-accent transition-all duration-300'>
                  <CardContent className='p-8'>
                    <div className='text-white dark:text-foreground p-3 rounded-xl w-fit mb-6'>{item.icon}</div>
                    <h3 className='text-2xl font-semibold mb-4 text-white dark:text-foreground'>{item.title}</h3>
                    <p className='text-white/80 dark:text-muted-foreground'>{item.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className='py-24 bg-gradient-to-br from-background via-blue-500/5 to-primary/5'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className='text-center mb-16'
          >
            <h2 className='text-4xl font-bold mb-6 text-foreground'>Benefits & Perks</h2>
            <p className='text-xl text-muted-foreground max-w-3xl mx-auto'>
              We offer comprehensive benefits to support your growth, health, and work-life balance.
            </p>
          </motion.div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className='h-full shadow-sm hover:shadow-sm transition-all duration-300'>
                  <CardContent className='p-8'>
                    <div className={`${benefit.color} p-3 rounded-xl w-fit mb-6`}>{benefit.icon}</div>
                    <h3 className='text-2xl font-semibold mb-4'>{benefit.title}</h3>
                    <p className='text-muted-foreground'>{benefit.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className='py-24 bg-gradient-to-br from-primary/5 via-purple-500/5 to-background'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className='text-center mb-16'
          >
            <h2 className='text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-primary'>
              Open Positions
            </h2>
            <p className='text-xl text-muted-foreground max-w-3xl mx-auto'>
              Join us in building the next generation of trading infrastructure.
            </p>
          </motion.div>

          <div className='space-y-6'>
            {openPositions.map((position, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className='overflow-hidden shadow-sm hover:shadow-sm transition-all duration-300'>
                  <CardContent className='p-8'>
                    <div className='flex items-start gap-6'>
                      <div className='bg-primary/20 p-3 rounded-xl'>{position.icon}</div>
                      <div className='flex-1'>
                        <div className='flex items-center justify-between mb-4'>
                          <h3 className='text-2xl font-semibold'>{position.title}</h3>
                          <Button>Apply Now</Button>
                        </div>
                        <p className='text-muted-foreground mb-4'>{position.description}</p>
                        <div className='flex items-center gap-4 text-sm text-muted-foreground mb-6'>
                          <span className='flex items-center gap-2'>
                            <Briefcase className='h-4 w-4' />
                            {' '}
                            {position.department}
                          </span>
                          <span className='flex items-center gap-2'>
                            <Building2 className='h-4 w-4' />
                            {' '}
                            {position.location}
                          </span>
                          <span className='flex items-center gap-2'>
                            <Clock className='h-4 w-4' />
                            {' '}
                            {position.type}
                          </span>
                        </div>
                        <div className='flex flex-wrap gap-2'>
                          {position.requirements.map((req, idx) => (
                            <span
                              key={idx}
                              className='bg-gradient-to-r from-primary/10 to-purple-500/10 text-primary px-3 py-1 rounded-full text-sm'
                            >
                              {req}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
