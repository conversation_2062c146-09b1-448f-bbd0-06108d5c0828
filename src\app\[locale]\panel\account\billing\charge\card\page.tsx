'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const formSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  cardNumber: z.string().min(16, 'Invalid card number'),
  expiryDate: z.string().min(5, 'Invalid expiry date'),
  cvv: z.string().min(3, 'Invalid CVV')
});

export default function ChargePage() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    }
  });

  function onSubmit(_values: z.infer<typeof formSchema>) {
    // console.log(values);
  }

  return (
    <div className='max-w-2xl mx-auto py-8'>
      <div className='mb-6'>
        <Link
          href='/panel/account/billing'
          className='text-sm text-muted-foreground hover:text-primary flex items-center gap-1'
        >
          <ArrowLeft className='size-4' />
          Back to Billing
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Add Funds with Credit Card</CardTitle>
          <CardDescription>Securely add funds to your account using your credit card</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount (USD)</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter amount' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='cardNumber'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Card Number</FormLabel>
                    <FormControl>
                      <Input placeholder='1234 5678 9012 3456' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='expiryDate'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiry Date</FormLabel>
                      <FormControl>
                        <Input placeholder='MM/YY' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='cvv'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CVV</FormLabel>
                      <FormControl>
                        <Input placeholder='123' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button type='submit' className='w-full'>
                Add Funds
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
