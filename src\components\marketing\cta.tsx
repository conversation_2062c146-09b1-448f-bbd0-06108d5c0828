'use client';
import Image from 'next/image';
import Link from 'next/link';
import Logo from '@/public/img/logo.svg';
import cTrader from '@/public/img/platforms/ctrader.webp';
import MetaTrader4 from '@/public/img/platforms/metatrader-4.webp';
import MetaTrader5 from '@/public/img/platforms/metatrader-5.webp';
import MultiChart from '@/public/img/platforms/multicharts.svg';
import SQX from '@/public/img/platforms/sqx.svg';
import TradeStation from '@/public/img/platforms/trade-station.webp';
import Tradovate from '@/public/img/platforms/tradovate.webp';

export default function Cta() {
  return (
    <section>
      <div className='py-12 md:py-20'>
        <div className='max-w-6xl mx-auto px-4 sm:px-6'>
          <div className='relative max-w-3xl mx-auto text-center pb-12 md:pb-16'>
            <div className='inline-flex items-center justify-center w-20 h-20 bg-white rounded-xl shadow-md mb-8 relative before:absolute before:-top-12 before:w-52 before:h-52 before:bg-zinc-900 before:opacity-[.08] before:rounded-full before:blur-3xl before:-z-10'>
              <Link href='/'>
                <Image src={Logo} width={60} height={60} alt='Logo' />
              </Link>
            </div>
            <h2 className='font-inter-tight text-3xl md:text-4xl font-bold text-zinc-900 mb-4'>
              Start your journey
              {' '}
              <em className='relative not-italic inline-flex justify-center items-end'>
                today
                <svg
                  className='absolute fill-zinc-300 w-[calc(100%+1rem)] -z-10'
                  xmlns='http://www.w3.org/2000/svg'
                  width='120'
                  height='10'
                  viewBox='0 0 120 10'
                  aria-hidden='true'
                  preserveAspectRatio='none'
                >
                  <path d='M118.273 6.09C79.243 4.558 40.297 5.459 1.305 9.034c-1.507.13-1.742-1.521-.199-1.81C39.81-.228 79.647-1.568 118.443 4.2c1.63.233 1.377 1.943-.17 1.89Z' />
                </svg>
              </em>
            </h2>
            <p className='text-lg text-zinc-500 mb-8'>
              Gray removes creative distances by connecting beginners, pros, and every team in between. Are you ready to
              start your journey?
            </p>
            <div className='max-w-xs mx-auto sm:max-w-none sm:inline-flex sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4'>
              <div>
                <Link className='btn btn-primary' href='/auth/sign-up'>
                  Try Demo
                </Link>
              </div>
              <div>
                <Link className='btn text-zinc-600 bg-white hover:text-zinc-900 w-full shadow' href='/contact'>
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>
          {/* Clients */}
          <div className='text-center'>
            <ul className='inline-flex flex-wrap items-center justify-center -m-2 [mask-image:linear-gradient(to_right,transparent_8px,_theme(colors.white/70)_64px,_theme(colors.white)_50%,_theme(colors.white/70)_calc(100%-64px),_transparent_calc(100%-8px))]'>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={Tradovate}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='Tradovate Trading Server VPS'
                />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={MetaTrader4}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='MetaTrader 4 Trading Server VPS'
                />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={MetaTrader5}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='MetaTrader 5 Trading Server VPS'
                />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image src={cTrader} width={48} height={48} className='rounded-md' alt='cTrader Trading Server VPS' />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={SQX}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='StrategyQuant X Trading Server VPS'
                />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={MultiChart}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='Multicharts Trading Server VPS'
                />
              </li>
              <li className='m-2 p-4 relative rounded-lg border border-transparent [background:linear-gradient(theme(colors.zinc.50),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.300),theme(colors.zinc.100),theme(colors.zinc.300))_border-box]'>
                <Image
                  src={TradeStation}
                  width={48}
                  height={48}
                  className='rounded-md'
                  alt='TradeStation Trading Server VPS'
                />
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
