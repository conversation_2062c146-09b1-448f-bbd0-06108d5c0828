import type { APIError, IServerChangePasswordParams, IServerChangePasswordResponse } from '@tradevpsnet/client';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';
const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL!);

export const useWindowsServerPasswordChange = (
  id: string,
  options?: {
    onSuccess?: (data: IServerChangePasswordResponse) => void;
    onError?: (error: APIError) => void;
  }
) => {
  return useMutation<IServerChangePasswordResponse, APIError, IServerChangePasswordParams>({
    mutationFn: (params: IServerChangePasswordParams) => client.windows.server_change_password(id, params),
    ...options
  });
};
