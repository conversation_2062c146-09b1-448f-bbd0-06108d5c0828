import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

const AllowWebRequest = () => {
  const { id } = useParams();
  const { mutate, isPending } = useNewTaskMutation();
  const [url, setUrl] = useState('');
  const handleSubmit = () => {
    if (!url) {
      toast.error('Please fill all required fields');
      return;
    }
    mutate(
      {
        params: { id: id as string },
        queryParams: { type: '13' },
        body: {
          payload: {
            url
          }
        }
      },
      {
        onSuccess: () => toast.success('Added Url'),
        onError: () => toast.error('Failed to add url')
      }
    );
  };
  return (
    <div className='w-full flex flex-col gap-1'>
      <div className='flex items-baseline flex-wrap lg:flex-nowrap gap-2.5'>
        <div className='form-label flex basis-1/4 items-center gap-1 max-w-32'>
          <p className='text-sm'>URL</p>
          <span className='text-red-500'>*</span>
        </div>
        <div className='w-full flex flex-col basis-3/4'>
          <Input
            id='url'
            value={url}
            onChange={e => setUrl(e.target.value)}
            disabled={isPending}
            placeholder='e.g. https://df.treydhub.com'
          />
          <Button onClick={handleSubmit} disabled={isPending} className='ml-auto mt-3'>
            {isPending ? 'Submitting...' : 'Submit'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AllowWebRequest;
