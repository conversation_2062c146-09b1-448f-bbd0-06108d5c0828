import type { IServerListQueryParams } from '@tradevpsnet/client';
import { useQuery } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_QUERY_KEY } from '@/configs/api-query-key';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';

const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL);

export const useWindowsServersFetch = (query: IServerListQueryParams, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [API_QUERY_KEY.WINDOWS_SERVERS_LIST, query],
    queryFn: () => client.windows.servers_list(query),
    enabled: options?.enabled ?? true
  });
};
