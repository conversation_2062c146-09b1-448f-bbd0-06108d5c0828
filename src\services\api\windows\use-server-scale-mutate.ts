import type { APIError, IServerScaleParams, IServerScaleResponse } from '@tradevpsnet/client';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';
const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL!);

export const useWindowsServerScale = (
  id: string,
  options?: {
    onSuccess?: (data: IServerScaleResponse) => void;
    onError?: (error: APIError) => void;
  }
) => {
  return useMutation<IServerScaleResponse, APIError, IServerScaleParams>({
    mutationFn: (params: IServerScaleParams) => client.windows.server_scale(id, params),
    ...options
  });
};
