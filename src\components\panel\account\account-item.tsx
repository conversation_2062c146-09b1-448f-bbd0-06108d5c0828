'use client';

import type { LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

type AccountItemProps = {
  icon: LucideIcon;
  title: string;
  description: string;
  href: string;
  color?: string;
};

export function AccountItem({ icon: Icon, title, description, href, color }: AccountItemProps) {
  return (
    <Link href={href} className='block h-full outline-none'>
      <div className='relative h-full'>
        <div className='h-full rounded-lg border bg-card p-6 shadow-sm transition-all duration-300 hover:shadow-md'>
          <div className={cn('inline-flex rounded-lg p-3', color)}>
            <Icon className='h-6 w-6' />
          </div>

          <div className='mt-4 space-y-2'>
            <h3 className='font-semibold tracking-tight'>{title}</h3>
            <p className='text-sm text-muted-foreground'>{description}</p>
          </div>
        </div>
      </div>
    </Link>
  );
}
