import type { IUseQueryFactoryProps } from 'hooks/api/use-query-factory/use-query-factory.type';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { useQueryFactory } from 'hooks/api/use-query-factory';
import { usePaginationFactory } from '@/hooks/api/use-pagination-factory';

type IDepartment = {
  key: number;
  label: string;
};

type IStatus = {
  data: string[];
};
// export const useUserTicketsFetch = (query: any, options?: Pick<IUseQueryFactoryProps<IUserInfo>, 'enabled'>) => {
//   return useQueryFactory<IUserInfo>({
//     queryKey: [API_QUERY_KEY.USER_TICKETS, query],
//     query,
//     url: API_ROUTES.ADD_USER_TICKET,
//     ...options
//   });
// };
export const useUserTicketsFetch = (query: any, options?: { enabled: boolean }) => {
  return usePaginationFactory({
    url: API_ROUTES.ADD_USER_TICKET,
    page: query.page,
    perPage: query.per_page,
    queryKey: [API_QUERY_KEY.USER_TICKETS, JSON.stringify(query)], // ✅ فقط برای cache
    query, // ✅ برای ساختن پارامتر واقعی
    ...options
  });
};
export const useTicketsDepartmentFetch = (options?: Pick<IUseQueryFactoryProps<IDepartment[]>, 'enabled'>) => {
  return useQueryFactory<IDepartment>({
    queryKey: API_QUERY_KEY.TICKETS_DEPARTMENTS,
    url: API_ROUTES.TICKET_DEPARTMENTS,
    ...options
  });
};

export const useTicketsStatusFetch = (options?: Pick<IUseQueryFactoryProps<IStatus[]>, 'enabled'>) => {
  return useQueryFactory<IStatus>({
    queryKey: API_QUERY_KEY.TICKETS_STATUSES,
    url: API_ROUTES.TICKET_STATUSES,
    ...options
  });
};
