export type INotification = {
  id: string;
  type: string;
  notifiable_id: string;
  notifiable_model: string | null;
  data: {
    id: string;
    name: string;
  };
  read_at: string | null;
  created_at: string;
  updated_at: string;
};

export type INotificationResponse = {
  ok: boolean;
  msg: string;
  data: INotification[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    first_page_url: string;
    from: number;
    last_page_url: string;
    links: Record<string, any>;
    next_page_url: string | null;
    prev_page_url: string | null;
  };
};
