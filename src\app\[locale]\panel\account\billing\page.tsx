'use client';
import type { DateRange } from 'react-day-picker';
import type { Invoice } from '@/types/invoice';
import { Download } from 'lucide-react';
import { useEffect, useState } from 'react';
import InvoicesFiltersComponent from '@/components/billing/invoices-filters';
import { InvoicesTable } from '@/components/billing/invoices-table';
import { TopUpButton } from '@/components/billing/top-up-button';
import { Button } from '@/components/ui/button';
import { useSearchQuery } from '@/hooks/search-query';
import { useInvoicesList } from '@/services/api/user/billing/invoices';

export default function BillingPage() {
  const [page, setPage] = useState(1);
  const [filterValue, setFilterValue] = useState('');
  const [per_page, setPerpage] = useState(15);
  const [status, setSelectedStatuses] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>(undefined);
  const [, setInvoices] = useState<Invoice[]>([]);
  const search = useSearchQuery(filterValue, 500);

  const { data: invoicesResponse } = useInvoicesList();

  useEffect(() => {
    if (invoicesResponse) {
      setInvoices(invoicesResponse.data);
    }
  }, [invoicesResponse]);

  return (
    <div>
      <div className='flex items-center justify-between mb-4'>
        <h1 className='text-2xl font-semibold'>Invoices</h1>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='sm'>
            <Download className='w-4 h-4 mr-2' />
            Download
          </Button>
          <TopUpButton />
        </div>
      </div>
      <InvoicesTable
        pageNumber={page}
        setPageNumber={setPage}
        perpage={per_page}
        setPerpageAction={setPerpage}
        search={search}
        status={status.join(',')}
        date_from={selectedDateRange?.from?.toISOString()}
        date_to={selectedDateRange?.to?.toISOString()}
        component={(
          <InvoicesFiltersComponent
            setFilterValue={setFilterValue}
            filterValue={filterValue}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            selectedStatus={status}
            setSelectedStatus={setSelectedStatuses}
          />
        )}
      />
    </div>
  );
}
