'use client';

import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import { AccountInviteBox } from '@/components/panel/account/account-invite-box';
import { AccountOffer } from '@/components/panel/account/account-offer';
import { buttonVariants } from '@/components/ui/button';
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { IMAGE_URL } from '@/configs/image-url';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useUser } from '@/hooks/user/user';
import { cn } from '@/lib/utils';

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  visible: {
    transition: {
      staggerChildren: 0.08
    }
  }
};

const cardHover = {
  initial: {},
  hover: {}
};

const iconHover = {
  initial: {
    y: 0
  },
  hover: {
    y: -2
  }
};

const titleHover = {
  initial: {
    color: 'inherit'
  },
  hover: {
    color: 'var(--primary)'
  }
};

const buttonHover = {
  initial: {
    x: 0
  },
  hover: {
    x: 4
  }
};

type ServiceCardProps = {
  title: string;
  description: string;
  price: string;
  imageSrc: string;
  link: string;
};

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, price, imageSrc, link }) => (
  <motion.div variants={fadeIn} className='h-full'>
    <motion.div initial='initial' whileHover='hover' variants={cardHover} className='h-full'>
      <Card className='border border-border transition-colors duration-200 hover:border-primary/20 h-full'>
        <CardHeader>
          <motion.div variants={iconHover} className='w-12 h-12 flex items-center justify-center mb-4'>
            <Image src={imageSrc} alt={title} width={48} height={48} />
          </motion.div>
          <motion.div variants={titleHover}>
            <CardTitle className='text-lg font-medium'>{title}</CardTitle>
          </motion.div>
          <CardDescription className='text-sm text-muted-foreground mt-2'>{description}</CardDescription>
        </CardHeader>

        <CardFooter>
          <div className='flex items-center justify-between w-full'>
            <motion.span variants={titleHover} className='text-lg font-medium'>
              {price}
            </motion.span>
            <motion.div variants={buttonHover} className='flex items-center'>
              <Link href={link} className={cn(buttonVariants({ variant: 'ghost' }), 'group hover:text-primary')}>
                Learn more
                <ChevronRight className='h-4 w-4 ml-2 transition-transform duration-200 group-hover:translate-x-1' />
              </Link>
            </motion.div>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  </motion.div>
);

const Dashboard = () => {
  const { data: user } = useUser();
  return (
    <motion.div initial='hidden' animate='visible' variants={staggerContainer} className='space-y-8'>
      <motion.div variants={fadeIn} className='w-full max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 items-center'>
        <h2 className='text-xl font-medium tracking-tight'>
          <span className='bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent'>
            Welcome back,
            {' '}
            {user?.name?.split(' ')[0] || 'Guest'}
          </span>
        </h2>
      </motion.div>

      <motion.div variants={fadeIn}>
        <AccountOffer />
      </motion.div>

      <div className='grid lg:grid-cols-3 gap-5 lg:gap-7.5'>
        <div className='lg:col-span-2'>
          <div className='grid md:grid-cols-2 gap-5 lg:gap-7.5'>
            <ServiceCard
              title='Trading Terminal'
              description='Run expert advisors, track terminal activity, manage strategies, monitor performance, and streamline your trading operations.'
              price='$3.99'
              imageSrc={IMAGE_URL.TRADING_TERMINAL}
              link={PAGE_ROUTES.PANEL_TRADING_TERMINALS}
            />
            <ServiceCard
              title='Trading VPS'
              description='Ultra-fast deployment, secure access, low latency, scalable resources, and optimized for seamless trading performance worldwide.'
              price='$9.99'
              imageSrc={IMAGE_URL.TRADING_VPS}
              link={PAGE_ROUTES.PANEL_TRADING_VPS}
            />
            <ServiceCard
              title='Trading Desktop'
              description='A powerful, feature-rich, cloud-based trading server designed for professionals. Access seamlessly from your browser with secure connectivity via SUR.'
              price='$13.99'
              imageSrc={IMAGE_URL.TRADING_DESKTOP}
              link={PAGE_ROUTES.PANEL_TRADING_DESKTOPS}
            />
            <ServiceCard
              title='Windows Server'
              description='Reliable and stable, delivering exceptional performance worldwide with the latest version for seamless trading operations.'
              price='$28.99'
              imageSrc={IMAGE_URL.WINDOWS_SERVER}
              link={PAGE_ROUTES.PANEL_TRADING_WINDOWS}
            />
          </div>
        </div>
        <motion.div variants={fadeIn}>
          <AccountInviteBox />
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Dashboard;
