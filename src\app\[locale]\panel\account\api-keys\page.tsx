'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon, Eye, Pencil, Star, Trash2, X } from 'lucide-react';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { cn } from '@/lib/utils';
import {
  useApiListFetch,
  useCreateApiTokenMutation,
  useRevokeTokenMutation
} from '@/services/api/user/api-key/use-api-key';

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  expires_at: z.date().optional(),
  abilities: z.array(z.enum(['All', 'read', 'write', 'delete'])).min(1, 'At least one ability is required')
});

const columns = (handleRevokeClick: (row: any) => void) => [
  {
    header: 'Name',
    accessorKey: 'name'
  },
  {
    header: 'Created At',
    accessorKey: 'created_at',
    cell: (info: any) => new Date(info.getValue()).toLocaleString()
  },
  {
    header: 'Last Used',
    accessorKey: 'last_used_at',
    cell: (info: any) => (info.getValue() ? new Date(info.getValue()).toLocaleString() : 'Never')
  },
  {
    header: 'Abilities',
    accessorKey: 'abilities',
    cell: (info: any) => {
      const abilities = info.getValue() as string[];
      const hasAllAbilities = abilities.includes('read') && abilities.includes('write') && abilities.includes('delete');

      return (
        <div className='flex items-center gap-2'>
          {hasAllAbilities ? (
            <Star className='h-4 w-4' />
          ) : (
            <>
              {abilities.includes('read') && <Eye className='h-4 w-4' />}
              {abilities.includes('write') && <Pencil className='h-4 w-4' />}
              {abilities.includes('delete') && <Trash2 className='h-4 w-4' />}
            </>
          )}
        </div>
      );
    }
  },
  {
    header: 'Expires',
    accessorKey: 'expires_at',
    cell: (info: any) => (info.getValue() ? new Date(info.getValue()).toLocaleString() : 'Never')
  },
  {
    header: 'Revoke',
    accessorKey: 'Revoke',
    cell: (info: any) => (
      <Button variant='ghost' size='sm' onClick={() => handleRevokeClick(info.row.original)}>
        <Trash2 color='#ff0000' />
      </Button>
    )
  }
];

export default function ApiManagementPage() {
  const [page, setPage] = useState(1);
  const [per_page, setPerpage] = useState(10);
  const [selectedToken, setSelectedToken] = useState<{ id: string; name: string } | null>(null);
  const {
    data: apiResponse,
    isLoading,
    refetch
  } = useApiListFetch({
    page,
    per_page
  });
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: ''
    }
  });
  const apiTokensResponse = apiResponse?.data;
  // @ts-expect-error: The following line is causing a type error which we are intentionally ignoring for now.
  const pagination = apiResponse?.pagination;
  const handleRevokeClick = (row: any) => {
    setSelectedToken({ id: row.id, name: row.name });
  };

  const destroyMutation = useRevokeTokenMutation(selectedToken?.id || '');

  const confirmRevoke = () => {
    if (!selectedToken?.id) {
      return;
    }

    destroyMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success('Token revoked successfully');
        refetch();
        setSelectedToken(null);
      },
      onError: (error) => {
        toast.error(error.message || 'Failed to revoke token');
      }
    });
  };

  const createTokenMutation = useCreateApiTokenMutation({
    onSuccess: () => {
      toast.success('API token created successfully');
      form.reset();
      refetch();
    },
    onError: (error: { message: any }) => {
      toast.error(error.message || 'Failed to create API token');
    }
  });
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    createTokenMutation.mutate({
      body: {
        name: values.name,
        expires_at: values.expires_at?.toISOString() || null,
        abilities: values.abilities.filter(ability => ability !== 'All')
      }
    });
  };

  return (
    <div className='mt-5 p-8'>
      <div className='flex gap-2 mb-5 items-end justify-between'>
        <div>
          <h4 className='text-primary'>API Tokens</h4>
          <p className='text-muted-foreground'>Central Hub for API Management</p>
        </div>
      </div>

      <Card className='mb-8'>
        <CardHeader>
          <CardTitle>Create New API Token</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-3'>
              <div className='grid grid-cols-3 md:grid-cols-2 sm:grid-cols-1 gap-6'>
                {/* Token Name Field */}
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Token Name</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g. Production Token' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Expiration Date Picker Field */}
                <FormField
                  control={form.control}
                  name='expires_at'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiration Date</FormLabel>
                      <div className='relative'>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant='outline'
                                className={cn(
                                  'w-full pl-3 pr-8 text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'PPP')
                                ) : (
                                  <span className='mr-auto'>Select a date (optional)</span>
                                )}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className='w-auto p-0' align='start'>
                            <Calendar
                              mode='single'
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={date => date < new Date()}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        {field.value ? (
                          <Button
                            variant='ghost'
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              field.onChange(undefined);
                            }}
                            className='absolute right-2 top-1/2 -translate-y-1/2 z-10 text-muted-foreground hover:text-red-500'
                          >
                            <X className='h-4 w-4' />
                            <span className='sr-only'>Clear date</span>
                          </Button>
                        ) : (
                          <CalendarIcon className='absolute right-2 top-1/2 -translate-y-1/2  h-4 w-4 opacity-50' />
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Abilities Toggle Group Field */}
                <FormField
                  control={form.control}
                  name='abilities'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Abilities</FormLabel>
                      <ToggleGroup
                        type='multiple'
                        value={field.value || []}
                        onValueChange={(value) => {
                          const currentValue = field.value || [];
                          const allValues = ['read', 'write', 'delete', 'All'];

                          if (value.includes('All') && !currentValue.includes('All')) {
                            field.onChange(allValues);
                          } else if (!value.includes('All') && currentValue.includes('All')) {
                            field.onChange([]);
                          } else if (
                            value.includes('read')
                            && value.includes('write')
                            && value.includes('delete')
                            && value.length === 3
                          ) {
                            field.onChange([...value, 'All']);
                          } else if (currentValue.includes('All') && value.length < currentValue.length) {
                            const newValue = value.filter(v => v !== 'All');
                            field.onChange(newValue);
                          } else {
                            field.onChange(value);
                          }
                        }}
                        className='flex justify-start'
                      >
                        <ToggleGroupItem
                          value='All'
                          aria-label='Toggle all'
                          className={field.value?.includes('All') ? 'bg-accent' : ''}
                        >
                          <Star />
                          <span>Toggle all</span>
                        </ToggleGroupItem>
                        <ToggleGroupItem
                          value='read'
                          aria-label='Toggle read'
                          className={field.value?.includes('read') ? 'bg-accent' : ''}
                        >
                          <Eye />
                          <span>Read</span>
                        </ToggleGroupItem>
                        <ToggleGroupItem
                          value='write'
                          aria-label='Toggle write'
                          className={field.value?.includes('write') ? 'bg-accent' : ''}
                        >
                          <Pencil />
                          <span>Write</span>
                        </ToggleGroupItem>
                        <ToggleGroupItem
                          value='delete'
                          aria-label='Toggle delete'
                          className={field.value?.includes('delete') ? 'bg-accent' : ''}
                        >
                          <Trash2 />
                          <span>Delete</span>
                        </ToggleGroupItem>
                      </ToggleGroup>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='flex justify-end'>
                <Button type='submit'>Create Token</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <DataTable
        pageNumber={page}
        setPageNumber={setPage}
        perpage={per_page}
        setPerpage={setPerpage}
        columns={columns(handleRevokeClick)}
        dataTable={{
          // @ts-expect-error: The following line is causing a type error which we are intentionally ignoring for now.
          data: apiTokensResponse || [],
          pagination: pagination || {
            current_page: 1,
            first_page_url: '',
            from: 0,
            last_page: 1,
            last_page_url: '',
            next_page_url: null,
            per_page,
            prev_page_url: '',
            total: 0
          }
        }}
        isLoading={isLoading}
        title='API Tokens'
      />

      <Dialog open={!!selectedToken} onOpenChange={open => !open && setSelectedToken(null)}>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Confirm Revoke</DialogTitle>
          </DialogHeader>
          <div className='py-4'>
            Are you sure you want to revoke token
            {' '}
            <span className='font-semibold'>
              "
              {selectedToken?.name}
              "
            </span>
            ? This
            action cannot be undone.
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={() => setSelectedToken(null)}>
              Cancel
            </Button>
            <Button variant='destructive' onClick={confirmRevoke}>
              Revoke Token
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
