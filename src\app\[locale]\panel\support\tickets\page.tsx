'use client';
import React, { useState } from 'react';
import { useSearchQuery } from '@/hooks/search-query';
import { useUserTicketsFetch } from '@/services/api/user/support/use-fetch-tickets';
import CreateTicketForm from './create-ticket-form';
import EmptyState from './empty';
import MessageBox from './message-box';
import TicketList from './tickets-list';

const Tickets = () => {
  const [createNewTicket, setCreateNewTicket] = React.useState(false);
  const [selectedTicket, setSelectedTicket] = useState<{ id: string; title: string }>();
  const [filterValue, setFilterValue] = React.useState<string>('');

  const search = useSearchQuery(filterValue, 500);
  const handleTicketClick = (ticketId: string, title: string) => {
    setSelectedTicket({ id: ticketId, title });
    setCreateNewTicket(false);
  };
  const closeAll = () => {
    // setCreateNewTicket(false);
    setSelectedTicket({ id: '', title: '' });
  };

  const [per_page, setPerPage] = useState(15);
  const { data: tickets, isLoading, error, refetch } = useUserTicketsFetch({ search, per_page }, { enabled: true });

  return (
    <div className='flex flex-col lg:flex-row ms-lg-7 ms-xl-10 gap-5 '>
      {/* <main className='hidden sm:block rounded-lg sm:basis-1/4 h-svh'>
        <header className='flex items-center justify-between border-b border-black/5 px-4 py-4 sm:px-6 sm:py-6 lg:px-8'>
          <h1 className='text-base font-semibold leading-7 text-white'>Support Tickets</h1>
          <div>
            <Button variant='outline' onClick={() => setCreateNewTicket(true)}>
              New Ticket
            </Button>
          </div>
        </header> */}

      <TicketList
        tickets={tickets?.data ?? []}
        isLoading={isLoading}
        error={error}
        refetch={refetch}
        setCreateNewTicket={setCreateNewTicket}
        onTicketClick={handleTicketClick}
        filterValue={filterValue}
        setFilterValue={setFilterValue}
        setPerPage={setPerPage}
        accessNextPage={tickets?.pagination?.next_page_url}
      />
      {!createNewTicket && !selectedTicket?.id ? (
        <EmptyState setCreateNewTicket={setCreateNewTicket} />
      ) : createNewTicket ? (
        <CreateTicketForm setCreateNewTicket={setCreateNewTicket} refetch={refetch} />
      ) : selectedTicket?.id ? (
        <MessageBox
          refetchList={refetch}
          setSelectedTickets={setSelectedTicket}
          selectedTicket={selectedTicket}
          closeAll={closeAll}
        />
      ) : null}
    </div>
  );
};

export default Tickets;
