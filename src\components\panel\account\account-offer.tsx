'use client';

import { BadgePercent } from 'lucide-react';
import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useAccountOffers } from '@/services/api/user/account/use-account-offers';

export function AccountOffer() {
  const { data, isLoading } = useAccountOffers();

  if (isLoading || !data?.welcome_days) {
    return null;
  }

  const welcomeDays = data.welcome_days;

  return (
    <div className='w-full'>
      <Card className='py-0 px-0'>
        <div
          className='flex items-center flex-wrap md:flex-nowrap justify-between grow gap-5 py-4  px-4 rtl:bg-[center_left_-8rem] bg-[center_right_-8rem] bg-no-repeat bg-[length:700px]'
          style={{
            backgroundImage: `url('https://v2.tradevps.net/assets/media/images/2600x1200/bg-14.png')`
          }}
        >
          <div className='flex items-center gap-4'>
            <div className='rounded-md bg-primary/10 p-2'>
              <BadgePercent className='size-5 text-primary' />
            </div>

            <div className='flex flex-col gap-2'>
              <div className='flex items-center flex-wrap sm:flex-nowrap gap-1'>
                <Link
                  className='text-base font-medium text-gray-900 hover:text-blue-700'
                  href={PAGE_ROUTES.PANEL_BILLING}
                >
                  Step Up with TradeVPS – Save 20%
                </Link>
                <span className='inline-flex items-center rounded-sm bg-red-50 px-1 py-0.5 text-[10px] font-medium text-red-700 ring-1 ring-red-600/10 ring-inset'>
                  Offer expires in
                  {' '}
                  {welcomeDays}
                  {' '}
                  days
                </span>
              </div>
              <div className='text-sm text-gray-700 '>
                As a new TradeVPS member, you have
                {' '}
                <strong>
                  {welcomeDays}
                  {' '}
                  days
                </strong>
                {' '}
                to upgrade to one of our paid
                plans with an
                {' '}
                <strong>exclusive 20% discount</strong>
                .
                <br />
                Don't miss this chance to supercharge your trading experience at the best value!
              </div>
            </div>
          </div>
          <div className='flex items-center gap-1.5 shrink-0'>
            <Link className={buttonVariants({ variant: 'ghost' })} href={PAGE_ROUTES.PANEL_SUPPORT}>
              Get in Touch
            </Link>
            <Link className={buttonVariants({ variant: 'secondary' })} href={PAGE_ROUTES.PANEL_BILLING}>
              View Plans
            </Link>
          </div>
        </div>
      </Card>
    </div>
  );
}
