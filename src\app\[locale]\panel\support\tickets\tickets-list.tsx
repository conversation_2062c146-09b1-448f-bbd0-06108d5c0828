import { formatDistanceToNow } from 'date-fns';
import { Search } from 'lucide-react';
import Image from 'next/image';
import ErrorImage from 'public/img/illustrations/23.svg';
import NoDataImage from 'public/img/illustrations/27.svg';
import * as React from 'react';
import { Badge } from '@/components/badge';
import { Skeleton } from '@/components/loading/skeleton';
import { DepartmentTypeBadge, ticketStatusMap } from '@/components/marketing/ticket/department-list';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useTicketsDepartmentFetch, useTicketsStatusFetch } from '@/services/api/user/support/use-fetch-tickets';

type ITicket = {
  id: string;
  title: string;
  department: number;
  status: number;
  created_at: string;
  updated_at: string;
  user: {
    avatar: string | undefined;
    id: string;
    name: string;
    email: string;
  };
};
type TicketListProps = {
  tickets: ITicket[];
  isLoading: boolean;
  error?: any;
  refetch: () => void;
  onTicketClick: (ticketId: string, title: string) => void;
  setCreateNewTicket: (value: boolean) => void;
  filterValue: string;
  setFilterValue: React.Dispatch<React.SetStateAction<string>>;
  setPerPage: React.Dispatch<React.SetStateAction<number>>;
  accessNextPage: boolean;
};

const TicketList = ({
  tickets,
  isLoading,
  error,
  setCreateNewTicket,
  onTicketClick,
  filterValue,
  setFilterValue,
  setPerPage,
  accessNextPage
}: TicketListProps) => {
  const ticketStatuses = useTicketsStatusFetch();
  const department = useTicketsDepartmentFetch();
  const [fetchTickets, setFetchTickets] = React.useState<ITicket[]>([]);
  const contentRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current) {
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isAtBottom && !isLoading && accessNextPage) {
        setPerPage(prev => prev + 15);
      }
    };

    const el = contentRef.current;
    if (el) {
      el.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (el) {
        el.removeEventListener('scroll', handleScroll);
      }
    };
  }, [isLoading, accessNextPage, setPerPage]);
  React.useEffect(() => {
    if (tickets?.length) {
      setFetchTickets(tickets);
    }
  }, [tickets, tickets.length]);

  return (
    <div className='flex-column lg:flex-row-auto w-full lg:w-[400px] xl:w-[550px] mb-10 lg:mb-0'>
      <Card className='w-full h-auto  max-h-[750px] overflow-hidden flex flex-col'>
        <CardHeader className='py-1 sticky top-0 z-10'>
          <div className='flex justify-between items-center gap-3'>
            {/* Search Bar */}
            <div className='relative flex items-center bg-background/80 backdrop-blur-none border border-primary/10 rounded-md pl-4 pr-2 h-10 flex-1'>
              <Search className='h-4 w-4 text-muted-foreground/60' />
              <Input
                placeholder='Search Terminals...'
                className='border-0 bg-transparent px-3 h-9 placeholder:text-muted-foreground'
                style={{
                  WebkitAppearance: 'none',
                  MozAppearance: 'none',
                  appearance: 'none',
                  outline: 'none',
                  boxShadow: 'none'
                }}
                value={filterValue}
                onChange={e => setFilterValue(e.target.value)}
              />
              {filterValue?.trim() && (
                <button
                  type='button'
                  onClick={() => setFilterValue('')}
                  className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-primary hover:scale-105 cursor-pointer'
                >
                  x
                </button>
              )}
            </div>

            {/* New Ticket Button */}
            <Button size='sm' variant='table' className='w-fit' type='button' onClick={() => setCreateNewTicket(true)}>
              New Ticket
            </Button>
          </div>
        </CardHeader>

        <CardContent ref={contentRef} className='overflow-y-auto mt-3 px-4'>
          <div className='space-y-5'>
            {isLoading && fetchTickets?.length === 0 ? (
              Array.from({ length: 3 }).map((_, i) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={i} className='mt-5'>
                  <div className='flex gap-3 items-center'>
                    <Skeleton height={30} width={30} className='rounded-full' />
                    <div className='w-full'>
                      <div className='w-1/2 mb-3'>
                        <Skeleton height={10} />
                      </div>
                      <div className='w-full'>
                        <Skeleton height={10} />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : error ? (
              <div className='p-4 flex flex-col h-[460px] items-center justify-center py-5'>
                <Image src={ErrorImage} width={250} height={250} alt='' />
                <p className='text-muted-foreground'>Error Receiving Tickets ...</p>
              </div>
            ) : fetchTickets?.length === 0 && isLoading === false ? (
              <div className='p-4 flex flex-col h-[460px] items-center justify-center py-5'>
                <Image src={NoDataImage} width={250} height={250} alt='' />
                <p className='text-muted-foreground'>No Ticket Found ...</p>
              </div>
            ) : (
              fetchTickets?.map((ticket: ITicket) => {
                const statusMapping = ticketStatusMap(ticketStatuses?.data || []);
                const { label, variant } = statusMapping[ticket.status] ?? { label: 'Unknown', variant: 'default' };

                return (
                  <div
                    key={ticket.id}
                    role='button'
                    tabIndex={0}
                    onClick={() => onTicketClick(ticket.id, ticket.title)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        onTicketClick(ticket.id, ticket.title);
                      }
                    }}
                    className='flex justify-between items-center cursor-pointer p-2 rounded-md hover:bg-blue-50 dark:hover:bg-gray-800 transition'
                  >
                    <div className='flex items-center gap-2'>
                      <Avatar className='w-[40px] h-[40px]'>
                        <AvatarImage src={ticket.user.avatar} alt={ticket.user.name} />
                        <AvatarFallback className='bg-gray-100/80 text-secondary'>
                          {ticket.user.name?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>

                      <div className='flex flex-col gap-1'>
                        <span className='text-primary font-semibold'>{ticket.title}</span>
                        <Badge value={label} variant={variant} />
                      </div>
                    </div>

                    <div className='text-end'>
                      <div className='text-xs text-muted-foreground'>
                        {formatDistanceToNow(new Date(ticket.created_at), { addSuffix: true })}
                      </div>
                      <div>{DepartmentTypeBadge(ticket.department, department?.data || [])}</div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
          {isLoading && fetchTickets.length > 0
            ? Array.from({ length: 3 }).map((_, i) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={i} className='mt-5 px-4'>
                  <div className='flex gap-3 items-center'>
                    <Skeleton height={30} width={30} className='rounded-full' />
                    <div className='w-full'>
                      <div className='w-1/2 mb-3'>
                        <Skeleton height={10} />
                      </div>
                      <div className='w-full'>
                        <Skeleton height={10} />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            : null}
        </CardContent>
      </Card>
    </div>
  );
};

export default TicketList;
