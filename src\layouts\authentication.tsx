'use client';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import PulseLoader from 'react-spinners/PulseLoader';
import { getFromStorage } from '@/utils/storage';

export function AuthenticationLayout({ children }: React.PropsWithChildren) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    const tokenStore = getFromStorage('token');

    if (pathname) {
      if (!tokenStore?.state?.token) {
        if (pathname.startsWith('/panel')) {
          router.replace('/auth/sign-in');
          setIsLoading(false);
        } else {
          setIsMounted(true);
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
        setIsMounted(true);
      }
    }
  }, [pathname, router]);

  // وقتی کامپوننت mount نشده است، چیزی رندر نمی‌شود
  // if (!isMounted) return null;

  if (isLoading || !isMounted) {
    return (
      <div className='flex flex-col items-center justify-center h-screen'>
        <PulseLoader
          color='blue'
          loading={isLoading || !isMounted}
          // cssOverride={override}
          size={15}
          aria-label='Loading Spinner'
          data-testid='loader'
        />
      </div>
    );
  }

  return <>{children}</>;
}
