import React from 'react';
import LoadingButton from '../loading-button';

type IModalProps = {
  open: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  action: () => void;
  isLoading?: boolean;
  title: string;
  desc: string;
};
const ConfirmModal = ({ open, setIsOpen, action, isLoading, title, desc }: IModalProps) => {
  if (!open) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-gray-500 bg-opacity-75 z-10 flex items-center justify-center'>
      <div className='relative transform overflow-hidden rounded-lg bg-gray-900 text-start shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg'>
        <div className='bg-gray-900 px-4 pb-4 pt-5 sm:p-6 sm:pb-4'>
          <div className='sm:flex sm:items-start'>
            <div className='mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10'>
              <svg
                className='h-6 w-6 text-red-600'
                fill='none'
                viewBox='0 0 24 24'
                strokeWidth='1.5'
                stroke='currentColor'
                aria-hidden='true'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  d='M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z'
                />
              </svg>
            </div>
            <div className='mt-3 text-center sm:ms-4 sm:mt-0 sm:text-start'>
              <h3 className='text-base font-semibold leading-6 text-white' id='modal-title'>
                {title}
              </h3>
              <div className='mt-2'>
                <p className='text-sm text-gray-200'>{desc}</p>
              </div>
            </div>
          </div>
        </div>
        <div className='bg-gray-900 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6'>
          <button
            type='button'
            onClick={action}
            className='inline-flex w-full justify-center rounded-full bg-red-600 px-3 py-1 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto'
          >
            <span className='ml-1'>DESTROY</span>
            {isLoading && <LoadingButton />}
          </button>
          <button
            type='button'
            onClick={() => setIsOpen(false)}
            className='mt-3 me-2 inline-flex w-full justify-center rounded-full bg-white px-3 py-1 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto'
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmModal;
