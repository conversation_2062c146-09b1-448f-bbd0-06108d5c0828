# FIXME: Configure environment variables for your project
# FIXME: Configure environment variables for your project

# If you need to build a SaaS application with Stripe subscription payment with checkout page, customer portal, webhook, etc.
# You can check out the Next.js Boilerplate SaaS: https://nextjs-boilerplate.com/pro-saas-starter-kit

# Sentry
# Disable Sentry warning with TurboPack
SENTRY_SUPPRESS_TURBOPACK_WARNING=1

######## [BEGIN] SENSITIVE DATA ######## For security reason, don't update the following variables (secret key) directly in this file.
######## Please create a new file named `.env.local`, all environment files ending with `.local` won't be tracked by Git.
######## After creating the file, you can add the following variables.
# Arcjet security
# Get your key from https://launch.arcjet.com/Q6eLbRE
# ARCJET_KEY=

######## [END] SENSITIVE DATA
NEXT_PUBLIC_SERVER_URL=https://api.hobs.ai/v1

NEXT_PUBLIC_GTM_ID=