'use client';

import type { AxiosError } from 'axios';
import { zodResolver } from '@hookform/resolvers/zod';

import { Info } from 'lucide-react';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import { Button } from '@/components/ui/button';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useResetPasswordAccountMutate } from '@/services/api/auth/use-reset-password-mutation';

const formSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Z]/, 'Must include one uppercase letter')
      .regex(/[a-z]/, 'Must include one lowercase letter')
      .regex(/\d/, 'Must include one number'),
    confirmPassword: z.string().min(6, 'Please confirm your new password')
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Passwords don\'t match',
    path: ['confirmPassword']
  });

export default function SecurityPage() {
  const router = useRouter();
  const [errors, setErrors] = useState<string[]>([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [formValues, setFormValues] = useState<z.infer<typeof formSchema> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  const resetPasswordMutation = useResetPasswordAccountMutate({
    onSuccess: () => {
      toast.success('Password reset successfully!');
      setConfirmDialogOpen(false);
      setIsLoading(false);
      router.push(PAGE_ROUTES.LOGIN);
      localStorage.removeItem('token');
    },
    onError: (error: AxiosError<{ message?: string; errors?: Record<string, string[]> }>) => {
      if (error.response?.status === 422) {
        const errData = error.response?.data.errors;
        if (errData) {
          const allMessages = Object.values(errData).flat();
          setErrors(allMessages);
        } else {
          setErrors([error.response.data.message || 'Validation error']);
        }
      } else {
        const fallback = 'Something went wrong';
        setErrors([fallback]);
      }
      setConfirmDialogOpen(false);
      setIsLoading(false);
    }
  });

  const handleFormSubmit = (values: z.infer<typeof formSchema>) => {
    setFormValues(values);
    setConfirmDialogOpen(true);
  };

  const confirmReset = () => {
    if (!formValues) {
      return;
    }
    try {
      setErrors([]);
      setIsLoading(true);
      resetPasswordMutation.mutate({
        body: {
          current_password: formValues.currentPassword,
          new_password: formValues.newPassword,
          new_password_confirmation: formValues.confirmPassword
        }
      });
    } catch (error) {
      console.error('Password reset error:', error);
    }
  };

  return (
    <div>
      <Card className='max-w-2xl mx-auto p-6 '>
        <CardHeader className='my-2'>
          <CardTitle>Change Password</CardTitle>
        </CardHeader>
        <CardContent className='space-y-8'>
          <div className='mb-6 border border-amber-500 bg-amber-50 text-amber-800 dark:border-amber-500/30 dark:bg-amber-500/5 dark:text-amber-200/80 p-4 rounded-md'>
            <div className='flex items-start gap-2'>
              <div>
                <div className='flex items-center gap-2'>
                  <Info className='h-4 w-4' />
                  <span className='font-medium'>Warning</span>
                </div>
                <p className='text-sm mt-1'>
                  Changing your password will automatically revoke all existing API tokens. You will need to generate
                  new tokens using the
                  {' '}
                  <span className='text-blue-600 font-semibold cursor-pointer hover:underline'>login endpoint</span>
                  {' '}
                  or
                  {' '}
                  <span className='text-yellow-700 font-semibold'>create new tokens</span>
                  {' '}
                  after changing your password.
                </p>
              </div>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleFormSubmit)} className='space-y-4'>
              <FormField
                control={form.control}
                name='currentPassword'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <FormControl>
                      <Input
                        type='password'
                        placeholder='Your current password'
                        disabled={confirmDialogOpen}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='newPassword'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input type='password' placeholder='New password' disabled={confirmDialogOpen} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='confirmPassword'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input
                        type='password'
                        placeholder='Confirm new password'
                        disabled={confirmDialogOpen}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className='pt-4 flex flex-col justify-end space-y-2'>
                {errors.length > 0 && (
                  <ul className='mt-2 space-y-1 text-sm text-red-600'>
                    {errors.map((err: string, i: number) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <li key={i}>
                        •
                        {err}
                      </li>
                    ))}
                  </ul>
                )}
                <Button type='submit' disabled={confirmDialogOpen}>
                  {confirmDialogOpen ? 'Resetting...' : 'Reset Password'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <DeleteConfirmDialog
        title='Change paasword'
        open={confirmDialogOpen}
        setOpen={setConfirmDialogOpen}
        confirmSubject=''
        haveInput={false}
        onDeleteFunction={confirmReset}
        isLoading={isLoading}
      />
    </div>
  );
}
