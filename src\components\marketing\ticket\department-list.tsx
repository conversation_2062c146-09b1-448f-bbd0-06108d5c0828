import type { BadgeVariants } from '@/components/badge/badge.type';
import { Badge } from '@/components/ui/badge';

type Department = {
  key: number;
  label: string;
};

export const DepartmentTypeBadge = (department: number, departmentList: Department[]) => {
  const departmentInfo = departmentList?.find(dept => dept.key === department);
  const departmentName = departmentInfo ? departmentInfo.label : 'Unknown';

  const getBadgeStyles = (key: number) => {
    switch (key) {
      case 0:
      case 1:
      case 2:
      case 3:
        return 'text-[11px] text-sky-400 font-bold';
      case 4:
        return 'text-[11px] text-amber-400 font-bold';
      case 5:
        return 'text-[11px] text-neutral-400 font-bold';
      default:
        return 'text-[11px] text-gray-500 font-bold';
    }
  };

  return <span className={getBadgeStyles(department)}>{departmentName}</span>;
};

export const ticketStatusMap = (
  statusList: string[]
): Record<number, { label: string | undefined; variant: BadgeVariants }> => ({
  0: { label: statusList[0], variant: 'warning_light' },
  1: { label: statusList[1], variant: 'danger_light' },
  2: { label: statusList[2], variant: 'success_light' },
  3: { label: statusList[3], variant: 'sky_light' },
  4: { label: statusList[4], variant: 'warning_light' },
  5: { label: statusList[5], variant: 'success_light' },
  6: { label: statusList[6], variant: 'warning_light' }
});
export const TicketStatusBadge = (status: number, statusList: string[]) => {
  const statusName = statusList[status];
  switch (status) {
    case 0: // IN_QUEUE
      return (
        <Badge variant='warning_light'>
          <span className='size-1 rounded-full bg-amber-400 me-1' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 1:
      return (
        <Badge variant='danger_light'>
          <span className='size-1 rounded-full bg-red-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 2:
      return (
        <Badge variant='success_light'>
          <span className='size-1 rounded-full bg-emerald-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 3:
      return (
        <Badge variant='sky_light'>
          <span className='size-1 rounded-full bg-blue-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 4:
      return (
        <Badge variant='warning_light'>
          <span className='size-1 rounded-full bg-amber-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 5:
      return (
        <Badge variant='success_light'>
          <span className='size-1 rounded-full bg-emerald-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    case 6:
      return (
        <Badge variant='warning_light'>
          <span className='size-1 rounded-full bg-amber-400 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
    default:
      return (
        <Badge variant='default'>
          <span className='size-1 rounded-full bg-gray-500 me-1.5' />
          <span className='text-[9px] font-bold'>{statusName}</span>
        </Badge>
      );
  }
};
export const departmentTypes = [
  {
    key: '0',
    value: 'Account',
    type: 'info',
    description: 'Account-related issues including login, profile, and security settings.',
    icon: <p className='size-2.5 rounded-full bg-sky-400' />
  },
  {
    key: '1',
    value: 'Terminals',
    type: 'info',
    description: 'Issues related to trading terminals and their configuration.',
    icon: <p className='size-2.5 rounded-full bg-sky-400' />
  },
  {
    key: '2',
    value: 'Trading Server',
    type: 'info',
    description: 'Trading server connectivity and performance issues.',
    icon: <p className='size-2.5 rounded-full bg-sky-400' />
  },
  {
    key: '3',
    value: 'Windows Server',
    type: 'info',
    description: 'Windows server management and configuration support.',
    icon: <p className='size-2.5 rounded-full bg-sky-400' />
  },
  {
    key: '4',
    value: 'Billing',
    type: 'warning',
    description: 'Billing, payments, and subscription-related inquiries.',
    icon: <p className='size-2.5 rounded-full bg-amber-400' />
  },
  {
    key: '5',
    value: 'Other',
    type: 'default',
    description: 'General inquiries or issues not covered by other departments.',
    icon: <p className='size-2.5 rounded-full bg-neutral-400' />
  }
];
