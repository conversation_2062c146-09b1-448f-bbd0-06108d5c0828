import type { Invoice } from '@/types/invoice';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { API_ROUTES } from '@/configs/api-routes';
import { SERVER } from '@/libs/Axios';

type InvoicesResponse = {
  data: Invoice[];
  pagination: {
    current_page: number;
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: null | string;
    per_page: number;
    prev_page_url: string;
    total: number;
  };
};

export const useInvoicesList = () => {
  return useQuery<InvoicesResponse>({
    queryKey: [API_QUERY_KEY.USER_INVOICES],
    queryFn: async () => {
      const response = await SERVER.get<InvoicesResponse>(API_ROUTES.USER_INVOICES);
      return response.data;
    }
  });
};
