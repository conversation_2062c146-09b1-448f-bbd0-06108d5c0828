import { getTranslations, setRequestLocale } from 'next-intl/server';

type IAboutProps = {
  params: Promise<{ slug: string; locale: string }>;
};

export async function generateMetadata(props: IAboutProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Pricing'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default async function About(props: IAboutProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  // const t = await getTranslations({
  // locale,
  // namespace: 'Pricing',
  // });

  return (
    <>
      <h1>ASAP</h1>
    </>
  );
}
