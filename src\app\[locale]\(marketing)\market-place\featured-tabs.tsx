import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import openAi from '@/public/img/platforms/openai-chatgpt-logo-icon-free-png.webp';
import RectangleCard from './cards/Rectangle-card';

export default function FeaturedTabs() {
  return (
    <Tabs defaultValue='account' className=' mt-10'>
      <TabsList className='grid w-full md:w-1/3 grid-cols-2 '>
        <TabsTrigger className='cursor-pointer' value='account'>
          Recommended
        </TabsTrigger>
        <TabsTrigger className='cursor-pointer' value='password'>
          Recently added
        </TabsTrigger>
      </TabsList>
      <TabsContent value='account'>
        <div className='grid mt-10 md:grid-cols-2 gap-3'>
          <RectangleCard
            title='Codacy'
            description='Codacy helps to build effortless code quality and security for developers'
            type='App'
            image={openAi}
          />
          <RectangleCard
            title='Codacy'
            description='Codacy helps to build effortless code quality and security for developers'
            type='App'
            image={openAi}
          />
        </div>
      </TabsContent>
      <TabsContent value='password'>
        <div className='grid mt-10 md:grid-cols-2 gap-3'>
          <RectangleCard
            title='Codacy'
            description='Codacy helps to build effortless code quality and security for developers'
            type='App'
            image={openAi}
          />
          <RectangleCard
            title='Codacy'
            description='Codacy helps to build effortless code quality and security for developers'
            type='App'
            image={openAi}
          />
        </div>
      </TabsContent>
    </Tabs>
  );
}
