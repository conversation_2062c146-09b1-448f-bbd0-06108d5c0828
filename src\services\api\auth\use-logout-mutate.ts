import type { APIError, ILogoutResponse } from '@tradevpsnet/client';
import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useLogoutMutate = (
  options?: Pick<IUseMutationFactoryProps<any>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory({
    url: API_ROUTES.AUTH_LOGOUT,
    method: 'POST',
    ...options
  });
};

export const useLogoutClientMutate = (options?: {
  onSuccess?: (data: ILogoutResponse) => void;
  onError?: (error: APIError) => void;
}) => {
  const client = new Client('', process.env.NEXT_PUBLIC_SERVER_URL);

  return useMutation<ILogoutResponse, APIError>({
    mutationFn: () => client.auth.logout(),
    ...options
  });
};
