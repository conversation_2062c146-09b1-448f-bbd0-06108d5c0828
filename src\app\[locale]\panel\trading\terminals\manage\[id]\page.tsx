'use client';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

const AccountDetails = () => {
  const params = useParams();
  const id = params.id as string;

  const [panelType, setPanelType] = useState<number>(1);

  return (
    <div>
      <div className='mt-5 flex justify-center'>
        <fieldset className='grid grid-cols-2 gap-x-1 rounded-full bg-white/5 p-1 text-center text-xs font-semibold leading-5 text-white'>
          <Button
            type='button'
            onClick={() => setPanelType(1)}
            className={`${panelType === 1 ? 'bg-indigo-500' : ''} cursor-pointer rounded-full px-2.5 py-1 h-auto`}
          >
            مشخصات
          </Button>
          <Button
            type='button'
            onClick={() => setPanelType(3)}
            className={`${panelType === 3 ? 'bg-indigo-500' : ''} cursor-pointer rounded-full px-2.5 py-1 h-auto`}
          >
            تاریخچه معاملات
          </Button>
        </fieldset>
      </div>
      {id}
      {/* {panelType === 1 && <AccountInfo accountData={accountData} isLoading={isLoading} />} */}
      {/* {panelType === 3 && id ? <TradingHistory params={{ id }} /> : <p>Loading...</p>} */}
    </div>
  );
};

export default AccountDetails;
