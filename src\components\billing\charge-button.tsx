'use client';

import { CreditCard, Plus, Wallet } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export function ChargeButton() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className='gap-1.5 bg-primary/90 hover:bg-primary' size='sm'>
          <Plus className='size-4' />
          Top Up Balance
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[200px]'>
        <DropdownMenuItem asChild>
          <Link href='/panel/account/billing/charge/card' className='flex items-center'>
            <CreditCard className='mr-2 size-4' />
            Credit Card
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href='/panel/account/billing/charge/crypto' className='flex items-center'>
            <Wallet className='mr-2 size-4' />
            Cryptocurrency
          </Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
