import React from 'react';

export const BackgroundGrid = () => {
  return (
    <div className="absolute inset-0 h-full w-full overflow-hidden">
      <div className="absolute inset-0 h-full w-full pointer-events-none z-0">
        <div className="absolute inset-0 h-full w-full bg-white dark:bg-black pointer-events-none [mask-image:radial-gradient(ellipse_at_center,transparent,white)]"></div>
        
        {/* Simplified grid pattern */}
        <div className="absolute inset-0 opacity-20">
          <svg
            className="absolute inset-0 h-full w-full"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <pattern
                id="grid"
                width="60"
                height="60"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 60 0 L 0 0 0 60"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1"
                  className="text-neutral-200 dark:text-neutral-800"
                />
                <circle
                  cx="0"
                  cy="0"
                  r="2"
                  fill="currentColor"
                  className="text-neutral-300 dark:text-neutral-700"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>
      </div>
    </div>
  );
};
