import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useAddUserTicketMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.ADD_USER_TICKET,
    method: 'POST',
    isMultipart: true,
    ...options
  });
};
