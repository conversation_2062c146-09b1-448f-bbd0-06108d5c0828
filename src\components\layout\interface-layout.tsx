'use client';

import { useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useSettings } from '@/stores/settings';

type InterfaceLayoutProps = {
  children: React.ReactNode;
};

export function InterfaceLayout({ children }: InterfaceLayoutProps) {
  const { compactMode, reducedMotion, fontSize } = useSettings();

  useEffect(() => {
    if (reducedMotion) {
      document.documentElement.style.setProperty('--reduce-motion', 'reduce');
    } else {
      document.documentElement.style.removeProperty('--reduce-motion');
    }
  }, [reducedMotion]);

  return (
    <div className={cn('layout-wrapper', !compactMode && 'expanded-mode', `text-size-${fontSize}`)}>
      <main>{children}</main>
    </div>
  );
}
