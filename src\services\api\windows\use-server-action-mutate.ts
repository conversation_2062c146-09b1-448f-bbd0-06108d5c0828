import type { APIError, IServerActionParams, IServerActionResponse } from '@tradevpsnet/client';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';
const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL!);

export const useWindowsServerAction = (
  id: string,
  options?: {
    onSuccess?: (data: IServerActionResponse) => void;
    onError?: (error: APIError) => void;
  }
) => {
  return useMutation<IServerActionResponse, APIError, IServerActionParams>({
    mutationFn: (params: IServerActionParams) => client.windows.server_action(id, params),
    ...options
  });
};
