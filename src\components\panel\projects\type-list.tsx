import type { BadgeVariants } from '@/components/badge/badge.type';
import { Badge } from '@/components/ui/badge';

export enum ProjectType {
  WEB = 0,
  MOBILE = 1,
  API = 2,
  GENERAL = 4,
  SERVICE = 5,
  LEARNING = 6,
  OTHER = 9
}

export const projectTypeMap: Record<
  number,
  {
    label: string;
    variant: BadgeVariants;
  }
> = {
  [ProjectType.WEB]: { label: 'Web', variant: 'emerald_light' },
  [ProjectType.MOBILE]: { label: 'Mobile', variant: 'sky_light' },
  [ProjectType.API]: { label: 'Api', variant: 'stone_light' },
  [ProjectType.GENERAL]: { label: 'General', variant: 'neutral_light' },
  [ProjectType.SERVICE]: { label: 'Service', variant: 'rose_light' },
  [ProjectType.LEARNING]: { label: 'Learning', variant: 'amber_light' },
  [ProjectType.OTHER]: { label: 'Other', variant: 'emerald_light' }
};

export const ProjectsTypeBadge = (status: number) => {
  switch (status) {
    case 0:
      return (
        <Badge variant='emerald_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-emerald-500 me-1.5'></span>
          Web
        </Badge>
      );
    case 1:
      return (
        <Badge variant='sky_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-sky-500 me-1.5'></span>
          Mobile
        </Badge>
      );
    case 2:
      return (
        <Badge variant='stone_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-stone-500 me-1.5'></span>
          Api
        </Badge>
      );
    case 4:
      return (
        <Badge variant='neutral_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-neutral-500 me-1.5'></span>
          General
        </Badge>
      );
    case 5:
      return (
        <Badge variant='rose_light' className='w-[100px] h-[23px] '>
          <span className='size-2 rounded-full bg-rose-500 me-1.5'></span>
          Service
        </Badge>
      );
    case 6:
      return (
        <Badge variant='amber_light' className='w-[100px] h-[23px]'>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-amber-400 opacity-75' />
            <span className='relative h-full w-full  inline-flex size-2 rounded-full bg-amber-500' />
          </span>
          Learning
        </Badge>
      );
    case 9:
      return (
        <Badge variant='emerald_light' className='w-[100px] h-[23px] flex items-center gap-2'>
          <span className='size-2 rounded-full bg-primay-500 me-1.5'></span>
          Other
        </Badge>
      );

    default:
      return (
        <Badge variant='default' className='h-[23px] w-[100px] '>
          <span className='size-1.5 rounded-full bg-gray-500 me-1.5'></span>
          Unknown
        </Badge>
      );
  }
};

export const typeItems = [
  {
    key: '0',
    value: 'Web',
    icon: <p className='size-2.5 rounded-full bg-emerald-500'></p>
  },

  {
    key: '1',
    value: 'Mobile',
    icon: <p className='size-2.5 rounded-full bg-sky-400 '></p>
  },
  {
    key: '2',
    value: 'Api',
    icon: <p className='size-2.5 rounded-full bg-stone-400 '></p>
  },
  {
    key: '4',
    value: 'General',
    icon: <p className='size-2.5 rounded-full bg-neutral-400'></p>
  },
  {
    key: '5',
    value: 'Service',
    icon: <p className='size-2.5 text-xs rounded-full bg-rose-400'></p>
  },
  {
    key: '6',
    value: 'Learning',
    icon: <p className='size-2.5 rounded-full bg-amber-400 '></p>
  },
  {
    key: '9',
    value: 'Other',
    icon: <p className='size-2.5 rounded-full bg-blue-600 '></p>
  }
];
