@layer utilities {
  .bg-dot-pattern {
    background-size: 16px 16px;
    background-image: radial-gradient(circle at 1px 1px, rgb(var(--foreground) / 0.2) 1px, transparent 0);
  }

  /* Enhanced button animations */
  @keyframes border-flow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-border-flow {
    animation: border-flow 3s linear infinite;
    background-size: 200% 200%;
  }

  @keyframes particle-float {
    0%,
    100% {
      transform: translateY(0) scale(1);
      opacity: 0.3;
    }
    50% {
      transform: translateY(-10px) scale(1.2);
      opacity: 0.7;
    }
  }

  .animate-float {
    animation: particle-float 3s ease-in-out infinite;
  }

  /* Border animations */
  @keyframes border-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.5;
    }
  }

  @keyframes border-rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes border-rotate-reverse {
    0% {
      transform: rotate(360deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  @keyframes dash {
    to {
      stroke-dashoffset: -250;
    }
  }

  @keyframes ping-slow {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    75%,
    100% {
      transform: scale(2.5);
      opacity: 0;
    }
  }

  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-border-pulse {
    animation: border-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-border-pulse-delayed {
    animation: border-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation-delay: 0.5s;
  }

  .animate-border-pulse-more-delayed {
    animation: border-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation-delay: 1s;
  }

  .animate-border-rotate {
    animation: border-rotate 8s linear infinite;
  }

  .animate-border-rotate-reverse {
    animation: border-rotate-reverse 8s linear infinite;
  }

  .animate-dash {
    animation: dash 8s linear infinite;
  }

  .animate-ping-slow {
    animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  }

  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }

  .delay-100 {
    animation-delay: 100ms;
  }

  .delay-200 {
    animation-delay: 200ms;
  }

  .delay-300 {
    animation-delay: 300ms;
  }

  @keyframes border-light {
    0% {
      transform: rotate(0deg) translateX(0);
    }
    100% {
      transform: rotate(360deg) translateX(0);
    }
  }

  .animate-border-light {
    animation: border-light 2s linear infinite;
    transform-origin: calc(50% + 2rem) calc(50% + 2rem);
  }

  @keyframes border-glow {
    0% {
      transform: rotate(0deg) translateX(calc(50% + 1rem));
    }
    100% {
      transform: rotate(360deg) translateX(calc(50% + 1rem));
    }
  }

  .animate-border-glow {
    animation: border-glow 3s linear infinite;
    transform-origin: center;
    opacity: 0.7;
  }
}

@keyframes float-minimal {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(-10px, -10px) rotate(3deg);
  }
}

.animate-float-minimal {
  animation: float-minimal 8s ease-in-out infinite;
}

/* Enhance existing animations */
@keyframes pulse-minimal {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-minimal {
  animation: pulse-minimal 4s ease-in-out infinite;
}

/* Add smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Enhanced hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

@layer components {
  .btn-animated {
    background: var(--primary);
    color: white;
    position: relative;
  }

  .btn-edge-light {
    position: relative;
    overflow: hidden;
  }

  .btn-edge-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .btn-edge-light:hover::before {
    left: 100%;
  }

  .btn-arrow {
    transition: transform 0.2s ease;
  }

  .btn-edge-light:hover .btn-arrow {
    transform: translateX(4px);
  }
}
