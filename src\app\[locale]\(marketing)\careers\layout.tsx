import { getTranslations } from 'next-intl/server';

type ICareersProps = {
  params: Promise<{ locale: string }>;
  children: React.ReactNode;
};

export async function generateMetadata(props: ICareersProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Careers'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default function Layout({ children }: ICareersProps) {
  return <>{children}</>;
}
