'use client';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { SkeletonCard } from '@/components/loading/skeleton/card-skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTerminalTaskFetch } from '@/services/api/user/trading/tasks';
import { useTerminalDetailFetch } from '@/services/api/user/trading/terminals';
import EmptyTab from './empty-tabs';
import NewTask from './new-task';
import Overview from './over-view';
import TasksTable from './tasks';

const TenimalDetailComponent = () => {
  const [selectedTabs, setSekectedTabs] = useState('Overview');
  const params = useParams();
  const id = params.id as string;
  const { data: terminalData, isLoading: isTerminalLoading } = useTerminalDetailFetch(id);
  const { data: taskResponse, isLoading: isTaskLoading } = useTerminalTaskFetch(id);
  const trigegerTabs = [
    { title: 'Overview', value: 'Overview' },
    { title: 'Tasks', value: 'Tasks' },
    { title: 'Invoices', value: 'Invoices' },
    { title: 'Terminal Logs', value: 'Terminal Logs' },
    { title: 'Activity', value: 'Activity' },
    { title: 'Support', value: 'Support' },
    { title: 'New Task', value: 'New Task' },
    { title: 'Settings', value: 'Settings' }
  ];

  const returnTabContent = () => {
    switch (selectedTabs) {
      case 'Overview':
        return (
          <div className='flex flex-col lg:flex-row w-full gap-8'>
            <div className='w-full lg:w-1/3'>
              {isTerminalLoading ? (
                <SkeletonCard />
              ) : (
                <Overview
                  detailInfo={{
                    name: terminalData?.name,
                    region: terminalData?.region,
                    server_name: terminalData?.server_name,
                    login: terminalData?.login,
                    status: terminalData?.status ?? undefined
                  }}
                />
              )}
            </div>
            <div className='w-full lg:w-2/3'>
              {isTaskLoading ? (
                <SkeletonCard />
              ) : (
                <TasksTable
                  taskData={
                    taskResponse?.map(task => ({
                      id: task.id,
                      type: task.type,
                      payload: task.payload,
                      result: task.result
                        ? {
                            runner_result: task.result.runner_result || null,
                            status: task.result.status
                          }
                        : null,
                      status: task.status,
                      started_at: task.started_at,
                      finished_at: task.finished_at
                    })) || []
                  }
                />
              )}
            </div>
          </div>
        );
      case 'Invoices':
      case 'Settings':
      case 'Support':
      case 'Activity':
      case 'Terminal Logs':
        return <EmptyTab />;
      case 'Tasks':
        return (
          <TasksTable
            taskData={
              taskResponse?.map(task => ({
                id: task.id,
                type: task.type,
                payload: task.payload,
                result: task.result
                  ? {
                      runner_result: task.result.runner_result || null,
                      status: task.result.status
                    }
                  : null,
                status: task.status,
                started_at: task.started_at,
                finished_at: task.finished_at
              })) || []
            }
          />
        );
      case 'New Task':
        return <NewTask />;
      // case 'Change Password':
      //   return <CahngeVpsPassword />
      default:
        return <div>Tab content not found</div>;
    }
  };
  return (
    <>
      <div className='flex justify-between items-center'>
        <div className='my-2'>
          <h1 className='text-primary'>{terminalData?.name}</h1>
        </div>
        <div className='flex gap-2'>
          {/* <Link className='cursor-pointer' href='https://docs.tradevps.net/products/trading-terminal'>
            <Button variant='outline' size='sm'>
              <HelpCircle className='w-4 h-4 mr-2' />
              How Works?
            </Button>
          </Link>
          <Link href='/panel/trading/terminals/deploy'>
            <Button size='sm'>
              <PlusIcon />
              Deploy
            </Button>
          </Link> */}

          {/* <Button size='sm' onClick={() => setSekectedTabs('New Task')}>
            <PlusIcon />
            New Task
          </Button> */}
        </div>
      </div>
      <Tabs value={selectedTabs} onValueChange={setSekectedTabs} className='mt-1'>
        <TabsList className='w-full py-5 md:py-0 flex justify-between'>
          <div>
            {trigegerTabs.map(trigger => (
              <TabsTrigger key={trigger.value} className='cursor-pointer  px-5' value={trigger.value}>
                {trigger.title}
              </TabsTrigger>
            ))}
          </div>
          <div className='text-end'></div>
        </TabsList>
        <TabsContent value={selectedTabs}>
          <div className='mt-7'>{returnTabContent()}</div>
        </TabsContent>
      </Tabs>
    </>
  );
};
export default TenimalDetailComponent;
