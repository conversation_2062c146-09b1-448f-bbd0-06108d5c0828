'use client';

import type { Accept } from 'react-dropzone';
import { CloudArrowDownIcon } from '@heroicons/react/24/outline';
import { useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';

type FileType = 'ea' | 'settings' | 'image';

const fileAcceptance: Record<FileType, Accept> = {
  ea: {
    'text/x-mql': ['.mql4', '.mql5'],
    'application/octet-stream': ['.ex4', '.ex5']
  },
  settings: {
    'text/plain': ['.set', '.ini']
  },
  image: {
    'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/zip': ['.zip'],
    'application/x-rar-compressed': ['.rar']
  }
};

const calculateSHA256 = async (file: File): Promise<string> => {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

const FileUploader = ({
  fileType = 'ea',
  variant = 'file',
  title,
  describe,
  onReady,
  file,
  setFile
}: {
  fileType?: FileType;
  variant?: 'file' | 'picture';
  onReady?: (file: File | null, sha256?: string) => void;
  title: string;
  describe?: string;
  file: File | null;
  setFile: (file: File | null) => void;
}) => {
  const [sha256, setSha256] = useState<string>('');
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [percent, setPercent] = useState<number>(0);

  const maxSize = fileType === 'ea' ? 10 * 1024 * 1024 : fileType === 'settings' ? 1 * 1024 * 1024 : 10 * 1024 * 1024;

  const acceptedFiles = fileAcceptance[fileType];
  const onReadyRef = useRef(onReady);

  useEffect(() => {
    if (file) {
      setIsUploading(true);
      setSha256('');
      setPercent(0);

      const timer = setInterval(() => {
        setPercent((prev) => {
          if (prev >= 100) {
            clearInterval(timer);
            calculateSHA256(file).then((hash) => {
              setSha256(hash);
              setIsUploading(false);
              onReadyRef.current?.(file, hash);
            });
          }
          return Math.min(prev + 10, 100);
        });
      }, 100);

      return () => clearInterval(timer);
    }

    return undefined;
  }, [file]);

  const { getRootProps, getInputProps } = useDropzone({
    multiple: false,
    accept: acceptedFiles,
    maxSize,
    onDrop: (acceptedFiles) => {
      const selectedFile = acceptedFiles[0];
      if (selectedFile) {
        setFile(selectedFile);
      }
    },
    onDropRejected: (fileRejections) => {
      const rejection = fileRejections[0];
      if (rejection) {
        if (rejection.errors.some(e => e.code === 'file-too-large')) {
          console.warn(`File too large. Max: ${maxSize / 1024 / 1024}MB`);
        } else if (rejection.errors.some(e => e.code === 'file-invalid-type')) {
          console.warn(`Invalid type. Allowed: ${Object.values(acceptedFiles).flat().join(', ')}`);
        }
      }
    }
  });

  const handleRemoveFile = () => {
    setFile(null);
    setSha256('');
    setPercent(0);
    setIsUploading(false);
    onReady?.(null);
  };

  const renderFileSize = (size: number) =>
    size > 1024 * 1024 ? `${(size / 1024 / 1024).toFixed(1)} MB` : `${(size / 1024).toFixed(1)} KB`;

  return (
    <div className='w-full'>
      <div {...getRootProps({ className: 'cheque-upload-dropzone' })}>
        <input {...getInputProps()} />
        <div
          className={`flex rounded-md items-center py-3 border border-dotted border-primary/30 justify-center ${variant === 'picture' ? 'flex-col' : 'flex-col'} cursor-pointer`}
        >
          {isUploading ? (
            <div className='mt-2'>
              <div className='flex rounded-md items-center py-3 space-y-2 justify-center flex-col cursor-pointer'>
                <CloudArrowDownIcon className='h-10 w-10 text-primary' />
                <div className='badge bg-sky-100 text-sky-600 px-3 py-1 rounded-full text-xs font-medium flex items-center'>
                  <span className='relative flex justify-center items-center w-3 h-3 mr-1'>
                    <span className='absolute size-2 -mt-1 -ml-1 h-full w-full animate-ping rounded-full bg-sky-400 opacity-75' />
                    <span className='relative size-2 h-full w-full inline-flex rounded-full bg-sky-500' />
                  </span>
                  Uploading...
                  {' '}
                  {percent}
                  %
                </div>
              </div>
            </div>
          ) : file ? (
            <div className='bg-gray-100 rounded-lg p-4 shadow-sm'>
              <div className='w-full'>
                <div className='flex justify-end'>
                  <Badge variant='danger_light' className='p-0 rounded-sm ring-red-400'>
                    <Button
                      type='button'
                      variant='ghost'
                      className='text-red-500 text-[10px] hover:text-red-700 rounded-sm transition py-1 px-3'
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFile();
                      }}
                      aria-label='Remove file'
                    >
                      ✖
                    </Button>
                  </Badge>
                </div>
              </div>

              <div className='my-2 p-3 grid gap-y-1'>
                <div className='grid grid-cols-2  text-sm '>
                  <span className='font-medium text-gray-600'>File Name:</span>
                  <span className='text-right truncate text-gray-700'>{file.name}</span>

                  <span className='font-medium text-gray-600'>Size:</span>
                  <span className='text-right truncate text-gray-700'>{renderFileSize(file.size)}</span>
                </div>

                {sha256 && (
                  <div className='flex flex-col text-sm text-gray-800  '>
                    <span className='font-medium text-gray-600'>SHA256:</span>
                    <span className='break-all w-full text-gray-500 bg-gray-200 p-2 mt-2 rounded-sm text-xs font-mono'>
                      {sha256}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className='flex rounded-md items-center py-3  justify-center flex-col cursor-pointer'>
              <CloudArrowDownIcon className='h-10 w-10 text-primary' />
              <div className='text text-sm text-center mt-3 mx-3'>
                <p className='text-primary/50'>{title}</p>
                <p className='text-primary/50'>{describe}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploader;
