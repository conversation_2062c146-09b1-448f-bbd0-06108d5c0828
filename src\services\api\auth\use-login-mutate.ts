import type { APIError, ILoginParams, ILoginResponse } from '@tradevpsnet/client';
import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useLoginMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'onError' | 'query'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.AUTH_LOGIN,
    method: 'POST',
    ...options
  });
};

export const useLoginClientMutate = (options?: {
  onSuccess?: (data: ILoginResponse) => void;
  onError?: (error: APIError) => void;
}) => {
  const client = new Client('', process.env.NEXT_PUBLIC_SERVER_URL);

  return useMutation<ILoginResponse, APIError, ILoginParams>({
    mutationFn: (params: ILoginParams) => client.auth.login(params),
    ...options
  });
};
