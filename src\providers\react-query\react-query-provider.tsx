'use client';

import type { IReactQueryProviderProps as Props } from './react-query-provider.type';
import { QueryClientProvider } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { reactQueryConfig } from './config/react-query.config';

const ReactQueryDevtools = dynamic(() => import('@tanstack/react-query-devtools').then(d => d.ReactQueryDevtools));

export const ReactQueryProvider: React.FC<Props> = ({ children }) => {
  return (
    <QueryClientProvider client={reactQueryConfig}>
      {children}
      {process.env.NODE_ENV === 'development' && !process.env.STORYBOOK && (
        <ReactQueryDevtools initialIsOpen={false} buttonPosition='bottom-left' />
      )}
    </QueryClientProvider>
  );
};
