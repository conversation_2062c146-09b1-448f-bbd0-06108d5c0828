import { AxiosError } from 'axios';
import { formatDistanceToNow } from 'date-fns';
import { Paperclip, Send, Trash2, Waypoints } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { toast } from 'sonner';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import { Skeleton } from '@/components/loading/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useCloseTicketMutate } from '@/services/api/user/support/use-close-tecket';

import { useDeleteTicketMutate } from '@/services/api/user/support/use-delete-ticket';
import { useSendMessageMutate } from '@/services/api/user/support/use-send-message';
import { useShowUserMessagess } from '@/services/api/user/support/use-show-messages';

export type TicketMessageUser = {
  name: string;
  email: string;
  is_system: boolean;
};

export type TicketMessage = {
  id: string;
  content: string;
  created_at: string;
  user: TicketMessageUser;
  attachment_url: string | null;
};

export type TicketMessagesPagination = {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  first_page_url: string;
  from: number;
  last_page_url: string;
  links: string | null;
  next_page_url: string | null;
  prev_page_url: string | null;
};

export type TicketMessagesResponse = {
  ok: boolean;
  msg: string;
  data: TicketMessage[];
  pagination: TicketMessagesPagination;
};
type IUser = {
  avatar: string;
  is_system: boolean;
  name: string;
};
type IMessage = {
  id: string;
  user: IUser;
  created_at: string;
  content: string;
  attachment_url?: string;
};
const MessageBox = ({
  selectedTicket,
  refetchList,
  closeAll,
  setSelectedTickets
}: {
  selectedTicket: { id: string; title: string };
  closeAll: () => void;
  refetchList: () => void;
  setSelectedTickets: React.Dispatch<React.SetStateAction<{ id: string; title: string } | undefined>>;
}) => {
  const [messageContent, setMessageContent] = React.useState('');
  const [attachment, setAttachment] = React.useState<File | null>(null);
  const [isOpenDialog, setIsOpenDialog] = React.useState(false);
  const { data, isLoading, error, refetch } = useShowUserMessagess(selectedTicket.id, {
    enabled: !!selectedTicket.id
  });

  const deleteTicketMutation = useDeleteTicketMutate({
    onSuccess: () => {
      toast.success('Ticket Delete successfully');
      refetchList();
      refetch();
      setIsOpenDialog(false);
      setSelectedTickets({ id: '', title: '' });
    }
  });
  const handleDeleteTickets = () => {
    const id = selectedTicket.id;
    deleteTicketMutation.mutate({ params: { id } });
  };
  const CloseTicketMutation = useCloseTicketMutate({
    onSuccess: () => {
      toast.success('Ticket Closed successfully');
      refetchList();
      // closeAll();
    }
  });
  const handleCloseTickets = (id: string) => {
    CloseTicketMutation.mutate({ params: { id } });
  };
  const sendTicketMutation = useSendMessageMutate(selectedTicket.id, {
    onSuccess: () => {
      toast.success('Message sent successfully');
      setMessageContent('');
      setAttachment(null);
      refetch();
    },
    onError: () => {
      toast.error('Failed to send message');
    }
  });
  const handleSend = () => {
    if (!messageContent.trim() && !attachment) {
      toast.error('Please enter a message or attach a file.');
      return;
    }

    const formData = new FormData();
    formData.append('content', messageContent);
    if (attachment) {
      formData.append('attachment', attachment);
    }

    sendTicketMutation.mutate({ body: formData });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setAttachment(e.target.files[0]);
    }
  };

  return (
    <React.Fragment>
      <Card className='w-full h-auto max-h-[750px] flex flex-col'>
        <CardHeader className='py-1'>
          <div className='flex items-center justify-between'>
            <p className='font-semibold text-primary'>{selectedTicket.title}</p>
            <div className='flex gap-1'>
              <Button onClick={() => closeAll()} variant='outline' size='sm' className='w-[80px]'>
                Back
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size='sm' variant='table' className='flex items-center'>
                    {/* <EllipsisVertical /> */}
                    <Waypoints />
                    Actions
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent align='end' className='text-primary'>
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>

                  <DropdownMenuItem onClick={() => handleCloseTickets(selectedTicket.id)}>
                    Close Ticket
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setIsOpenDialog(true)} className='text-destructive'>
                    <Trash2 className='mr-2 h-4 w-4' />
                    Delete Ticket
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <Separator />
        {/* Scrollable Content */}
        <CardContent className='flex-1 mt-2 overflow-y-auto px-4'>
          {!isLoading && data?.length > 0 ? (
            data.map((message: IMessage) => (
              <div
                key={message.id}
                className={`flex justify-content-${message.user.is_system ? 'start' : 'end'} mb-10`}
              >
                <div className={`flex flex-col items-${message.user.is_system ? 'start' : 'end'} w-full`}>
                  <div className='flex items-center mb-2'>
                    <div className={message.user.is_system ? 'ms-3' : 'me-3 '}>
                      <p className='text-sm text-end font-bold hover:text-blue-400'>
                        {message.user.is_system ? message.user.name : 'You'}
                      </p>
                      <span className='text-gray-400 text-xs fs-7 mb-1'>
                        {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                      </span>
                    </div>
                    <div>
                      <Avatar className='w-[40px] h-[40px]'>
                        <AvatarImage src={message.user.avatar} alt={message.user.name} />
                        <AvatarFallback className='bg-gray-100/80 text-secondary'>
                          {message.user.name?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </div>
                  <div
                    className={`p-5 rounded ${message.user.is_system ? 'bg-gray-100 text-start dark:bg-gray-800 ' : 'bg-blue-100 text-end dark:bg-blue-900 '}  font-semibold `}
                  >
                    <p className='text-gray-600 dark:text-white text-sm'>{message.content}</p>
                    {message.attachment_url ? (
                      <Link
                        href={message.attachment_url}
                        target='_blank'
                        download
                        className='flex items-center gap-2 text-sm text-blue-500 hover:underline'
                      >
                        <Paperclip className='size-4 text-gray-400' />
                        <span className='text-xs text-gray-500 '>
                          {decodeURIComponent(message.attachment_url?.split('?')[0]?.split('/').pop() ?? '')}
                        </span>
                      </Link>
                    ) : (
                      <div></div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            // <div className='relative flex justify-center items-center h-full py-5'>
            <div>
              {Array.from({ length: 10 }).map((_, i) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={i} className={`mt-5 w-full flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                  <div className='w-1/3'>
                    <div className='flex gap-3 items-center'>
                      <Skeleton height={30} width={30} className='rounded-full' />
                      <div className='w-full'>
                        <div className='w-1/2 mb-3'>
                          <Skeleton height={10} />
                        </div>
                        <div className='w-full'>
                          <Skeleton height={10} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {/* <Image
              src={EmptyLogo}
              width={180}
              height={180}
              alt=''
              className={isLoading ? 'opacity-50' : 'opacity-100'}
            />
            <div
              className={`absolute inset-0 flex justify-center items-center transition-opacity duration-300 ${isLoading ? 'opacity-100' : 'opacity-0'}`}
            >
              <PulseLoader
                color='blue'
                loading={isLoading}
                size={10}
                aria-label='Loading Spinner'
                data-testid='loader'
              />
            </div> */}
            </div>
          )}
        </CardContent>

        {/* Sticky Footer */}
        <CardFooter className='flex flex-col border-t px-4 pt-4'>
          <Textarea
            className='w-full'
            placeholder='Type your message...'
            value={messageContent}
            onChange={e => setMessageContent(e.target.value)}
          />

          {error && (
            <div className='text-red-600 text-sm mt-2'>
              <p>
                Error:
                {' '}
                {error instanceof AxiosError
                  ? error.response?.data?.message || error.message
                  : 'An unexpected error occurred.'}
              </p>
            </div>
          )}

          <div className='flex w-full items-center justify-between mt-4'>
            <label className='flex items-center gap-2 p-1.5 rounded hover:bg-blue-100 hover:cursor-pointer'>
              <input type='file' hidden onChange={handleFileChange} />
              <Paperclip className='size-4 text-gray-400' />
              {attachment && <span className='text-sm text-gray-600 truncate max-w-[200px]'>{attachment.name}</span>}
            </label>

            <div>
              <Button variant='blue_primary' className='px-5 w-[100px]' onClick={handleSend} disabled={isLoading}>
                {sendTicketMutation?.isPending ? 'Sending' : 'Send'}
                {sendTicketMutation?.isPending ? <CyrcleSvg color='white' /> : <Send size={16} />}
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
      <DeleteConfirmDialog
        open={isOpenDialog}
        setOpen={setIsOpenDialog}
        confirmSubject={selectedTicket.title}
        onDeleteFunction={handleDeleteTickets}
        isLoading={deleteTicketMutation.isPending}
        haveInput={false}
      />
    </React.Fragment>
  );
};

export default MessageBox;
