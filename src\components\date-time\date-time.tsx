import type { IDateTimeProps } from './date-time.type';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { useSettings } from '@/stores/settings';
import { DateTimePropsSchema } from './date-time.type';
import { formatDate, getTimeAgo } from './helper';

export const DateTime: React.FC<IDateTimeProps> = (props) => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const { dateFormat, useLocalTime } = useSettings();

  const parsed = DateTimePropsSchema.safeParse(props);
  useEffect(() => {
    if (!parsed.success) {
      return;
    }

    const value = parsed.data.value;
    const date = typeof value === 'number' ? new Date(value * 1000) : new Date(value);

    const timeDiff = (currentTime.getTime() - date.getTime()) / 1000;
    if (timeDiff >= 60) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, [parsed.success, currentTime, parsed.data?.value]);

  if (!parsed.success) {
    console.error('Invalid props:', parsed.error.format());
    return null;
  }

  const { value, className } = parsed.data;

  const date
    = typeof value === 'number'
      ? new Date(value * 1000) // UNIX to ms
      : new Date(value); // ISO string

  const formattedDateTime = formatDate(date, dateFormat.format, !useLocalTime);
  const isToday = currentTime.toDateString() === date.toDateString();
  const todayTime = getTimeAgo(currentTime, date);

  return (
    <div>
      <time dateTime={date.toISOString()} className={cn(className)}>
        {isToday ? todayTime : formattedDateTime}
      </time>
    </div>
  );
};
