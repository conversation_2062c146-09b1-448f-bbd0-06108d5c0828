'use client';
import { <PERSON><PERSON>, CalendarD<PERSON>, <PERSON>pu, <PERSON><PERSON><PERSON>, FolderKanban, GlobeLock, MemoryStick } from 'lucide-react';
import * as React from 'react';
import { DateTime } from '@/components/date-time';
import { Card } from '@/components/ui/card';

const Overview = ({ VpsDatail }: { VpsDatail: any }) => {
  const [createAt, setCreateAt] = React.useState<any>(null);

  React.useLayoutEffect(() => {
    const date = new Date(VpsDatail?.created_at);
    setCreateAt(date);
  }, [VpsDatail?.created_at]);
  function isValidDateInput(value: any): value is string {
    const date = new Date(value);
    return date instanceof Date && !Number.isNaN(date.getTime());
  }

  return (
    <div className='grid grid-cols-3 gap-4'>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <Cpu className='size-5' />
          </div>

          <p className='text-sm font-medium'>Cpu:</p>
          <p className='text-sm text-muted-foreground'>{VpsDatail?.cpu}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <MemoryStick className='size-5' />
          </div>

          <p className='text-sm font-medium'>Memory:</p>
          <p className='text-sm text-muted-foreground'>{VpsDatail?.memory}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <CalendarDays className='size-5' />
          </div>
          <p className='text-sm font-medium'>Create At:</p>
          <p className='text-sm text-muted-foreground'>
            {isValidDateInput(createAt) ? <DateTime value={new Date(createAt).toISOString()} /> : null}
          </p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <Banknote className='size-5' />
          </div>
          <p className='text-sm font-medium'>Hard limit:</p>
          <p className='text-sm text-muted-foreground'>{`$ ${VpsDatail?.hard_limit}`}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <Banknote className='size-5' />
          </div>
          <p className='text-sm font-medium'>Soft limit:</p>
          <p className='text-sm text-muted-foreground'>{`$ ${VpsDatail?.soft_limit}`}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <Cylinder className='size-5' />
          </div>
          <p className='text-sm font-medium'>Storage:</p>
          <p className='text-sm text-muted-foreground'>{VpsDatail?.storage || 0}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <GlobeLock className='size-5' />
          </div>
          <p className='text-sm font-medium'>Ip:</p>
          <p className='text-sm text-muted-foreground'>{VpsDatail?.ip}</p>
        </div>
      </Card>
      <Card className='px-5'>
        <div className='flex items-center gap-2'>
          <div className='flex items-center rounded-full p-3 bg-gray-100'>
            {' '}
            <FolderKanban className='size-5' />
          </div>
          <p className='text-sm font-medium'>Charge:</p>
          <p className='text-sm text-muted-foreground'>{VpsDatail?.charge}</p>
        </div>
      </Card>
    </div>
  );
};

export default Overview;
