import type { AxiosResponse } from 'axios';
import type { IUseMutationFactoryProps } from '@/hooks/api/use-mutation-factory/use-mutation-factory.type';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { SERVER } from 'libs/Axios';
import { useMutationFactory } from '@/hooks/api/use-mutation-factory';

type ApiToken = {
  id: number;
  tokenable_type: string;
  tokenable_id: string;
  name: string;
  abilities: string[];
  last_used_at: string | null;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
};

type ApiResponse<T> = {
  ok: boolean;
  msg: string;
  data: T extends any[] ? T : T[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    first_page_url: string;
    from: number;
    last_page_url: string;
    links: string;
    next_page_url: string | null;
    prev_page_url: string | null;
  };
};

export const useApiListFetch = (params?: { page?: number; per_page?: number }) => {
  return useQuery<AxiosResponse<ApiResponse<ApiToken>>>({
    queryKey: [API_QUERY_KEY.API_LIST, params],
    queryFn: async () => {
      const response = await SERVER.get<ApiResponse<ApiToken>>(API_ROUTES.API_MANAGEMENT, { params });
      return response;
    }
  });
};

export const useCreateApiTokenMutation = (
  options?: Pick<IUseMutationFactoryProps<ApiResponse<ApiToken>>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<ApiResponse<ApiToken>>({
    url: API_ROUTES.API_MANAGEMENT,
    method: 'POST',
    isUrlencoded: false,
    ...options
  });
};
export const useRevokeTokenMutation = (id: string) => {
  return useMutationFactory<{ id: string; ok: boolean; msg: string; data: null }>({
    url: `${API_ROUTES.API_REVOKE_TOKEN}/${id}`,
    method: 'DELETE'
  });
};
