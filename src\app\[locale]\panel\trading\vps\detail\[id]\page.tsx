'use client';

import VpsDetailComponent from '@/components/panel/trading/vps/details';
// import { getTranslations, setRequestLocale } from 'next-intl/server';

// type ISignInPageProps = {
//   params: Promise<{ locale: string }>;
// };

// export async function generateMetadata(props: ISignInPageProps) {
//   const { locale } = await props.params;
//   const t = await getTranslations({
//     locale,
//     namespace: 'SignIn',
//   });

//   return {
//     title: t('meta_title'),
//     description: t('meta_description'),
//   };
// }
// props: ISignInPageProps
export default function TerminalDetail() {
  // const { locale } = await props.params;
  // setRequestLocale(locale);

  return <VpsDetailComponent />;
}
