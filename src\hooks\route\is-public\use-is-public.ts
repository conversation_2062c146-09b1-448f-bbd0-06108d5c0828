import { usePathname } from 'next/navigation';
import { PAGE_ROUTES } from '@/configs/page-routes';

const PUBLIC_ROUTES = [
  PAGE_ROUTES.HOME,
  PAGE_ROUTES.LOGIN,
  PAGE_ROUTES.REGISTER,
  PAGE_ROUTES.FORGET_PASSWORD,
  PAGE_ROUTES.CONTACT,
  PAGE_ROUTES.TERMS,
  PAGE_ROUTES.PRIVACY,
  PAGE_ROUTES.DMCA,
  PAGE_ROUTES.COOKIES,
  PAGE_ROUTES.BLOG
] as const;

export function useIsPublicRoute() {
  const pathname = usePathname();

  // Remove locale prefix if present
  // const pathWithoutLocale = routing.locales.some(locale => pathname.startsWith(`/${locale}`))
  // ? pathname.substring(3) // Remove first 3 characters (e.g., '/en' or '/fr')
  // : pathname;

  return PUBLIC_ROUTES.includes(pathname as (typeof PUBLIC_ROUTES)[number]);
}
