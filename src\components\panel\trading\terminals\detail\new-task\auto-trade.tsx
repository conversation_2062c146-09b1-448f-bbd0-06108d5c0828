import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { BookmarkX } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

export default function AutoTrade() {
  const [allowAlgoTrading, setAllowAlgoTrading] = useState<number | null>(null);
  const { id } = useParams();
  const { mutate, isPending } = useNewTaskMutation();

  const handleSubmit = () => {
    if (allowAlgoTrading === null) {
      toast.error('Please select an option');
      return;
    }

    mutate(
      {
        params: { id: id as string },
        queryParams: { type: '4' },
        body: {
          payload: {
            allow_algo_trading: allowAlgoTrading
          }
        }
      },
      {
        onSuccess: () => toast.success('Auto trading setting updated'),
        onError: () => toast.error('Failed to update setting')
      }
    );
  };

  return (
    <div className='-space-y-px rounded-md bg-white dark:bg-zinc-900 flex flex-col items-center py-10'>
      <Alert
        className='mb-2 cursor-pointer'
        variant={allowAlgoTrading === 0 ? 'success' : 'default'}
        onClick={() => setAllowAlgoTrading(0)}
      >
        <CheckCircleIcon />
        <AlertTitle>On</AlertTitle>
        <AlertDescription>Allow EAs (Expert Advisors) to make an order and manage trading positions.</AlertDescription>
      </Alert>

      <Alert
        className='cursor-pointer'
        variant={allowAlgoTrading === 1 ? 'destructive' : 'default'}
        onClick={() => setAllowAlgoTrading(1)}
      >
        <BookmarkX className='h-4 w-4' />
        <AlertTitle>Off</AlertTitle>
        <AlertDescription>Will not allow any EA (Expert Advisor) to order and/or trade automatically.</AlertDescription>
      </Alert>

      <Button onClick={handleSubmit} disabled={isPending || allowAlgoTrading === null} className='ml-auto mt-3'>
        {isPending ? 'Submitting...' : 'Submit'}
      </Button>
    </div>
  );
}
