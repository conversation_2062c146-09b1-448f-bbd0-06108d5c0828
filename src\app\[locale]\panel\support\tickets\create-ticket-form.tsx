import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { CyrcleSvg } from '@/components/cyrcle-svg';

import FileUploader from '@/components/drop-zone';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useAddUserTicketMutate } from '@/services/api/user/support/use-add-ticket';
import { useTicketsDepartmentFetch } from '@/services/api/user/support/use-fetch-tickets';

const ticketSchema = z.object({
  title: z.string().min(5, 'Title is requiredm minimum 5 words.'),
  department: z.string().min(1, 'Select a department'),
  content: z.string().min(5, 'Content is required minimum 5 words'),
  attachment: z.any().optional()
});

type TicketFormValues = z.infer<typeof ticketSchema>;

type IProps = {
  setCreateNewTicket: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
};

const CreateTicketForm = ({ setCreateNewTicket, refetch }: IProps) => {
  const defaultValues = {
    title: '',
    department: '',
    content: ''
  };
  const form = useForm<TicketFormValues>({
    resolver: zodResolver(ticketSchema),
    defaultValues
  });

  const [attachmentFile, setAttachmentFile] = useState<File | null>(null);

  const [error, setError] = useState<string>('');
  const { data: departmentList } = useTicketsDepartmentFetch();
  const mappedDepartments
    = departmentList?.map((department: { label: string; key: any }) => {
      let type = 'info';
      let iconColor = 'bg-sky-400';

      if (department.label === 'Billing') {
        type = 'warning';
        iconColor = 'bg-amber-400';
      } else if (department.label === 'Other') {
        type = 'default';
        iconColor = 'bg-neutral-400';
      }

      return {
        key: department.key.toString(),
        value: department.label,
        type,
        icon: <p className={`size-2.5 rounded-full ${iconColor}`} />
      };
    }) || [];
  const sendTicketMutation = useAddUserTicketMutate({
    onSuccess: () => {
      toast.success('Send Ticket successfully');
      setError('');
      refetch();
      setAttachmentFile(null);
      form.reset({ ...defaultValues });
    },
    onError: (error) => {
      if (error.response?.status === 422) {
        const fieldErrors = error.response?.data;

        setError(fieldErrors.message);
      } else {
        setError('An unexpected error occurred. Please try again later.');
      }
    }
  });
  const onSubmit = (data: TicketFormValues) => {
    sendTicketMutation.mutate({ body: data });
  };

  return (
    <Card className='self-start  p-8  w-full'>
      <CardHeader>
        <h2 className='text-2xl text-gray-700 dark:text-gray-100 font-semibold mb-2'>New Ticket</h2>
        <p className='text-muted-foreground mb-6 text-sm'>Fill the form below to create a support ticket.</p>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {/* Title */}
              <FormField
                control={form.control}
                name='title'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-gray-600 dark:text-gray-300'>Title</FormLabel>
                    <FormControl>
                      <Input placeholder='e.g. Request payout with refund' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Department */}
              <FormField
                control={form.control}
                name='department'
                render={({ field }) => {
                  // eslint-disable-next-line react-hooks/rules-of-hooks
                  const [searchTerm, setSearchTerm] = useState('');

                  const filteredDepartments = mappedDepartments.filter((dept: { value: string }) =>
                    dept.value.toLowerCase().includes(searchTerm.toLowerCase())
                  );

                  return (
                    <FormItem>
                      <FormLabel className='text-gray-600 dark:text-gray-300'>Department</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                        <FormControl>
                          <div className='relative w-full'>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Select department' />
                            </SelectTrigger>
                            {field.value && (
                              <button
                                type='button'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  field.onChange('');
                                }}
                                className='absolute text-[12px] cursor-pointer right-9 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-900 hover:scale-110'
                              >
                                ✕
                              </button>
                            )}
                          </div>
                        </FormControl>
                        <SelectContent className='p-0'>
                          <div className='w-full px-2 py-1 border-b mb-2'>
                            <Input
                              placeholder='Search departments...'
                              value={searchTerm}
                              onChange={e => setSearchTerm(e.target.value)}
                              className='w-full'
                              onClick={e => e.stopPropagation()}
                              onKeyDown={e => e.stopPropagation()}
                            />
                          </div>
                          <div className='max-h-[250px] overflow-y-auto'>
                            {filteredDepartments.length > 0 ? (
                              filteredDepartments.map((dept: any) => (
                                <SelectItem key={dept.key} value={dept.key}>
                                  <div className='flex items-center gap-2'>
                                    {dept.icon}
                                    <span>{dept.value}</span>
                                  </div>
                                </SelectItem>
                              ))
                            ) : (
                              <div className='py-2 text-center text-sm text-gray-500'>No departments found</div>
                            )}
                          </div>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            {/* Content */}
            <FormField
              control={form.control}
              name='content'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-gray-600 dark:text-gray-300'>Content</FormLabel>
                  <FormControl>
                    <Textarea placeholder='Describe your issue...' {...field} rows={5} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File Uploader */}
            <FormItem>
              <FileUploader
                file={attachmentFile}
                setFile={(file: File | null) => {
                  setAttachmentFile(file);
                  form.setValue('attachment', file);
                }}
                title='Upload a file or drag and drop'
                describe='jpg, jpeg, png, pdf, doc, docx, xls, xlsx, zip, rar. Maximum size: 10MB.'
                fileType='image'
              />
            </FormItem>

            <Separator />

            <div className='flex justify-end gap-4'>
              <Button type='button' variant='outline' onClick={() => setCreateNewTicket(false)}>
                Cancel
              </Button>
              {error && <p className='text-red-500 text-sm'>{error}</p>}
              <Button variant='blue_primary'>
                {sendTicketMutation?.isPending ? <CyrcleSvg color='white' /> : null}
                Submit Ticket
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default CreateTicketForm;
