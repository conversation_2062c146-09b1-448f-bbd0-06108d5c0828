'use client';

import {
  AppWindow,
  BookOpenText,
  Bot,
  Box,
  Boxes,
  BrainCircuit,
  ChartCandlestick,
  FolderClosed,
  Grid2X2,
  LifeBuoy,
  Monitor,
  Server,
  Square,
  SquareUser,
  Webhook
} from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { NavMain } from '@/components/panel/nav-main';
import { NavSecondary } from '@/components/panel/nav-secondary';
import { NavUser } from '@/components/panel/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useUser } from '@/hooks/user/user';
import { generateInitials, getConsistentColor } from '@/utils/avatar';
import Logo from '../logo';
import { NavMarketplace } from './nav-marketplace';
import { NavPlatform } from './nav-platform';

const data = {
  navPlatform: [
    {
      title: 'Dashboard',
      url: PAGE_ROUTES.PANEL,
      icon: Square
    },
    {
      title: 'Account Center',
      url: PAGE_ROUTES.PANEL_ACCOUNT,
      icon: SquareUser
    },
    {
      title: 'Projects',
      url: PAGE_ROUTES.PANEL_PROJECTS,
      icon: FolderClosed,
      count: 1
    }
  ],

  navMain: [
    {
      title: 'Trading Terminal',
      url: PAGE_ROUTES.PANEL_TRADING_TERMINALS,
      icon: AppWindow
    },
    {
      title: 'Trading VPS',
      url: PAGE_ROUTES.PANEL_TRADING_VPS,
      icon: Server
    },
    {
      title: 'Trading Desktop',
      url: PAGE_ROUTES.PANEL_TRADING_DESKTOPS,
      icon: Monitor
    },
    {
      title: 'Windows Server',
      url: PAGE_ROUTES.PANEL_TRADING_WINDOWS,
      icon: Grid2X2
    }
  ],
  navMarketplace: [
    {
      title: 'Trading Bots',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_BOTS,
      icon: Bot
    },
    {
      title: 'Indicators',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_INDICATORS,
      icon: ChartCandlestick
    },
    {
      title: 'Services',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_SERVICES,
      icon: Box
    },
    {
      title: 'AI Models',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_AI_MODELS,
      icon: BrainCircuit
    },
    {
      title: 'APIs',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_APIS,
      icon: Webhook
    },
    {
      title: 'Solutions',
      url: PAGE_ROUTES.PANEL_MARKETPLACE_SOLUTIONS,
      icon: Boxes
    }
  ],
  navSecondary: [
    {
      title: 'Documentation',
      url: 'https://docs.tradevps.net',
      icon: BookOpenText
    },
    {
      title: 'Support',
      url: PAGE_ROUTES.PANEL_SUPPORT,
      icon: LifeBuoy
    }
  ]
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: user } = useUser();

  const generateAvatarFallback = (name: string) => {
    const initials = generateInitials(name);
    const bgColor = getConsistentColor(name);

    return {
      initials,
      bgColor
    };
  };

  const userInfo = (() => {
    if (!user) {
      const fallback = generateAvatarFallback('Guest User');
      return {
        name: 'Guest',
        email: '<EMAIL>',
        avatar: '',
        avatarFallback: fallback
      };
    }

    const fallback = generateAvatarFallback(user.name);
    return {
      name: user.name,
      email: user.email,
      avatar: user.avatar || '',
      avatarFallback: fallback
    };
  })();

  return (
    <Sidebar variant='inset' {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size='lg' asChild>
              <Link href={PAGE_ROUTES.PANEL}>
                <Logo />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavPlatform items={data.navPlatform} />
        <NavMain items={data.navMain} />
        <NavMarketplace items={data.navMarketplace} />
        <NavSecondary items={data.navSecondary} className='mt-auto' />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userInfo} />
      </SidebarFooter>
    </Sidebar>
  );
}
