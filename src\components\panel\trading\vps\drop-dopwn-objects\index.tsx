import type { BadgeVariants } from '@/components/badge';

export const Regions = [
  {
    id: '0',
    label: 'New York',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '1',
    label: 'Amesterdam',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '2',
    label: 'Frankfurt',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '3',
    label: 'London',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '4',
    label: 'San Francisco ',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '5',
    label: 'Singapore',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '6',
    label: 'Toronto',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '7',
    label: 'Bangkok',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '8',
    label: 'Hong Kong',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '9',
    label: 'Paris',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '10',
    label: 'Sydney',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '11',
    label: 'Tokyo',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  },
  {
    id: '12',
    label: 'Mumbai',
    icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
  }
];

export enum enumStatus {
  CREATED = 0,
  PAYMENT = 1,
  QUEUE = 2,
  DEPLOYING = 3,
  INSTALLING = 4,
  RUNNING = 5,
  FINISHED = 6,
  CANCELED = 7,
  BLOCKED = 8
}
export const VpsStatusMap: Record<number, { label: string; variant: BadgeVariants }> = {
  [enumStatus.CREATED]: { label: 'Created', variant: 'stone_light' },
  [enumStatus.PAYMENT]: { label: 'Payment', variant: 'slate_light' },
  [enumStatus.QUEUE]: { label: 'Queue', variant: 'fuchsia_light' },
  [enumStatus.DEPLOYING]: { label: 'Deploying', variant: 'sky_light' },
  [enumStatus.INSTALLING]: { label: 'Installing', variant: 'primary_light' },
  [enumStatus.RUNNING]: { label: 'Running', variant: 'emerald_light' },
  [enumStatus.FINISHED]: { label: 'Finished', variant: 'neutral_light' },
  [enumStatus.CANCELED]: { label: 'Canceled', variant: 'warning_light' },
  [enumStatus.BLOCKED]: { label: 'Blocked', variant: 'rose_light' }
};

export const statusItems = [
  {
    key: '0',
    value: 'Created',
    icon: <p className='size-2.5 rounded-full bg-blue-500'> </p>
  },
  { key: '1', value: 'Payment', icon: <p className='size-2.5 rounded-full bg-amber-400'> </p> },

  {
    key: '2',
    value: 'Queue',
    icon: <p className='size-2.5 rounded-full bg-blue-400 '> </p>
  },
  {
    key: '3',
    value: 'Deploying',
    icon: <p className='size-2.5 rounded-full bg-amber-400 '> </p>
  },
  {
    key: '4',
    value: 'Installing',
    icon: <p className='size-2.5 rounded-full bg-amber-400'> </p>
  },
  {
    key: '5',
    value: 'Running',
    icon: <p className='size-2.5 text-xs rounded-full bg-green-400'> </p>
  },
  {
    key: '6',
    value: 'Finished',
    icon: <p className='size-2.5 rounded-full bg-green-400 '> </p>
  },
  {
    key: '7',
    value: 'Canceled',
    icon: <p className='size-2.5 rounded-full bg-rose-400 '> </p>
  },
  {
    key: '8',
    value: 'Blocked',
    icon: <p className='size-2.5 rounded-full bg-rose-400 '> </p>
  }
  // {
  //   key: '9',
  //   value: 'Failed',
  //   icon: <p className='size-2.5 rounded-full bg-red-400 '> </p>
  // },
  // {
  //   key: '10',
  //   value: 'Deleting',
  //   icon: <p className='size-2.5 rounded-full bg-amber-400'> </p>
  // },
  // {
  //   key: '11',
  //   value: 'Destroyed',
  //   icon: <p className='size-2.5 rounded-full bg-gray-400 '> </p>
  // }
];
export const providerItems = [
  {
    key: '0',
    value: 'Digital Ocean',
    icon: <p className='size-2.5 rounded-full bg-blue-500'> </p>
  },
  { key: '1', value: 'Vultr', icon: <p className='size-2.5 rounded-full bg-sky-400'> </p> },

  {
    key: '2',
    value: 'Linode',
    icon: <p className='size-2.5 rounded-full bg-green-400'> </p>
  },
  {
    key: '3',
    value: 'Aws',
    icon: <p className='size-2.5 rounded-full bg-amber-400 '> </p>
  },
  {
    key: '4',
    value: 'Google Cloud',
    icon: <p className='size-2.5 rounded-full bg-amber-400'> </p>
  },
  {
    key: '5',
    value: 'Azure',
    icon: <p className='size-2.5 text-xs rounded-full bg-blue-400'> </p>
  },
  {
    key: '6',
    value: 'Alibaba Cloud',
    icon: <p className='size-2.5 rounded-full bg-orange-400 '> </p>
  },
  {
    key: '7',
    value: 'Ctr Cloud',
    icon: <p className='size-2.5 rounded-full bg-green-400 '> </p>
  }
];
