import { <PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

type DeleteDialogProps = {
  title?: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  confirmSubject: string;
  haveInput?: boolean;
  onDeleteFunction: (id?: string) => void;
  isLoading: boolean;
};

export const DeleteConfirmDialog: React.FC<DeleteDialogProps> = ({
  title = 'Delete',
  open,
  setOpen,
  confirmSubject,
  haveInput = true,
  onDeleteFunction,
  isLoading
}) => {
  const [confirmCode, setConfirmCode] = useState('');

  // Reset input when dialog is closed or opened
  useEffect(() => {
    if (!open) {
      setConfirmCode('');
    }
  }, [open]);

  const closeDialog = () => {
    setConfirmCode('');
    setOpen(false);
  };

  const isConfirm = useMemo(() => {
    if (!haveInput) {
      return true;
    }
    return confirmCode.trim() === confirmSubject;
  }, [haveInput, confirmCode, confirmSubject]);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent onClick={e => e.stopPropagation()} className='sm:max-w-[480px]'>
        <DialogHeader>
          <DialogTitle className='text-primary flex items-center gap-2'>
            <ShieldAlert className='w-5 h-5' />
            Confirm
            {' '}
            {title}
          </DialogTitle>
          <DialogDescription asChild>
            <div className='flex flex-col gap-3 mt-2'>
              <p>
                Are you sure you want to
                {' '}
                {title}
                {' '}
                <strong>{confirmSubject}</strong>
                ?
              </p>
              <p>This action cannot be undone.</p>
              {haveInput ? (
                <div className='space-y-1 '>
                  <p className='select-none mb-2 text-sm text-gray-600'>
                    Type
                    {' '}
                    <strong>{confirmSubject}</strong>
                    {' '}
                    to confirm:
                  </p>
                  <Input
                    placeholder='Enter exact name'
                    value={confirmCode}
                    onChange={e => setConfirmCode(e.target.value)}
                    className={isConfirm ? '' : 'border-destructive'}
                  />
                </div>
              ) : (
                <div></div>
              )}
            </div>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='mt-4 flex gap-3 justify-end'>
          <Button
            variant='ghost'
            className='text-muted-foreground hover:text-foreground transition-colors'
            onClick={(e) => {
              e.stopPropagation();
              closeDialog();
            }}
          >
            Cancel
          </Button>

          <Button
            disabled={!isConfirm}
            variant='destructive'
            className={`transition-all duration-200 flex items-center gap-2 px-5 ${
              isConfirm ? 'bg-gradient-to-r from-red-600 to-red-500 hover:brightness-110 hover:scale-[1.02]' : ''
            }`}
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Your delete logic here
              onDeleteFunction();
            }}
          >
            {isLoading ? <CyrcleSvg /> : <Trash2 className='w-4 h-4' />}
            Confirm
            {' '}
            {title}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
