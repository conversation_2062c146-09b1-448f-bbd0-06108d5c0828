import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useDeleteTicketMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'query'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.DELETE_TICKET,

    method: 'DELETE',
    ...options
  });
};
