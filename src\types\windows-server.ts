type BaseResponse<T> = {
  ok: boolean;
  msg: string;
  data: T;
};

type BaseServer = {
  id: string;
  name: string;
  project_id: string;
  provider: number;
  region: number;
  status: number;
  created_at: string;
  updated_at: string;
};

export type IWindowsServer = {
  id: string;
  name: string;
  project_id: string;
  provider: number;
  region: number;
  status: number;
  created_at: string;
  updated_at: string;
  admin_username: string | null;
  vm_class: string | null;
  version: number;
  cpu: number;
  memory: number;
  disk: number;
  disk_type: string | null;
  auto_scale_cpu: number | null;
  auto_scale_memory: number | null;
  charge: number;
  soft_limit: number | null;
  hard_limit: number | null;
  ip: string;
  has_static_ip: boolean;
  ip_version: number | null;
  synced_at: string | null;
};

export type IWindowsQueryParams = {
  date_from?: string;
  date_to?: string;
  per_page?: number;
  page?: number;
  search?: string;
  sort_by?: 'created_at' | 'name' | 'status';
  sort_direction?: 'asc' | 'desc';
  status?: number;
  region?: number;
  project_id?: string;
  version?: number;
};

export type IPaginationMetadata = {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  first_page_url: string;
  from: number;
  last_page_url: string;
  next_page_url: string | null;
  prev_page_url: string | null;
};

export type IWindowsServersResponse = BaseResponse<IWindowsServer[]> & {
  pagination: IPaginationMetadata;
};

export type IRegion = {
  id: number;
  label: string;
  icon?: string;
};

export type IRegionsResponse = BaseResponse<IRegion[]>;

export type TWindowsServerAction = 'start' | 'stop' | 'restart';

export type IWindowsServerActionRequest = {
  server_id: string;
  action: TWindowsServerAction;
};

export type IWindowsServerActionResponse = BaseResponse<{
  id: string;
  status: number;
  updated_at: string;
}>;

export type IWindowsServerDeployRequest = {
  name: string;
  region: number;
  project_id: string;
  provider?: number;
  version?: number;
  cpu?: number;
  memory?: number;
  disk?: number;
  disk_type?: string;
  auto_scale_cpu?: boolean;
  auto_scale_memory?: boolean;
  has_static_ip?: boolean;
};

export type IWindowsServerDeployResponse = BaseResponse<
  BaseServer & {
    version: number;
  }
>;

export type IWindowsServerDetailResponse = BaseResponse<IWindowsServer>;
export type IWindowsServerDeleteResponse = BaseResponse<string>;
