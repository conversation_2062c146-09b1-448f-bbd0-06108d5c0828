import { useParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

const LoginAccount = () => {
  const { id } = useParams();
  const { mutate, isPending } = useNewTaskMutation();

  // State for form fields
  const [login, setLogin] = useState('');
  const [password, setPassword] = useState('');
  const [serverName, setServerName] = useState('');

  const handleSubmit = () => {
    if (!login || !password || !serverName) {
      toast.error('Please fill all required fields');
      return;
    }

    mutate(
      {
        params: { id: id as string },
        queryParams: {
          type: '9'
        },
        body: {
          payload: {
            login,
            password,
            server: serverName
          }
        }
      },
      {
        onSuccess: () => {
          toast.success('Login successful');
          setLogin('');
          setPassword('');
          setServerName('');
        },
        onError: (error) => {
          toast.error(error.message || 'Login failed');
        }
      }
    );
  };

  return (
    <div className='flex flex-col items-center justify-between gap-y-4 py-3'>
      <div className='w-full flex flex-col gap-1'>
        <div className='flex items-baseline flex-wrap lg:flex-nowrap gap-2.5'>
          <label
            htmlFor='trading-account-login'
            className='form-label flex text-sm basis-1/4 items-center gap-1 max-w-32'
          >
            Login
            <span className='text-xs text-red-500'>*</span>
          </label>
          <div className='w-full flex flex-col basis-3/4'>
            <Input
              id='trading-account-login'
              placeholder='Trading Account number'
              value={login}
              onChange={e => setLogin(e.target.value)}
              disabled={isPending}
            />
          </div>
        </div>

        <div className='flex my-2 items-baseline flex-wrap lg:flex-nowrap gap-2.5'>
          <label
            htmlFor='trading-account-password'
            className='form-label text-sm flex basis-1/4 items-center gap-1 max-w-32'
          >
            Password
            <span className='text-xs text-red-500'>*</span>
          </label>
          <div className='w-full flex flex-col basis-3/4'>
            <Input
              id='trading-account-password'
              type='password'
              placeholder='Your trading account password'
              value={password}
              onChange={e => setPassword(e.target.value)}
              disabled={isPending}
            />
          </div>
        </div>

        <div className='flex my-2 items-baseline flex-wrap lg:flex-nowrap gap-2.5'>
          <label
            htmlFor='trading-server-name'
            className='form-label text-sm flex basis-1/4 items-center gap-1 max-w-32'
          >
            Server Name
            <span className='text-xs text-red-500'>*</span>
          </label>
          <div className='w-full flex flex-col basis-3/4'>
            <Input
              id='trading-server-name'
              placeholder='Trading account server name'
              value={serverName}
              onChange={e => setServerName(e.target.value)}
              disabled={isPending}
            />
            <Button onClick={handleSubmit} disabled={isPending} className='mt-3 ml-auto'>
              {isPending ? 'Submitting...' : 'Submit'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginAccount;
