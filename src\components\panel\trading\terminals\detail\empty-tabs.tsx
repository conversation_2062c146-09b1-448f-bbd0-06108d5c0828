import Image from 'next/image';
import Link from 'next/link';
import * as React from 'react';

const EmptyTab = () => {
  return (
    <div className='w-full flex justify-center items-center flex-col mt-20'>
      <Image
        alt='there is no data here'
        height={200}
        width={250}
        src='https://v2.tradevps.net/assets/media/illustrations/14.svg'
      />
      <h1 className='text-2xl mt-5'>It seems there's no data at the moment, no need to worry.</h1>
      <p className='mt-3'>
        If you think this is a mistake, we’re always here to help.
        {' '}
        <Link className='text-sm text-blue-400' href='/'>
          Get Support
        </Link>
        {' '}
      </p>
    </div>
  );
};

export default EmptyTab;
