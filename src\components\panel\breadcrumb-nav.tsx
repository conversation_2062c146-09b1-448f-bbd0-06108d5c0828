'use client';

import Link from 'next/link';
import { Fragment } from 'react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { usePathname } from '@/libs/i18nNavigation';

export function BreadcrumbNav() {
  const pathname = usePathname();
  const paths = pathname
    .split('/')
    .filter(Boolean)
    .filter(path => path !== 'panel' && path !== 'detail');

  const generateBreadcrumbItems = () => {
    return paths.map((path, index) => {
      const href = `/panel/${paths.slice(0, index + 1).join('/')}`;
      const isLast = index === paths.length - 1;
      const formattedPath = path
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      if (isLast) {
        return (
          <BreadcrumbItem key={path}>
            <BreadcrumbPage>{formattedPath}</BreadcrumbPage>
          </BreadcrumbItem>
        );
      }

      return (
        <Fragment key={path}>
          <BreadcrumbItem className='hidden md:block'>
            <BreadcrumbLink asChild>
              <Link href={href}>{formattedPath}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className='hidden md:block' />
        </Fragment>
      );
    });
  };

  return (
    <div className='w-full bg-background border-b border-border/20'>
      <div className='container mx-auto px-4 sm:px-6 lg:px-8 h-10 flex items-center'>
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className='hidden md:block'>
              <BreadcrumbLink asChild>
                <Link href='/panel'>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {paths.length > 0 && <BreadcrumbSeparator className='hidden md:block' />}
            {generateBreadcrumbItems()}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </div>
  );
}
