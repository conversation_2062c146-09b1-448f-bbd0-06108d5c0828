'use client';
import type { APIError } from '@tradevpsnet/client';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from 'axios';
import { format } from 'date-fns';
import {
  AlertTriangle,
  ArrowUpDown,
  CheckCircle,
  Cpu,
  Download,
  Eye,
  EyeOff,
  Globe,
  HardDrive,
  Key,
  Lock,
  MemoryStick,
  Monitor,
  Network,
  Power,
  PowerOff,
  RefreshCw,
  Save,
  Shield,
  Trash2
} from 'lucide-react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { CopyableIP } from '@/components/copyable-ip';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import { showRegion } from '@/components/regions';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

import { IMAGE_URL } from '@/configs/image-url';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useWindowsServerAction } from '@/services/api/windows/use-server-action-mutate';
import { useDeleteWindowsServerMutation, useWindowsServerDetailFetch } from '@/services/api/windows/use-server-fetch';
import { useWindowsServerPasswordChange } from '@/services/api/windows/use-server-password-mutate';
import { useWindowsServerScale } from '@/services/api/windows/use-server-scale-mutate';
import { cloudProviderMap, regionMap, serverStatusMap, windowsVersionMap } from '../list-map';

const ServerDetailComponent = () => {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  const { data: serverData, isLoading, refetch } = useWindowsServerDetailFetch(id);

  const [activeTab, setActiveTab] = useState('overview');
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [cpu, setCpu] = useState<number>(2);
  const [memory, setMemory] = useState<number>(4);
  const [isDownloading, setIsDownloading] = useState(false);
  useEffect(() => {
    if (serverData) {
      setCpu(serverData.data.cpu ?? 2);
      setMemory(serverData.data.memory ?? 4);
    }
  }, [serverData]);
  // Add this mutation hook
  const { mutate: scaleServer, isPending: isScaling } = useWindowsServerScale(id, {
    onSuccess: () => {
      toast.success('Server scaling applied successfully');
      refetch(); // Refetch server details to update the UI
    },
    onError: (error: APIError) => {
      toast.error(error.message || 'Failed to scale server resources');
    }
  });

  const [showPassword, setShowPassword] = useState({
    account_password: false,
    new_password: false,
    confirm_password: false
  });

  // Delete server mutation
  const { mutate: deleteServer, isPending: isDeleting } = useDeleteWindowsServerMutation(id, {
    onSuccess: () => {
      toast.success('Server deleted successfully');
      router.push(PAGE_ROUTES.PANEL_TRADING_WINDOWS);
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Failed to delete server');
    }
  });

  const handleDeleteServer = () => {
    setOpenDeleteDialog(true);
  };

  const confirmDelete = () => {
    deleteServer();
  };

  // Password change form schema
  const passwordFormSchema = z
    .object({
      account_password: z.string().min(1, 'Account password is required'),
      new_password: z
        .string()
        .min(12, 'Password must be at least 12 characters long')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/\d/, 'Password must contain at least one number')
        .regex(/[@#$!%*?&]/, 'Password must contain at least one special character (@, #, $, !, %, *, ?, &)'),
      confirm_password: z.string().min(1, 'Please confirm your password')
    })
    .refine(data => data.new_password === data.confirm_password, {
      message: 'Passwords do not match',
      path: ['confirm_password']
    });

  // Password change form
  const passwordForm = useForm<z.infer<typeof passwordFormSchema>>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      account_password: '',
      new_password: '',
      confirm_password: ''
    }
  });

  const { mutate: changePassword } = useWindowsServerPasswordChange(id as string, {
    onSuccess: () => {
      toast.success('Password changed successfully');
      setIsChangingPassword(false);
      passwordForm.reset();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to change password');
      setIsChangingPassword(false);
    }
  });

  const onPasswordSubmit = (values: z.infer<typeof passwordFormSchema>) => {
    setIsChangingPassword(true);
    changePassword({
      account_password: values.account_password,
      new_password: values.new_password
    });
  };

  const { mutate: performAction, isPending: isActionLoading } = useWindowsServerAction(id, {
    onSuccess: () => {
      toast.success('Action triggered successfully');
      // Refetch server details to update status
      refetch();
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Failed to perform action. Please try again.');
    }
  });

  const handleServerAction = (action: 'start' | 'stop' | 'restart') => {
    performAction({ action });
  };

  const formatDate = (dateString: string) => {
    return dateString ? format(new Date(dateString), 'PPpp') : 'N/A';
  };

  const getProviderName = (providerId: number) => {
    return cloudProviderMap[providerId]?.label || 'Unknown';
  };

  const getProviderInfo = (providerId: number) => {
    return cloudProviderMap[providerId] || { label: 'Unknown', description: '', icon: undefined };
  };

  const getVersionName = (versionId: number) => {
    return windowsVersionMap[versionId]?.label || 'Unknown';
  };

  const getRegionName = (regionId: number) => {
    return regionMap[regionId]?.label || 'Unknown';
  };

  const getRegionInfo = (regionId: number) => {
    return showRegion(regionId);
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-[80vh]'>
        <div className='relative'>
          <div className='absolute inset-0 rounded-full blur-xl bg-primary/20 animate-pulse'></div>
          <div className='relative animate-spin rounded-full h-16 w-16 border-2 border-background border-t-primary border-r-primary'></div>
        </div>
      </div>
    );
  }

  // Get the correct status color based on the server status
  const getStatusColor = (status: number) => {
    // Check if the status exists in serverStatusMap
    const statusInfo = serverStatusMap[status];

    if (statusInfo) {
      // Map the variant to a color
      switch (statusInfo.variant) {
        case 'success_light':
          return 'green';
        case 'warning_light':
          return 'amber';
        case 'danger_light':
          return 'red';
        case 'primary_light':
          return 'blue';
        default:
          return 'gray';
      }
    }

    return 'gray'; // Default color if status not found
  };

  const statusColor = getStatusColor(serverData?.data.status || 0);

  return (
    <div className='max-w-[1400px] mx-auto'>
      {/* Ultra-Premium Hero Section */}
      <div className='relative overflow-hidden rounded-2xl mb-8 bg-gradient-to-br from-background via-background/95 to-background/90'>
        <div className='absolute inset-0 bg-grid-primary/5'></div>
        <div className='absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl opacity-30'></div>
        <div className='absolute bottom-0 left-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl opacity-30'></div>

        <div className='relative z-10 p-8'>
          <div className='flex flex-col md:flex-row justify-between items-start md:items-center gap-6'>
            <div className='flex items-center gap-4'>
              <div className='relative'>
                <div className='absolute inset-0 rounded-xl blur-md bg-primary/5'></div>
                <div className='relative flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/10'>
                  <Image src={IMAGE_URL.WINDOWS_SERVER} alt='Windows Server' width={48} height={48} />
                </div>
              </div>
              <div>
                <div className='flex items-center gap-3'>
                  <h1 className='text-3xl font-bold'>{serverData?.data.name}</h1>
                  <div
                    className={`px-2.5 py-0.5 rounded-full bg-${statusColor}-500/10 border border-${statusColor}-500/20 text-${statusColor}-500 text-xs font-medium flex items-center gap-1.5`}
                  >
                    <span className={`w-1.5 h-1.5 rounded-full bg-${statusColor}-500 animate-pulse`}></span>
                    {serverData?.data.status !== undefined ? serverStatusMap[serverData.data.status]?.label : 'Unknown'}
                  </div>
                </div>
                <div className='flex items-center gap-3 mt-1 text-sm text-muted-foreground'>
                  <div className='flex items-center gap-1.5'>
                    <Globe className='w-3.5 h-3.5' />
                    <CopyableIP ip={serverData?.data.ip} />
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Professional Action Panel */}
            <div className='relative'>
              {/* Background Glow Effect */}
              <div className='absolute -inset-2 bg-gradient-to-r from-primary/10 via-transparent to-primary/10 rounded-2xl blur-xl opacity-50'></div>

              <div className='relative'>
                <div className='flex flex-col lg:flex-row gap-3'>
                  {/* Primary Actions Group */}
                  <div className='flex flex-wrap gap-2'>
                    {/* Start Button */}
                    <Button
                      onClick={() => handleServerAction('start')}
                      disabled={isActionLoading || serverData?.data.status === 6}
                      className='relative bg-zinc-100 text-zinc-900 hover:bg-zinc-200 border border-zinc-300 shadow-sm flex items-center gap-2 h-9 px-3 rounded-md disabled:opacity-50 disabled:cursor-not-allowed'
                    >
                      <div className='w-5 h-5 rounded-full bg-green-500 flex items-center justify-center'>
                        <Power className='w-3 h-3 text-white' />
                      </div>
                      <span className='text-sm font-medium'>{isActionLoading ? 'Processing...' : 'Start'}</span>
                    </Button>

                    {/* Stop Button */}
                    <Button
                      onClick={() => handleServerAction('stop')}
                      disabled={isActionLoading || serverData?.data.status === 7}
                      className='relative bg-zinc-100 text-zinc-900 hover:bg-zinc-200 border border-zinc-300 shadow-sm flex items-center gap-2 h-9 px-3 rounded-md disabled:opacity-50 disabled:cursor-not-allowed'
                    >
                      <div className='w-5 h-5 rounded-full bg-red-500 flex items-center justify-center'>
                        <PowerOff className='w-3 h-3 text-white' />
                      </div>
                      <span className='text-sm font-medium'>{isActionLoading ? 'Processing...' : 'Stop'}</span>
                    </Button>

                    {/* Restart Button */}
                    <Button
                      onClick={() => handleServerAction('restart')}
                      disabled={isActionLoading}
                      className='relative bg-zinc-100 text-zinc-900 hover:bg-zinc-200 border border-zinc-300 shadow-sm flex items-center gap-2 h-9 px-3 rounded-md'
                    >
                      <div className='w-5 h-5 rounded-full bg-amber-500 flex items-center justify-center'>
                        <RefreshCw className='w-3 h-3 text-white' />
                      </div>
                      <span className='text-sm font-medium'>{isActionLoading ? 'Processing...' : 'Restart'}</span>
                    </Button>
                  </div>

                  {/* Separator */}
                  <div className='hidden lg:block w-px bg-border/40 mx-2'></div>
                  <div className='lg:hidden h-px bg-border/40 my-1'></div>

                  {/* Secondary Actions Group */}
                  <div className='flex flex-wrap gap-2'>
                    {/* Connect Button */}
                    <Button
                      size='sm'
                      className='relative bg-zinc-100 text-zinc-900 hover:bg-zinc-200 border border-zinc-300 shadow-sm flex items-center gap-2 h-9 px-3 rounded-md'
                    >
                      <div className='w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center'>
                        <Monitor className='w-3 h-3 text-white' />
                      </div>
                      <span className='text-sm font-medium'>Connect</span>
                    </Button>
                  </div>
                </div>

                {/* Loading Overlay */}
                {isActionLoading && (
                  <div className='absolute inset-0 bg-background/50 backdrop-blur-sm rounded-2xl flex items-center justify-center'>
                    <div className='flex items-center gap-3 px-4 py-2 bg-background/80 rounded-lg border border-border/40 shadow-lg'>
                      <div className='w-4 h-4 border-2 border-primary/20 border-t-primary rounded-full animate-spin'></div>
                      <span className='text-sm font-medium text-muted-foreground'>Processing action...</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Ultra-Premium Stat Cards - Exceptional Design */}
          <div className='grid grid-cols-2 sm:grid-cols-4 gap-6 mt-8'>
            <div className='group relative isolate overflow-hidden rounded-2xl backdrop-blur-sm'>
              <div className='absolute inset-0 bg-gradient-to-br from-primary/5 via-background/95 to-background/90 opacity-90'></div>
              <div className='absolute inset-0 bg-grid-primary/5 mask-gradient-faded'></div>
              <div className='absolute -inset-0.5 bg-gradient-to-br from-primary/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-sm'></div>
              <div className='absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full blur-3xl opacity-70 group-hover:opacity-90 transition-opacity'></div>
              <div className='absolute -top-6 -left-6 opacity-10'>
                <Cpu className='w-24 h-24 text-primary' />
              </div>

              <div className='relative z-10 p-6 border border-primary/15 rounded-2xl bg-background/30 backdrop-blur-sm shadow-xl'>
                <div>
                  <p className='text-sm font-medium text-muted-foreground mb-1'>Processor</p>
                  <div className='flex items-baseline'>
                    <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80'>
                      {serverData?.data.cpu || 'N/A'}
                    </p>
                    <p className='text-sm text-muted-foreground ml-2'>vCPUs</p>
                  </div>
                </div>
              </div>
            </div>

            <div className='group relative isolate overflow-hidden rounded-2xl backdrop-blur-sm'>
              <div className='absolute inset-0 bg-gradient-to-br from-blue-500/5 via-background/95 to-background/90 opacity-90'></div>
              <div className='absolute inset-0 bg-grid-primary/5 mask-gradient-faded'></div>
              <div className='absolute -inset-0.5 bg-gradient-to-br from-blue-500/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-sm'></div>
              <div className='absolute top-0 right-0 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl opacity-70 group-hover:opacity-90 transition-opacity'></div>
              <div className='absolute -top-6 -left-6 opacity-10'>
                <MemoryStick className='w-24 h-24 text-blue-500' />
              </div>

              <div className='relative z-10 p-6 border border-blue-500/15 rounded-2xl bg-background/30 backdrop-blur-sm shadow-xl'>
                <div>
                  <p className='text-sm font-medium text-muted-foreground mb-1'>Memory</p>
                  <div className='flex items-baseline'>
                    <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80'>
                      {serverData?.data.memory || 'N/A'}
                    </p>
                    <p className='text-sm text-muted-foreground ml-2'>GB RAM</p>
                  </div>
                </div>
              </div>
            </div>

            <div className='group relative isolate overflow-hidden rounded-2xl backdrop-blur-sm'>
              <div className='absolute inset-0 bg-gradient-to-br from-amber-500/5 via-background/95 to-background/90 opacity-90'></div>
              <div className='absolute inset-0 bg-grid-primary/5 mask-gradient-faded'></div>
              <div className='absolute -inset-0.5 bg-gradient-to-br from-amber-500/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-sm'></div>
              <div className='absolute top-0 right-0 w-32 h-32 bg-amber-500/10 rounded-full blur-3xl opacity-70 group-hover:opacity-90 transition-opacity'></div>
              <div className='absolute -top-6 -left-6 opacity-10'>
                <HardDrive className='w-24 h-24 text-amber-500' />
              </div>

              <div className='relative z-10 p-6 border border-amber-500/15 rounded-2xl bg-background/30 backdrop-blur-sm shadow-xl'>
                <div>
                  <p className='text-sm font-medium text-muted-foreground mb-1'>Storage</p>
                  <div className='flex items-baseline'>
                    <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80'>
                      {serverData?.data.disk || 'N/A'}
                    </p>
                    <p className='text-sm text-muted-foreground ml-2'>GB SSD</p>
                  </div>
                </div>
              </div>
            </div>

            <div className='group relative isolate overflow-hidden rounded-2xl backdrop-blur-sm'>
              <div className='absolute inset-0 bg-gradient-to-br from-green-500/5 via-background/95 to-background/90 opacity-90'></div>
              <div className='absolute inset-0 bg-grid-primary/5 mask-gradient-faded'></div>
              <div className='absolute -inset-0.5 bg-gradient-to-br from-green-500/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-sm'></div>
              <div className='absolute top-0 right-0 w-32 h-32 bg-green-500/10 rounded-full blur-3xl opacity-70 group-hover:opacity-90 transition-opacity'></div>
              <div className='absolute -top-6 -left-6 opacity-10'>
                <Globe className='w-24 h-24 text-green-500' />
              </div>

              <div className='relative z-10 p-6 border border-green-500/15 rounded-2xl bg-background/30 backdrop-blur-sm shadow-xl'>
                <div>
                  <p className='text-sm font-medium text-muted-foreground mb-1'>Region</p>
                  <div className='flex items-center'>
                    {serverData?.data.region !== undefined && (
                      <Image
                        width={24}
                        height={24}
                        src={getRegionInfo(serverData.data.region).icon}
                        alt={getRegionName(serverData.data.region)}
                        className='w-6 h-6 rounded-full object-cover border border-border/30 mr-2'
                      />
                    )}
                    <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80'>
                      {serverData?.data.region !== undefined ? getRegionName(serverData.data.region) : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* New Layout with Vertical Navigation */}
      <div className='grid grid-cols-1 md:grid-cols-[220px_1fr] gap-6'>
        {/* Vertical Navigation */}
        <div className='p-3 rounded-lg border border-border/30 bg-background/50 backdrop-blur-sm'>
          <div className='text-xs font-medium text-muted-foreground mb-3 px-1.5'>SERVER MANAGEMENT</div>

          <div className='space-y-1 divide-y divide-border/10'>
            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'overview'}
              onClick={() => setActiveTab('overview')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.WINDOWS_SERVER} alt='Overview' width={20} height={20} />
              </div>
              <span>Overview</span>
            </Button>

            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground pt-1 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'connect'}
              onClick={() => setActiveTab('connect')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_CONNECT} alt='Connect' width={20} height={20} />
              </div>
              <span>Connect</span>
            </Button>

            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground pt-1 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'disk'}
              onClick={() => setActiveTab('disk')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_DISK} alt='Disk' width={20} height={20} />
              </div>
              <span>Disk</span>
              <div className='ml-auto'>
                <span className='inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium tracking-wide bg-amber-50/80 text-amber-600 border border-amber-200/40 backdrop-blur-sm transition-all duration-200 hover:bg-amber-100/60 hover:border-amber-300/50'>
                  BETA
                </span>
              </div>
            </Button>

            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground pt-1 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'network'}
              onClick={() => setActiveTab('network')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_NETWORK} alt='Network' width={20} height={20} />
              </div>
              <span>Network</span>
              <div className='ml-auto'>
                <span className='inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium tracking-wide bg-amber-50/80 text-amber-600 border border-amber-200/40 backdrop-blur-sm transition-all duration-200 hover:bg-amber-100/60 hover:border-amber-300/50'>
                  BETA
                </span>
              </div>
            </Button>

            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground pt-1 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'scale'}
              onClick={() => setActiveTab('scale')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_SCALE} alt='Scale' width={20} height={20} />
              </div>
              <span>Scale</span>
              <div className='ml-auto'>
                <span className='inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium tracking-wide bg-amber-50/80 text-amber-600 border border-amber-200/40 backdrop-blur-sm transition-all duration-200 hover:bg-amber-100/60 hover:border-amber-300/50'>
                  BETA
                </span>
              </div>
            </Button>

            <Button
              variant='ghost'
              className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-foreground pt-1 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-all'
              data-active={activeTab === 'password'}
              onClick={() => setActiveTab('password')}
            >
              <div className='w-5 h-5 mr-2.5 opacity-80'>
                <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_PASSWORD} alt='Password' width={20} height={20} />
              </div>
              <span>Change Password</span>
            </Button>
          </div>

          <Separator className='my-3 opacity-50' />

          <Button
            variant='ghost'
            className='w-full justify-start h-9 px-3 rounded-md text-muted-foreground hover:text-destructive data-[active=true]:text-destructive-foreground data-[active=true]:bg-destructive/10 transition-all'
            onClick={() => setActiveTab('delete')}
            data-active={activeTab === 'delete'}
          >
            <div className='w-5 h-5 mr-2.5 opacity-80'>
              <Image src={IMAGE_URL.PANEL_TRADING_WINDOWS_SERVERS_TRASH} alt='Delete' width={20} height={20} />
            </div>
            <span>Delete</span>
          </Button>
        </div>

        {/* Content Area */}
        <div>
          {activeTab === 'overview' && (
            <div className='space-y-6'>
              {/* Server Information Card */}
              <Card className='overflow-hidden border-border/30 shadow-sm'>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-lg font-medium'>Server Information</CardTitle>
                  <CardDescription>Details about your Windows Server instance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div className='space-y-4'>
                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Name</div>
                        <div className='font-medium'>{serverData?.data.name || 'N/A'}</div>
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Status</div>
                        <div className='flex items-center gap-2'>
                          <div className={`w-2 h-2 rounded-full bg-${statusColor}-500`}></div>
                          <span>
                            {serverData?.data.status !== undefined
                              ? serverStatusMap[serverData.data.status]?.label
                              : 'Unknown'}
                          </span>
                        </div>
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>IP Address</div>
                        <CopyableIP ip={serverData?.data.ip} className='text-blue-600' />
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Windows Version</div>
                        <div>
                          {serverData?.data.version !== undefined ? getVersionName(serverData.data.version) : 'N/A'}
                        </div>
                      </div>
                    </div>

                    <div className='space-y-4'>
                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Region</div>
                        <div className='flex items-center gap-2'>
                          {serverData?.data.region !== undefined && (
                            <Image
                              width={16}
                              height={16}
                              src={getRegionInfo(serverData.data.region).icon}
                              alt={getRegionName(serverData.data.region)}
                              className='w-4 h-4 rounded-full object-cover'
                            />
                          )}
                          <span>
                            {serverData?.data.region !== undefined ? getRegionName(serverData.data.region) : 'N/A'}
                          </span>
                        </div>
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Provider</div>
                        <div className='flex items-center gap-1'>
                          {serverData?.data.provider !== undefined
                            && getProviderInfo(serverData.data.provider).icon && (
                            <Image
                              width={16}
                              height={16}
                              src={getProviderInfo(serverData.data.provider).icon || ''}
                              alt={getProviderName(serverData.data.provider)}
                              className='w-4 h-4 object-contain'
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          )}
                          <span>
                            {serverData?.data.provider !== undefined
                              ? getProviderName(serverData.data.provider)
                              : 'N/A'}
                          </span>
                        </div>
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Created At</div>
                        <div className='text-sm'>
                          {serverData?.data.created_at ? formatDate(serverData.data.created_at) : 'N/A'}
                        </div>
                      </div>

                      <div className='space-y-1.5'>
                        <div className='text-sm font-medium text-muted-foreground'>Last Updated</div>
                        <div className='text-sm'>
                          {serverData?.data.updated_at ? formatDate(serverData.data.updated_at) : 'N/A'}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'connect' && (
            <div className='relative'>
              {/* Premium Background Effects */}
              <div className='absolute inset-0 bg-gradient-to-br from-blue-50/30 via-indigo-50/20 to-cyan-50/30 dark:from-blue-950/20 dark:via-indigo-950/10 dark:to-cyan-950/20 rounded-2xl blur-3xl'></div>
              <div className='absolute top-4 right-4 w-32 h-32 bg-blue-500/5 rounded-full blur-2xl'></div>

              <Card className='relative overflow-hidden border-border/20 shadow-xl shadow-blue-500/5 backdrop-blur-sm bg-background/95 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-500'>
                {/* Premium Header Gradient */}
                <div className='h-1 w-full bg-gradient-to-r from-blue-400 via-indigo-500 to-cyan-400'></div>

                <CardHeader className='pb-6 relative'>
                  {/* Decorative Background Icon */}
                  <div className='absolute -top-4 -right-4 opacity-[0.03] dark:opacity-[0.08]'>
                    <Monitor className='w-32 h-32 text-blue-500 rotate-12' />
                  </div>

                  <div className='relative z-10'>
                    <CardTitle className='flex items-center gap-3 text-xl font-semibold mb-2'>
                      <div className='p-2.5 rounded-xl bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border border-blue-200/20 shadow-sm'>
                        <Monitor className='w-5 h-5 text-blue-600' />
                      </div>
                      <span className='bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent'>
                        Remote Connection
                      </span>
                    </CardTitle>
                    <CardDescription className='text-sm text-muted-foreground/80 ml-11'>
                      Secure remote access to your Windows Server environment
                    </CardDescription>
                  </div>
                </CardHeader>

                <CardContent className='space-y-6 pb-8'>
                  {/* Connection Details Grid */}
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* IP Address Section */}
                    <div className='relative group'>
                      <div className='absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl blur-sm group-hover:blur-none transition-all duration-300'></div>
                      <div className='relative p-4 rounded-xl border border-blue-200/30 bg-gradient-to-br from-blue-50/50 to-indigo-50/30 dark:from-blue-950/30 dark:to-indigo-950/20 backdrop-blur-sm'>
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center gap-3'>
                            <div className='p-2 rounded-lg bg-blue-500/10 border border-blue-200/30'>
                              <Network className='w-4 h-4 text-blue-600' />
                            </div>
                            <div>
                              <p className='text-sm font-semibold text-foreground'>Server IP Address</p>
                              <p className='text-xs text-muted-foreground'>Primary connection endpoint</p>
                            </div>
                          </div>
                          <div className='flex items-center gap-2'>
                            <CopyableIP ip={serverData?.data.ip} showIcon={true} variant='outline' />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Username Section */}
                    <div className='relative group'>
                      <div className='absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl blur-sm group-hover:blur-none transition-all duration-300'></div>
                      <div className='relative p-4 rounded-xl border border-blue-200/30 bg-gradient-to-br from-blue-50/50 to-indigo-50/30 dark:from-blue-950/30 dark:to-indigo-950/20 backdrop-blur-sm'>
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center gap-3'>
                            <div className='p-2 rounded-lg bg-blue-500/10 border border-blue-200/30'>
                              <Key className='w-4 h-4 text-blue-600' />
                            </div>
                            <div>
                              <p className='text-sm font-semibold text-foreground'>Admin Username</p>
                              <p className='text-xs text-muted-foreground'>Administrator account</p>
                            </div>
                          </div>
                          <div className='flex items-center gap-2'>
                            <button
                              type='button'
                              onClick={() => {
                                const username = serverData?.data.admin_username || 'Administrator';
                                navigator.clipboard.writeText(username);
                                toast.success('Username copied to clipboard');
                              }}
                              className='group/copy inline-flex items-center gap-2 px-3 py-1.5 text-sm font-mono bg-background border border-border rounded-md hover:bg-muted transition-colors'
                            >
                              <span className='text-foreground'>
                                {serverData?.data.admin_username || 'Administrator'}
                              </span>
                              <div className='opacity-60 group-hover/copy:opacity-100 transition-opacity duration-200'>
                                <svg
                                  className='w-3.5 h-3.5 text-muted-foreground'
                                  fill='none'
                                  stroke='currentColor'
                                  viewBox='0 0 24 24'
                                >
                                  <path
                                    strokeLinecap='round'
                                    strokeLinejoin='round'
                                    strokeWidth={2}
                                    d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'
                                  />
                                </svg>
                              </div>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Premium RDP Download */}
                  <div className='space-y-8'>
                    {/* Premium Header */}
                    <div className='text-center space-y-4'>
                      <div className='inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 border border-blue-200/30 shadow-lg backdrop-blur-sm'>
                        <Monitor className='w-7 h-7 text-blue-600' />
                      </div>
                      <div className='space-y-2'>
                        <h3 className='text-2xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent'>
                          Remote Desktop Connection
                        </h3>
                        <p className='text-muted-foreground max-w-md mx-auto leading-relaxed'>
                          Download your personalized RDP configuration file with optimized settings for seamless
                          connectivity
                        </p>
                      </div>
                    </div>

                    {/* Premium Download Card */}
                    <div className='max-w-2xl mx-auto'>
                      <div className='relative group'>
                        {/* Premium Glow Effect */}
                        <div className='absolute -inset-0.5 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-2xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-500'></div>

                        {/* Main Premium Card */}
                        <div className='relative bg-gradient-to-br from-white via-blue-50/50 to-indigo-50/30 dark:from-gray-900 dark:via-blue-950/30 dark:to-indigo-950/20 rounded-2xl border border-white/20 backdrop-blur-xl overflow-hidden'>
                          {/* Premium Content */}
                          <div className='p-8'>
                            {/* Ultra Premium Download Button */}
                            <div className='relative group/download'>
                              {/* Advanced Glow Effects */}
                              <div className='absolute -inset-2 bg-gradient-to-r from-blue-500/30 via-indigo-500/40 to-purple-500/30 rounded-2xl blur-xl opacity-0 group-hover/download:opacity-100 transition-all duration-700'></div>
                              <div className='absolute -inset-1 bg-gradient-to-r from-blue-400/20 via-indigo-400/30 to-purple-400/20 rounded-xl blur-lg opacity-50 group-hover/download:opacity-100 transition-all duration-500'></div>

                              <Button
                                onClick={async () => {
                                  try {
                                    setIsDownloading(true);
                                    const response = await axios.post(
                                      '/api/rdp',
                                      {
                                        address: serverData?.data.ip,
                                        username: serverData?.data.admin_username
                                      },
                                      { responseType: 'blob' }
                                    );

                                    const url = window.URL.createObjectURL(response.data);
                                    const a = document.createElement('a');
                                    a.href = url;
                                    const serverName = serverData?.data.name || serverData?.data.ip || 'server';
                                    a.download = `${serverName}.rdp`;
                                    document.body.appendChild(a);
                                    a.click();
                                    a.remove();
                                    window.URL.revokeObjectURL(url);
                                    toast.success('RDP file downloaded successfully');
                                  } catch (error) {
                                    toast.error('Failed to download RDP file', {
                                      description: error instanceof Error ? error.message : 'Unknown error'
                                    });
                                  } finally {
                                    setIsDownloading(false);
                                  }
                                }}
                                disabled={isDownloading}
                                className='relative w-full h-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 hover:from-slate-800 hover:via-blue-800 hover:to-indigo-800 text-white border border-blue-500/20 hover:border-blue-400/40 rounded-2xl shadow-lg shadow-blue-900/20 hover:shadow-xl hover:shadow-indigo-900/25 transition-all duration-500 hover:scale-[1.01] group/btn overflow-hidden backdrop-blur-sm'
                              >
                                {/* Animated Background Layers */}
                                <div className='absolute inset-0 bg-gradient-to-r from-blue-600/10 via-indigo-600/20 to-purple-600/10 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-700'></div>
                                <div className='absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-1000 delay-200'></div>

                                {/* Shimmer Effect */}
                                <div className='absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover/btn:opacity-100 group-hover/btn:translate-x-full transition-all duration-1000 delay-300'></div>

                                {/* Premium Content */}
                                <div className='relative flex items-center justify-between w-full px-8'>
                                  {/* Left Side - Icon & Primary Text */}
                                  <div className='flex items-center gap-6'>
                                    {/* Ultra Premium Icon */}
                                    <div className='relative'>
                                      {/* Icon Glow */}
                                      <div className='absolute inset-0 bg-blue-400/30 rounded-2xl blur-lg group-hover/btn:bg-indigo-400/40 transition-all duration-500'></div>
                                      <div className='relative p-4 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 border border-white/20 group-hover/btn:from-white/20 group-hover/btn:to-white/10 group-hover/btn:border-white/30 transition-all duration-500 group-hover/btn:scale-110 group-hover/btn:rotate-3'>
                                        <Download className='w-8 h-8 group-hover/btn:scale-110 transition-transform duration-500' />
                                      </div>
                                    </div>

                                    {/* Premium Text Content */}
                                    <div className='text-left space-y-2'>
                                      <div className='text-2xl font-bold tracking-wide group-hover/btn:tracking-wider transition-all duration-300'>
                                        Download RDP File
                                      </div>
                                      <div className='text-blue-200/80 group-hover/btn:text-blue-100 font-medium transition-colors duration-300'>
                                        Pre-configured • Secure • Ready to use
                                      </div>
                                    </div>
                                  </div>

                                  {/* Right Side - Status & Arrow */}
                                  <div className='flex items-center gap-4'>
                                    {/* Status Indicator */}
                                    <div className='flex items-center gap-2'>
                                      <div className='w-2 h-2 rounded-full bg-green-400 animate-pulse'></div>
                                      <span className='text-sm text-green-300 font-medium'>Ready</span>
                                    </div>

                                    {/* Arrow or Spinner */}
                                    <div className='p-3 rounded-xl bg-white/5 border border-white/10 group-hover/btn:bg-white/10 group-hover/btn:border-white/20 transition-all duration-300'>
                                      {isDownloading ? (
                                        <div className='w-6 h-6 border-2 border-white/10 border-t-white rounded-full animate-spin'></div>
                                      ) : (
                                        <svg
                                          className='w-6 h-6 group-hover/btn:scale-110 transition-transform duration-300'
                                          fill='none'
                                          stroke='currentColor'
                                          viewBox='0 0 24 24'
                                        >
                                          <path
                                            strokeLinecap='round'
                                            strokeLinejoin='round'
                                            strokeWidth={2}
                                            d='M17 8l4 4m0 0l-4 4m4-4H3'
                                          />
                                        </svg>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                {/* Bottom Progress Bar */}
                                <div className='absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 w-0 group-hover/btn:w-full transition-all duration-1000 delay-100'></div>
                              </Button>
                            </div>

                            {/* Premium Features */}
                            <div className='mt-8 pt-8 border-t border-gradient-to-r from-transparent via-blue-200/30 to-transparent'>
                              <div className='grid grid-cols-3 gap-6'>
                                <div className='text-center space-y-3'>
                                  <div className='w-12 h-12 mx-auto rounded-xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-emerald-200/30 flex items-center justify-center'>
                                    <Shield className='w-6 h-6 text-emerald-600' />
                                  </div>
                                  <div className='space-y-1'>
                                    <div className='font-semibold text-sm text-foreground'>Enterprise Security</div>
                                    <div className='text-xs text-muted-foreground'>End-to-end encryption</div>
                                  </div>
                                </div>

                                <div className='text-center space-y-3'>
                                  <div className='w-12 h-12 mx-auto rounded-xl bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border border-blue-200/30 flex items-center justify-center'>
                                    <CheckCircle className='w-6 h-6 text-blue-600' />
                                  </div>
                                  <div className='space-y-1'>
                                    <div className='font-semibold text-sm text-foreground'>Auto-Configuration</div>
                                    <div className='text-xs text-muted-foreground'>Zero setup required</div>
                                  </div>
                                </div>

                                <div className='text-center space-y-3'>
                                  <div className='w-12 h-12 mx-auto rounded-xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-200/30 flex items-center justify-center'>
                                    <Globe className='w-6 h-6 text-purple-600' />
                                  </div>
                                  <div className='space-y-1'>
                                    <div className='font-semibold text-sm text-foreground'>Universal Access</div>
                                    <div className='text-xs text-muted-foreground'>Any RDP client</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'disk' && (
            <div className='relative'>
              <Card className='overflow-hidden border-border/30 shadow-sm min-h-[400px]'>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-lg font-medium'>Disk Management</CardTitle>
                  <CardDescription>Manage storage volumes and disk operations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>{/* Disk content will go here */}</div>
                </CardContent>
              </Card>

              {/* Coming Soon Overlay */}
              <div className='absolute inset-0 backdrop-blur-[2px] bg-background/60 flex flex-col items-center justify-center rounded-lg border border-border/30'>
                <div className='relative'>
                  <div className='absolute -inset-4 bg-primary/10 rounded-full blur-xl opacity-70'></div>
                  <div className='relative bg-background/80 backdrop-blur-sm px-6 py-3 rounded-lg border border-primary/20 shadow-lg'>
                    <div className='flex items-center gap-2'>
                      <div className='w-2 h-2 rounded-full bg-primary animate-pulse'></div>
                      <span className='text-lg font-medium text-primary'>Coming Soon</span>
                    </div>
                    <p className='text-sm text-muted-foreground mt-1'>Disk management features are under development</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'network' && (
            <div className='relative'>
              <Card className='overflow-hidden border-border/30 shadow-sm min-h-[400px]'>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-lg font-medium'>Network Configuration</CardTitle>
                  <CardDescription>Manage network interfaces and connectivity</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>{/* Network content will go here */}</div>
                </CardContent>
              </Card>

              {/* Coming Soon Overlay */}
              <div className='absolute inset-0 backdrop-blur-[2px] bg-background/60 flex flex-col items-center justify-center rounded-lg border border-border/30'>
                <div className='relative'>
                  <div className='absolute -inset-4 bg-primary/10 rounded-full blur-xl opacity-70'></div>
                  <div className='relative bg-background/80 backdrop-blur-sm px-6 py-3 rounded-lg border border-primary/20 shadow-lg'>
                    <div className='flex items-center gap-2'>
                      <div className='w-2 h-2 rounded-full bg-primary animate-pulse'></div>
                      <span className='text-lg font-medium text-primary'>Coming Soon</span>
                    </div>
                    <p className='text-sm text-muted-foreground mt-1'>
                      Network configuration features are under development
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'scale' && (
            <Card className='overflow-hidden border-border/30 shadow-sm hover:shadow-md transition-all'>
              <div className='h-0.5 w-full bg-gradient-to-r from-primary/60 via-primary/80 to-primary/60'></div>
              <CardHeader className='pb-2'>
                <CardTitle className='flex items-center gap-2 text-lg font-medium'>
                  <div className='p-1 rounded-md bg-primary/5 text-primary'>
                    <ArrowUpDown className='w-4 h-4' />
                  </div>
                  Scale Resources
                </CardTitle>
                <CardDescription className='text-xs'>Adjust server resources</CardDescription>
              </CardHeader>
              <CardContent className='space-y-6'>
                {/* CPU Selection with power indicators */}
                <div>
                  <div className='flex items-center space-x-2 border-b border-border/20 pb-2 mb-4'>
                    <div className='bg-primary/10 p-1.5 rounded-md'>
                      <Cpu className='h-4 w-4 text-primary' />
                    </div>
                    <h5 className='text-lg font-medium tracking-tight'>CPU Cores</h5>
                  </div>

                  <div className='flex flex-nowrap gap-3 overflow-x-auto hide-scrollbar pb-2'>
                    {[1, 2, 4, 8, 16, 24, 32].map((cores, index) => {
                      // Calculate opacity based on power (higher cores = higher opacity)
                      const powerOpacity = 0.3 + (index / 6) * 0.7; // 0.3 to 1.0
                      const isSelected = cpu === cores;

                      return (
                        <div
                          key={cores}
                          role='button'
                          tabIndex={0}
                          onClick={() => setCpu(cores)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              setCpu(cores);
                            }
                          }}
                          aria-pressed={isSelected}
                          aria-label={`Select ${cores} CPU core${cores > 1 ? 's' : ''}`}
                          className={`relative rounded-md p-3 cursor-pointer transition-all duration-500
                          flex flex-col items-center gap-2 border text-center flex-shrink-0 w-[100px]
                          ${isSelected ? 'border-primary/40 bg-primary/[0.03]' : 'border-border/30 hover:border-primary/30 hover:bg-background/60'}
                          focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2
                        `}
                          style={{ '--power-level': powerOpacity } as React.CSSProperties}
                        >
                          <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/30 bg-background/80 transition-all duration-300'>
                            <Cpu
                              className='h-5 w-5 text-primary transition-all duration-300'
                              style={{ opacity: powerOpacity }}
                            />
                          </div>
                          <div className='font-medium'>
                            {cores}
                            {' '}
                            vCPU
                            {cores > 1 ? 's' : ''}
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            {cores <= 2 ? 'Basic' : cores <= 8 ? 'Standard' : 'Premium'}
                          </div>
                          <div
                            className='absolute bottom-0 left-0 right-0 h-1 rounded-b-md transition-all duration-300'
                            style={{
                              background: `linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)))`,
                              opacity: isSelected ? 1 : powerOpacity / 2,
                              transform: isSelected ? 'scaleX(1)' : 'scaleX(0.5)',
                              transformOrigin: 'left'
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                  <div className='text-xs text-muted-foreground mt-2 opacity-60'>
                    Recommended: 2-4 cores for most trading applications
                  </div>
                </div>

                {/* Memory Selection with power indicators */}
                <div>
                  <div className='flex items-center space-x-2 border-b border-border/20 pb-2 mb-4'>
                    <div className='bg-primary/10 p-1.5 rounded-md'>
                      <MemoryStick className='h-4 w-4 text-primary' />
                    </div>
                    <h5 className='text-lg font-medium tracking-tight'>Memory (RAM)</h5>
                  </div>

                  <div className='flex flex-nowrap gap-3 overflow-x-auto hide-scrollbar pb-2'>
                    {[2, 4, 8, 16, 32, 64, 128].map((ram, index) => {
                      // Calculate opacity based on power (higher RAM = higher opacity)
                      const powerOpacity = 0.3 + (index / 6) * 0.7; // 0.3 to 1.0
                      const isSelected = memory === ram;

                      return (
                        <div
                          key={ram}
                          role='button'
                          tabIndex={0}
                          onClick={() => setMemory(ram)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              setMemory(ram);
                            }
                          }}
                          aria-pressed={isSelected}
                          aria-label={`Select ${ram} GB memory`}
                          className={`relative rounded-md p-3 cursor-pointer transition-all duration-500
                          flex flex-col items-center gap-2 border text-center flex-shrink-0 w-[100px]
                          ${isSelected ? 'border-primary/40 bg-primary/[0.03]' : 'border-border/30 hover:border-primary/30 hover:bg-background/60'}
                          focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2
                        `}
                          style={{ '--power-level': powerOpacity } as React.CSSProperties}
                        >
                          <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/30 bg-background/80 transition-all duration-300'>
                            <MemoryStick
                              className='h-5 w-5 text-primary transition-all duration-300'
                              style={{ opacity: powerOpacity }}
                            />
                          </div>
                          <div className='font-medium'>
                            {ram}
                            {' '}
                            GB
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            {ram <= 4 ? 'Basic' : ram <= 16 ? 'Standard' : 'Premium'}
                          </div>
                          <div
                            className='absolute bottom-0 left-0 right-0 h-1 rounded-b-md transition-all duration-300'
                            style={{
                              background: `linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)))`,
                              opacity: isSelected ? 1 : powerOpacity / 2,
                              transform: isSelected ? 'scaleX(1)' : 'scaleX(0.5)',
                              transformOrigin: 'left'
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                  <div className='text-xs text-muted-foreground mt-2 opacity-60'>
                    Recommended: 4-8 GB for most trading platforms
                  </div>
                </div>

                <div className='pt-4 flex justify-end'>
                  <Button
                    className='min-w-[120px] bg-primary hover:bg-primary/90 text-primary-foreground'
                    disabled={isScaling || cpu === null || memory === null}
                    onClick={() => {
                      if (cpu === null || memory === null) {
                        return;
                      }
                      scaleServer({ cpu, memory });
                    }}
                  >
                    {isScaling ? (
                      <p>Scaling...</p>
                    ) : (
                      <>
                        <Save className='w-3.5 h-3.5 mr-1.5' />
                        Apply Changes
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === 'delete' && (
            <div className='relative'>
              {/* Premium Background Effects */}
              <div className='absolute inset-0 bg-gradient-to-br from-red-50/30 via-orange-50/20 to-pink-50/30 dark:from-red-950/20 dark:via-orange-950/10 dark:to-pink-950/20 rounded-2xl blur-3xl'></div>
              <div className='absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-red-400/5 to-transparent rounded-full blur-3xl'></div>
              <div className='absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-orange-400/5 to-transparent rounded-full blur-3xl'></div>

              <Card className='relative overflow-hidden border-destructive/30 bg-gradient-to-br from-background/95 via-background/98 to-background/95 backdrop-blur-xl shadow-2xl shadow-red-500/10'>
                {/* Premium Top Border */}
                <div className='h-1 w-full bg-gradient-to-r from-red-500 via-orange-500 to-red-600'></div>

                {/* Ultra-Premium Header */}
                <CardHeader className='pb-6 relative overflow-hidden'>
                  {/* Background Pattern */}
                  <div className='absolute inset-0 bg-grid-red-100/30 dark:bg-grid-red-900/10 mask-gradient-faded'></div>
                  <div className='absolute -top-4 -right-4 opacity-5'>
                    <Trash2 className='w-32 h-32 text-red-500' />
                  </div>
                  <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-red-500/5 to-transparent rounded-full blur-2xl'></div>

                  <div className='relative z-10'>
                    <CardTitle className='flex items-center gap-4 text-2xl font-bold'>
                      <div className='relative'>
                        <div className='absolute inset-0 rounded-xl blur-md bg-red-500/20'></div>
                        <div className='relative p-3 rounded-xl bg-gradient-to-br from-red-500/10 via-orange-500/10 to-pink-500/10 border border-red-500/20 backdrop-blur-sm'>
                          <Trash2 className='w-6 h-6 text-red-600 dark:text-red-400' />
                        </div>
                      </div>
                      <div>
                        <span className='bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 dark:from-red-400 dark:via-orange-400 dark:to-pink-400 bg-clip-text text-transparent'>
                          Danger Zone
                        </span>
                        <div className='text-sm font-normal text-muted-foreground mt-1'>
                          This action cannot be undone and will result in permanent data loss.
                        </div>
                      </div>
                    </CardTitle>
                  </div>
                </CardHeader>

                <CardContent className='px-8 pb-2'>
                  {/* Enhanced Warning Section */}
                  <div className='space-y-6'>
                    {/* Critical Warning Box */}
                    <div className='relative p-6 rounded-2xl bg-gradient-to-r from-red-50/80 to-orange-50/80 dark:from-red-950/30 dark:to-orange-950/30 border border-red-200/40 dark:border-red-800/40 overflow-hidden'>
                      {/* Background Pattern */}
                      <div className='absolute inset-0 bg-grid-red-100/20 dark:bg-grid-red-900/10'></div>
                      <div className='absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-red-400/10 to-transparent rounded-full blur-xl'></div>

                      {/* Background Icon */}
                      <div className='absolute -top-4 -left-4 opacity-5'>
                        <AlertTriangle className='w-32 h-32 text-red-500' />
                      </div>

                      <div className='relative z-10'>
                        <div className='flex items-start gap-4'>
                          <div className='flex-1'>
                            <h3 className='text-lg font-bold text-red-700 dark:text-red-300 mb-2'>Critical Warning</h3>
                            <div className='space-y-2 text-sm text-red-600/80 dark:text-red-400/80'>
                              <div className='flex items-center gap-2'>
                                <div className='size-2 rounded-full bg-red-500'></div>
                                <span>All server data and configurations will be permanently deleted</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='size-2 rounded-full bg-red-500'></div>
                                <span>Active RDP sessions will be immediately terminated</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='size-2 rounded-full bg-red-500'></div>
                                <span>Associated resources and backups will be removed</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='size-2 rounded-full bg-red-500'></div>
                                <span>Billing for this server will stop immediately</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Premium Action Section */}
                    <div className='relative pt-6'>
                      {/* Elegant Separator */}
                      <div className='absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-red-300/10 via-red-300/60 to-red-300/10'></div>

                      <div className='flex items-center justify-end'>
                        {/* Premium Delete Button */}
                        <Button
                          variant='destructive'
                          onClick={handleDeleteServer}
                          className='relative min-w-[220px] h-14 rounded-xl bg-gradient-to-r from-red-600 via-red-700 to-red-800 hover:from-red-700 hover:via-red-800 hover:to-red-900 text-white font-bold shadow-lg shadow-red-500/30 transition-all duration-500 hover:shadow-xl hover:shadow-red-500/40 hover:scale-[1.02] overflow-hidden group'
                        >
                          {/* Button Background Animation */}
                          <div className='absolute inset-0 bg-gradient-to-r from-red-400/20 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>

                          {/* Button Content */}
                          <div className='relative z-10 flex items-center gap-3'>
                            <div className='p-1 rounded-md bg-white/10'>
                              <Trash2 className='w-5 h-5' />
                            </div>
                            <span className='text-base'>Delete Server Permanently</span>
                            <div className='w-1 h-1 rounded-full bg-white/60 animate-pulse'></div>
                          </div>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'password' && (
            <div className='relative'>
              {/* Premium Background Effects */}
              <div className='absolute inset-0 bg-gradient-to-br from-blue-50/30 via-indigo-50/20 to-purple-50/30 dark:from-blue-950/20 dark:via-indigo-950/10 dark:to-purple-950/20 rounded-2xl blur-3xl'></div>
              <div className='absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-400/5 to-transparent rounded-full blur-3xl'></div>
              <div className='absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-indigo-400/5 to-transparent rounded-full blur-3xl'></div>

              <Card className='relative overflow-hidden border-border/60 bg-gradient-to-br from-background/95 via-background/98 to-background/95 backdrop-blur-xl'>
                {/* Premium Top Border */}
                <div className='h-1 w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-pink-500'></div>

                {/* Ultra-Premium Header */}
                <CardHeader className='pb-6 relative overflow-hidden'>
                  {/* Background Pattern */}
                  <div className='absolute inset-0 bg-grid-blue-100/30 dark:bg-grid-blue-900/10 mask-gradient-faded'></div>
                  <div className='absolute -top-4 -right-4 opacity-3'>
                    <Key className='w-32 h-32 text-blue-500' />
                  </div>
                  <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-500/5 to-transparent rounded-full blur-2xl'></div>

                  <div className='relative z-10'>
                    <CardTitle className='flex items-center gap-4 text-2xl font-bold mb-3'>
                      <div className='relative'>
                        <div className='absolute inset-0 rounded-xl blur-md bg-blue-500/20'></div>
                        <div className='relative p-3 rounded-xl bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 border border-blue-500/20 backdrop-blur-sm'>
                          <Shield className='w-6 h-6 text-blue-600 dark:text-blue-400' />
                        </div>
                      </div>
                      <div>
                        <span className='bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400 bg-clip-text text-transparent'>
                          Security Center
                        </span>
                        <div className='text-sm font-normal text-muted-foreground mt-1'>
                          Enterprise Password Management
                        </div>
                      </div>
                    </CardTitle>

                    <CardDescription className='text-sm text-muted-foreground/80 leading-relaxed'>
                      Update your Windows Server credentials with enterprise-grade security protocols.
                      <span className='text-blue-600 dark:text-blue-400 font-medium'>
                        {' '}
                        Advanced encryption enabled.
                      </span>
                    </CardDescription>
                  </div>
                </CardHeader>

                <CardContent className='px-8 pb-8'>
                  <Form {...passwordForm}>
                    <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className='space-y-4'>
                      {/* Two Column Layout for Password Fields */}
                      <div className='grid grid-cols-1 lg:grid-cols-2 space-x-12'>
                        {/* Left Column - Password Fields */}
                        <div className='space-y-8 space-x-8'>
                          {/* Current Password Field */}
                          <FormField
                            control={passwordForm.control}
                            name='account_password'
                            render={({ field }) => (
                              <FormItem>
                                <div className='flex items-center'>
                                  <Lock className='w-4 h-4 text-muted-foreground me-2' />
                                  <FormLabel className='text-sm font-semibold'>TradeVPS Account Password</FormLabel>
                                  <span className='text-destructive text-sm ms-0.5'>*</span>
                                </div>
                                <FormControl>
                                  <div className='relative group -mt-2'>
                                    <Input
                                      type={showPassword.account_password ? 'text' : 'password'}
                                      placeholder='Enter current password'
                                      disabled={isChangingPassword}
                                      className='h-11 pl-4 pr-12 rounded-lg border border-border/40 bg-background/50 backdrop-blur-sm transition-all duration-300 focus:border-green-500/70 focus:bg-background group-hover:border-border/60 shadow-none'
                                      {...field}
                                    />
                                    <div className='absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center'>
                                      <button
                                        type='button'
                                        onClick={() =>
                                          setShowPassword(prev => ({
                                            ...prev,
                                            account_password: !prev.account_password
                                          }))}
                                        className='text-muted-foreground/50 hover:text-muted-foreground flex items-center justify-center'
                                      >
                                        {showPassword.account_password ? (
                                          <EyeOff className='w-4 h-4' />
                                        ) : (
                                          <Eye className='w-4 h-4' />
                                        )}
                                      </button>
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className='text-xs -mt-2' />
                              </FormItem>
                            )}
                          />

                          {/* New Password Field */}
                          <FormField
                            control={passwordForm.control}
                            name='new_password'
                            render={({ field }) => (
                              <FormItem>
                                <div className='flex items-center'>
                                  <Key className='w-4 h-4 text-muted-foreground me-2' />
                                  <FormLabel className='text-sm font-semibold'>New Password</FormLabel>
                                  <span className='text-destructive text-sm ms-0.5'>*</span>
                                </div>
                                <FormControl>
                                  <div className='relative group -mt-2'>
                                    <Input
                                      type={showPassword.new_password ? 'text' : 'password'}
                                      placeholder='Create strong password'
                                      disabled={isChangingPassword}
                                      className='h-11 pl-4 pr-12 rounded-lg border border-border/40 bg-background/50 backdrop-blur-sm transition-all duration-300 focus:border-green-500/70 focus:bg-background group-hover:border-border/60 shadow-none'
                                      {...field}
                                    />
                                    <div className='absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center'>
                                      <button
                                        type='button'
                                        onClick={() =>
                                          setShowPassword(prev => ({ ...prev, new_password: !prev.new_password }))}
                                        className='text-muted-foreground/50 hover:text-muted-foreground flex items-center justify-center'
                                      >
                                        {showPassword.new_password ? (
                                          <EyeOff className='w-4 h-4' />
                                        ) : (
                                          <Eye className='w-4 h-4' />
                                        )}
                                      </button>
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className='text-xs -mt-2' />
                              </FormItem>
                            )}
                          />

                          {/* Confirm Password Field */}
                          <FormField
                            control={passwordForm.control}
                            name='confirm_password'
                            render={({ field }) => (
                              <FormItem>
                                <div className='flex items-center'>
                                  <CheckCircle className='w-4 h-4 text-muted-foreground me-2' />
                                  <FormLabel className='text-sm font-semibold'>Confirm Password</FormLabel>
                                  <span className='text-destructive text-sm ms-0.5'>*</span>
                                </div>
                                <FormControl>
                                  <div className='relative group -mt-2'>
                                    <Input
                                      type={showPassword.confirm_password ? 'text' : 'password'}
                                      placeholder='Confirm new password'
                                      disabled={isChangingPassword}
                                      className='h-11 pl-4 pr-12 rounded-lg border border-border/40 bg-background/50 backdrop-blur-sm transition-all duration-300 focus:border-green-500/70 focus:bg-background group-hover:border-border/60 shadow-none'
                                      {...field}
                                    />
                                    <div className='absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center'>
                                      <button
                                        type='button'
                                        onClick={() =>
                                          setShowPassword(prev => ({
                                            ...prev,
                                            confirm_password: !prev.confirm_password
                                          }))}
                                        className='text-muted-foreground/50 hover:text-muted-foreground flex items-center justify-center'
                                      >
                                        {showPassword.confirm_password ? (
                                          <EyeOff className='w-4 h-4' />
                                        ) : (
                                          <Eye className='w-4 h-4' />
                                        )}
                                      </button>
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage className='text-xs -mt-2' />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Right Column - Requirements & Notice */}
                        <div className='space-y-6 sm:mt-8'>
                          {/* Enhanced Password Requirements */}
                          <div className='p-4 rounded-lg bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200/60 dark:border-blue-800/60'>
                            <div className='flex items-center gap-2 mb-4'>
                              <Shield className='w-4 h-4 text-blue-600 dark:text-blue-400' />
                              <span className='text-sm font-semibold text-blue-600 dark:text-blue-400'>
                                Security Requirements
                              </span>
                            </div>
                            <div className='grid grid-cols-1 gap-3 text-xs text-muted-foreground'>
                              <div className='flex items-center gap-2'>
                                <div className='w-1.5 h-1.5 rounded-full bg-blue-500'></div>
                                <span>12+ characters minimum</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='w-1.5 h-1.5 rounded-full bg-blue-500'></div>
                                <span>Uppercase letter (A-Z)</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='w-1.5 h-1.5 rounded-full bg-blue-500'></div>
                                <span>Lowercase letter (a-z)</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='w-1.5 h-1.5 rounded-full bg-blue-500'></div>
                                <span>Number (0-9)</span>
                              </div>
                              <div className='flex items-center gap-2'>
                                <div className='w-1.5 h-1.5 rounded-full bg-blue-500'></div>
                                <span>Special character (@#$!%*?&)</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Premium Action Section */}
                      <div className='relative mt-8 pt-6'>
                        {/* Elegant Separator */}
                        <div className='absolute top-0 left-0 right-0 h-px bg-border/60'></div>

                        <div className='flex items-center justify-between'>
                          {/* Security Notice - Left Side */}
                          <div className='flex items-center gap-3 text-sm text-muted-foreground'>
                            <div className='p-1.5 rounded-md bg-amber-500/10'>
                              <AlertTriangle className='w-4 h-4 text-amber-600 dark:text-amber-400' />
                            </div>
                            <div>
                              <p className='text-xs font-medium text-amber-700 dark:text-amber-300'>
                                All RDP sessions will be terminated
                              </p>
                              <p className='text-xs text-amber-600/70 dark:text-amber-400/70'>
                                Re-authentication required
                              </p>
                            </div>
                          </div>

                          {/* Premium Update Button - Right Side */}
                          <Button
                            type='submit'
                            disabled={isChangingPassword}
                            className='relative min-w-[180px] h-12 rounded-xl bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white font-semibold shadow-lg shadow-blue-500/30 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/40 disabled:opacity-50 overflow-hidden group'
                          >
                            {/* Button Background Animation */}
                            <div className='absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>

                            {/* Button Content */}
                            <div className='relative z-10 flex items-center gap-2'>
                              {isChangingPassword ? (
                                <>
                                  <CyrcleSvg />
                                  <span>Updating Password...</span>
                                </>
                              ) : (
                                <>
                                  <Shield className='w-4 h-4' />
                                  <span>Update Password</span>
                                  <div className='w-1 h-1 rounded-full bg-white/60 animate-pulse'></div>
                                </>
                              )}
                            </div>
                          </Button>
                        </div>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
      <DeleteConfirmDialog
        open={openDeleteDialog}
        setOpen={setOpenDeleteDialog}
        confirmSubject={serverData?.data.name || 'this server'}
        onDeleteFunction={confirmDelete}
        isLoading={isDeleting}
        title='Delete Server'
      />
    </div>
  );
};

export default ServerDetailComponent;
