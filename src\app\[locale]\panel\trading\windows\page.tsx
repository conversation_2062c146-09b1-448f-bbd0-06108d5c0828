import { getTranslations, setRequestLocale } from 'next-intl/server';
import WindowsServersTable from '@/components/panel/windows-server/windows-server-table';

type IWidnowsServerPageProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IWidnowsServerPageProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'WindowsServer'
  });

  return {
    title: t('meta_title')
  };
}

export default async function UserProfilePage(props: IWidnowsServerPageProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div>
      <WindowsServersTable />
    </div>
  );
}
