{"name": "hobsai-web", "version": "0.0.1", "author": "moezehdev (https://github.com/moezehdev)", "engines": {"node": ">=20"}, "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev --turbo", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "format": "prettier --write \"**/*.{ts,tsx,md,js,json,jsx,scss,css}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md,js,json,jsx,scss,css}\"", "check-types": "tsc --noEmit --pretty", "check-all": "npm run format:check && npm run lint && npm run check-types", "fix-all": "npm run format && npm run lint:fix", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "prepare": "husky"}, "dependencies": {"@arcjet/next": "^1.0.0-beta.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.2", "@logtail/pino": "^0.5.2", "@next/third-parties": "^15.2.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-spring/web": "^9.7.5", "@sentry/nextjs": "^8.54.0", "@spotlightjs/spotlight": "^2.10.3", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "@tanstack/react-table": "^8.21.2", "@tradevpsnet/client": "^1.2.2", "@types/three": "^0.174.0", "@types/topojson-client": "^3.1.5", "add": "^2.0.6", "axios": "^1.8.1", "axios-auth-refresh": "^3.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "eslint-plugin-react": "^7.37.4", "framer-motion": "^12.4.7", "gsap": "^3.12.7", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "next": "^15.2.1", "next-intl": "^3.26.3", "next-themes": "^0.4.6", "nextjs-toploader": "^3.7.15", "pagination": "^0.4.6", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-spinners": "^0.15.0", "shadcn": "^2.4.0-canary.14", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "topojson-client": "^3.1.0", "yup": "^1.6.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@commitlint/cz-commitlint": "^19.6.1", "@eslint-react/eslint-plugin": "^1.26.2", "@faker-js/faker": "^9.4.0", "@next/bundle-analyzer": "^15.1.7", "@next/eslint-plugin-next": "^15.2.4", "@percy/cli": "1.30.7", "@percy/playwright": "^1.0.7", "@playwright/test": "^1.50.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.5.4", "@storybook/addon-interactions": "^8.5.4", "@storybook/addon-links": "^8.5.4", "@storybook/addon-onboarding": "^8.5.4", "@storybook/blocks": "^8.5.4", "@storybook/nextjs": "^8.5.4", "@storybook/react": "^8.5.4", "@storybook/test": "^8.5.4", "@storybook/test-runner": "^0.21.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.0.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^22.13.1", "@types/pg": "^8.11.11", "@types/react": "^19.0.8", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.5", "@vitest/expect": "^3.0.5", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.21.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-testing-library": "^7.1.1", "http-server": "^14.1.1", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "npm-run-all": "^4.1.5", "postcss": "^8.5.2", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.2", "start-server-and-test": "^2.0.10", "storybook": "^8.5.4", "tailwindcss": "^4.0.6", "typescript": "^5.7.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.5", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}