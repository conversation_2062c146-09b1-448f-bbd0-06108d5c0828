import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import type { AxiosError, Method } from 'axios';
import type { QueryRequestType } from 'types/request';
import type { ResponseErrorType, ResponseSuccess } from 'types/response';

export type QueryKeyType = ReadonlyArray<string | number>;
export type IUseQueryFnData<Response> = ResponseSuccess<Response, null>;

export type IUseQueryFactoryProps<Response, SelectResponse = Response> = UseQueryOptions<
  IUseQueryFnData<Response>,
  AxiosError<ResponseErrorType>,
  IUseQueryFnData<SelectResponse>,
  QueryKeyType
> & {
  url: string;
  queryKey: QueryKeyType;
  query?: object;
  params?: object;
  version?: number;
  method?: Method;
  showError?: boolean;
  dependence?: string | null;
};

export type IUseQueryProps<Response> = UseQueryResult<IUseQueryFnData<Response>, AxiosError<ResponseErrorType>>;

export type IUseQueryFactoryResult<Response> = IUseQueryProps<Response> & {
  fetch: (variables: QueryRequestType) => void;
  query: object;
  params: object;
};
