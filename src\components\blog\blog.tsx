'use client';

import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useBlog } from '@/hooks/blog/use-blog';
import { Skeleton } from '../loading/skeleton';

type BlogProps = {
  postLimit?: number;
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

export default function BlogPosts({ postLimit = 3 }: BlogProps) {
  const { posts, isLoading, isError } = useBlog(postLimit);

  if (isError) {
    return (
      <div className='flex items-center justify-center h-96'>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className='text-center p-8 rounded-xl bg-red-50 dark:bg-red-900/10'
        >
          <h3 className='text-xl font-semibold text-red-600 dark:text-red-400'>Error fetching posts</h3>
          <p className='mt-2 text-red-500 dark:text-red-300'>Please try again later</p>
        </motion.div>
      </div>
    );
  }

  return (
    <section className='relative py-24 overflow-hidden'>
      {/* Background Pattern */}
      <div className='absolute inset-0 bg-grid-primary/[0.02] -z-10' />

      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex flex-col md:flex-row md:items-center md:justify-between mb-16'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className='text-left mb-8 md:mb-0'
          >
            <h2 className='text-4xl md:text-5xl font-bold text-foreground mb-4'>From the blog</h2>
            <p className='text-lg text-muted-foreground max-w-2xl'>Stay updated with the latest posts from our team</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
          >
            <Button
              className='gap-1.5 bg-primary/90 hover:bg-primary'
              size='lg'
              onClick={() => window.open('https://blog.tradevps.net', '_blank')}
            >
              Read More
              <ArrowRight className='size-4 transition-transform group-hover:translate-x-1' />
            </Button>
          </motion.div>
        </div>

        <motion.div
          variants={container}
          initial='hidden'
          whileInView='show'
          viewport={{ once: true }}
          className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
        >
          {isLoading
            ? Array.from({ length: postLimit }).map((_, index) => (
                <motion.div
                  key={index}
                  variants={item}
                  className='relative isolate flex flex-col justify-end overflow-hidden rounded-2xl bg-gray-900 px-8 pb-8 pt-80 sm:pt-48 lg:pt-80'
                >
                  <Skeleton className='absolute inset-0 -z-10 h-full w-full' />
                  <div className='absolute inset-0 -z-10 bg-gradient-to-t from-gray-900 via-gray-900/40' />
                  <div className='absolute inset-0 -z-10 rounded-2xl ring-1 ring-inset ring-gray-900/10' />
                  <div className='flex flex-wrap items-center gap-y-1 overflow-hidden text-sm leading-6 text-gray-300'>
                    <Skeleton height={16} width={80} />
                    <div className='-ml-4 flex items-center gap-x-4'>
                      <Skeleton type='circle' boxSize={24} />
                      <Skeleton height={16} width={100} />
                    </div>
                  </div>
                  <Skeleton height={24} width='80%' className='mt-3' />
                </motion.div>
              ))
            : posts?.map(post => (
                <motion.article
                  key={post.id}
                  variants={item}
                  className='relative isolate flex flex-col justify-end overflow-hidden rounded-2xl bg-gray-900 px-8 pb-8 pt-80 sm:pt-48 lg:pt-80'
                >
                  <Image
                    src={post.imageUrl}
                    alt={post.title}
                    fill
                    className='absolute inset-0 -z-10 h-full w-full object-cover'
                    unoptimized={post.imageUrl.includes('placehold.co')}
                  />
                  <div className='absolute inset-0 -z-10 bg-gradient-to-t from-gray-900 via-gray-900/40' />
                  <div className='absolute inset-0 -z-10 rounded-2xl ring-1 ring-inset ring-gray-900/10' />

                  <div className='flex flex-wrap items-center gap-y-1 overflow-hidden text-sm leading-6 text-gray-300'>
                    <time dateTime={post.datetime} className='mr-8'>
                      {post.date}
                    </time>
                    <div className='-ml-4 flex items-center gap-x-4'>
                      <svg viewBox='0 0 2 2' className='-ml-0.5 h-0.5 w-0.5 flex-none fill-white/50'>
                        <circle cx={1} cy={1} r={1} />
                      </svg>
                      <div className='flex gap-x-2.5'>
                        <Image
                          src={post.author.imageUrl}
                          alt={post.author.name}
                          width={24}
                          height={24}
                          className='h-6 w-6 flex-none rounded-full bg-white/10'
                        />
                        {post.author.name}
                      </div>
                    </div>
                  </div>

                  <h4 className='mt-3 font-semibold leading-6 text-white'>
                    <Link href={post.href} target='_blank' className='relative'>
                      <span className='absolute inset-0' aria-hidden='true' />
                      {post.title}
                    </Link>
                  </h4>
                </motion.article>
              ))}
        </motion.div>
      </div>
    </section>
  );
}
