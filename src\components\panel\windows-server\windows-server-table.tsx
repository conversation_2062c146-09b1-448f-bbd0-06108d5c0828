'use client';
import type { ColumnDef } from '@tanstack/react-table';
import type { ApiError } from 'next/dist/server/api-utils';
import type { DateRange } from 'react-day-picker';

import type { IWindowsServer } from '@/types/windows-server';
import { ChevronsUpDown, EllipsisVertical, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/badge';
import { CopyableIP } from '@/components/copyable-ip';
import { DateTime } from '@/components/date-time';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useSearchQuery } from '@/hooks/search-query';
import useTableSearchQueries from '@/hooks/table-query';
import { useDeleteWindowsServerMutation } from '@/services/api/windows/use-server-fetch';
import { useWindowsServersFetch } from '@/services/api/windows/use-server-list-fetch';
import { cloudProviderMap, regionMap, serverStatusMap, windowsVersionMap } from './list-map';
import WindowsServerFiltersComponent from './windows-server-filters';

export default function WindowsServersTable() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [filterValue, setFilterValue] = useState('');
  const [per_page, setPerpage] = useState(15);
  const [columnSort, setColumnSort] = useState<{ sort_by: string; sort_direction: 'asc' | 'desc' } | null>(null);
  const search = useSearchQuery(filterValue, 500);
  const [date_from, setdateFrom] = useState<string>();
  const [date_to, setDateTo] = useState<string>();
  const [status, setSelectedStatuses] = useState<string[]>([]);
  const [region, setRegion] = useState<string[]>([]);
  const [version, setVersion] = useState<string[]>([]);
  const [project_id, setProject_id] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>(undefined);
  const [subject, setSubject] = useState<string>('');
  const [deleteId, setDeleteId] = useState<string>('');
  const [Opendialog, setOpenDialog] = useState(false);

  const queries = useTableSearchQueries({
    page,
    per_page,
    search,
    date_from,
    date_to,
    status: status.join(','),
    sort_by: columnSort?.sort_by,
    sort_direction: columnSort?.sort_direction,
    region: region.join(','),
    version: version.map(v => Number.parseInt(v)).join(','),
    project_id: project_id.join(',')
  });
  const {
    data: servers,
    refetch,
    isLoading
  } = useWindowsServersFetch(
    {
      ...queries
    },
    {
      enabled: true
    }
  );
  const handleSort = (sortKey: string) => {
    setColumnSort(prev => ({
      sort_by: sortKey,
      sort_direction: prev?.sort_by === sortKey ? (prev.sort_direction === 'asc' ? 'desc' : 'asc') : 'desc'
    }));
  };
  const destroyMutation = useDeleteWindowsServerMutation(deleteId, {
    onSuccess: () => {
      toast.success('Terminal deleted successfully');
      setOpenDialog(false);
      setSubject('');
      setDeleteId('');
      refetch();
    },
    onError: (error: ApiError) => {
      toast.error(error.message || 'Failed to delete terminal');
    }
  });

  const handleDeleteClick = (terminal: IWindowsServer) => {
    setSubject(terminal?.name);
    setDeleteId(terminal?.id);
    setOpenDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (deleteId) {
      destroyMutation.mutate();
    }
  };

  const columns: ColumnDef<IWindowsServer>[] = [
    {
      accessorKey: 'select',
      meta: {
        label: 'select'
      },
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          onClick={e => e.stopPropagation()}
        />
      )
    },
    {
      accessorKey: 'name',
      meta: {
        label: 'Name'
      },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex items-center cursor-pointer'
          onClick={() => handleSort('name')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSort('name');
            }
          }}
        >
          Name
          <ChevronsUpDown className='ml-2 h-4 w-4' />
        </div>
      ),
      cell: ({ row }) => (
        <Link href={PAGE_ROUTES.PANEL_WINDOWS_SERVERS_DETAIL(row.original.id)} className='hover:underline'>
          {row.getValue('name')}
        </Link>
      )
    },
    {
      accessorKey: 'provider',
      meta: {
        label: 'provider'
      },
      header: 'Provider',
      cell: ({ row }) => {
        const provider = row.getValue('provider') as number;
        return cloudProviderMap[provider]?.label || 'Unknown';
      }
    },
    {
      accessorKey: 'version',
      meta: {
        label: 'version'
      },
      header: 'Version',
      cell: ({ row }) => {
        const version = row.getValue('version') as number;
        return windowsVersionMap[version]?.label || 'Unknown';
      }
    },
    {
      accessorKey: 'region',
      meta: {
        label: 'region'
      },
      header: 'Region',
      cell: ({ row }) => {
        const region = row.getValue('region') as number;
        return regionMap[region]?.label || 'Unknown';
      }
    },
    {
      accessorKey: 'status',
      meta: { label: 'status' },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex items-center cursor-pointer'
          onClick={() => handleSort('status')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSort('status');
            }
          }}
        >
          Status
          <ChevronsUpDown className='ml-2 h-4 w-4' />
        </div>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as number;
        const { label, variant } = serverStatusMap[status] || { label: 'Unknown', variant: 'default' };
        return <Badge variant={variant} value={label} />;
      }
    },
    {
      accessorKey: 'ip',
      meta: {
        label: 'ip'
      },
      header: 'IP Address',
      cell: ({ row }) => {
        const ip = row?.getValue('ip') as string | undefined;
        return <CopyableIP ip={ip} />;
      }
    },
    {
      accessorKey: 'created_at',
      meta: {
        label: 'created_at'
      },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex items-center cursor-pointer'
          onClick={() => handleSort('created_at')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSort('created_at');
            }
          }}
        >
          Created At
          <ChevronsUpDown className='ml-2 h-4 w-4' />
        </div>
      ),
      cell: ({ row }) => {
        const date = new Date(row?.getValue('created_at'));
        return <DateTime value={date.toISOString()} />;
      }
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      meta: {
        label: 'actions'
      },
      cell: ({ row }) => {
        const server = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <EllipsisVertical className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(server.id)}>
                Copy Server ID
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(server.ip || '')}>
                Copy IP Address
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className='text-red-600'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDeleteClick(server);
                }}
              >
                <Trash2 className='mr-2 h-4 w-4' />
                Delete Server
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ];

  const tableClickAction = (origin: { id: string }) => {
    router.push(`/panel/trading/windows/detail/${origin.id}`);
  };

  useEffect(() => {
    if (selectedDateRange === undefined) {
      setdateFrom('');
      setDateTo('');
    }
    if (selectedDateRange?.from && selectedDateRange?.to) {
      const fromDate = selectedDateRange.from.toISOString().split('T')[0];
      const toDate = selectedDateRange.to.toISOString().split('T')[0];
      setdateFrom(fromDate);
      setDateTo(toDate);
    }
  }, [selectedDateRange]);

  return (
    <div className='mt-5'>
      <div className='flex justify-between items-center mb-5'>
        <div>
          <h1 className='text-2xl font-bold'>Windows Servers</h1>
          <p className='text-muted-foreground'>Manage your Windows server instances</p>
        </div>
        <Button onClick={() => router.push(PAGE_ROUTES.PANEL_WINDOWS_SERVERS_DEPLOY)}>
          <Plus className='mr-2 h-4 w-4' />
          Deploy
        </Button>
      </div>

      <DataTable
        pageNumber={page}
        setPageNumber={setPage}
        perpage={per_page}
        setPerpage={setPerpage}
        columns={columns}
        rowclickaction={tableClickAction}
        dataTable={{
          data: Array.isArray(servers) ? servers : servers?.data || [],
          pagination: servers?.pagination || {
            current_page: 1,
            first_page_url: '',
            from: 0,
            last_page: 1,
            last_page_url: '',
            next_page_url: null,
            per_page,
            prev_page_url: '',
            total: 0
          }
        }}
        isLoading={isLoading}
        title='ُServers'
        component={(
          <WindowsServerFiltersComponent
            setFilterValue={setFilterValue}
            filterValue={filterValue}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            selectedStatus={status}
            setSelectedStatus={setSelectedStatuses}
            region={region}
            setRegion={setRegion}
            version={version}
            setVersion={setVersion}
            project={project_id}
            setProject={setProject_id}
          />
        )}
      />

      <DeleteConfirmDialog
        open={Opendialog}
        setOpen={setOpenDialog}
        confirmSubject={subject}
        onDeleteFunction={handleDeleteConfirm}
        isLoading={destroyMutation?.isPending}
      />
    </div>
  );
}
