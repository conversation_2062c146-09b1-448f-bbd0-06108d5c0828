import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export const SkeletonCard = () => {
  return (
    <Card className='w-full'>
      <CardHeader>
        <CardTitle className='text-lg'>
          <Skeleton className='h-6 w-[150px]' />
        </CardTitle>
      </CardHeader>
      <hr />
      <CardContent>
        <div className='space-y-4'>
          <div className='flex items-center'>
            <Skeleton className='h-4 w-[100px] mr-4' />
            <Skeleton className='h-4 w-[200px]' />
          </div>
          <div className='flex items-center'>
            <Skeleton className='h-4 w-[100px] mr-4' />
            <Skeleton className='h-4 w-[200px]' />
          </div>
          <div className='flex items-center'>
            <Skeleton className='h-4 w-[100px] mr-4' />
            <Skeleton className='h-4 w-[200px]' />
          </div>
          <div className='flex items-center'>
            <Skeleton className='h-4 w-[100px] mr-4' />
            <Skeleton className='h-4 w-[200px]' />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
