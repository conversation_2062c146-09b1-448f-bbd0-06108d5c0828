'use client';

import type { APIError, ILoginParams, ILoginResponse } from '@tradevpsnet/client';
import type { LoginSchemaType } from '@/validations/login';
import { yupResolver } from '@hookform/resolvers/yup';
import { useUser } from 'hooks/user/user';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { FcGoogle } from 'react-icons/fc';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { cn } from '@/lib/utils';
import { useLoginClientMutate } from '@/services/api/auth/use-login-mutate';
import { loginValidationSchema } from '@/validations/login';

export function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<'form'>) {
  const router = useRouter();
  const user = useUser();

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginSchemaType>({
    resolver: yupResolver(loginValidationSchema)
  });

  const loginApi = useLoginClientMutate({
    onSuccess: (data: ILoginResponse) => {
      if (data?.token) {
        user.login(data.token);
        router.push(PAGE_ROUTES.PANEL);
        toast(`Welcome back ${data?.user?.name}`, {
          action: {
            label: 'Go to Panel',
            onClick: () => router.push(PAGE_ROUTES.PANEL)
          }
        });
      }
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Login failed. Please try again.');
    }
  });

  const onSubmit = (data: ILoginParams) => {
    loginApi.mutate(data);
  };

  return (
    <form className={cn('flex flex-col gap-6', className)} {...props} onSubmit={handleSubmit(onSubmit)}>
      <div className='flex flex-col items-start gap-2 text-start'>
        <h1 className='text-2xl font-bold'>Welcome back</h1>
        <p className='text-balance text-sm text-muted-foreground'>
          Kindly enter your credentionals to access your account.
        </p>
      </div>
      <div className='grid gap-6'>
        <div className='grid gap-2'>
          <Label htmlFor='email'>Email</Label>
          <Input id='email' type='email' placeholder='<EMAIL>' {...register('email')} />
          {errors.email && <p className='text-red-500 text-sm'>{errors.email.message}</p>}
        </div>
        <div className='grid gap-2'>
          <div className='flex items-center'>
            <Label htmlFor='password'>Password</Label>
            <Link href={PAGE_ROUTES.FORGET_PASSWORD} className='ml-auto text-xs underline-offset-4 hover:underline'>
              Forgot password?
            </Link>
          </div>
          <Input id='password' type='password' {...register('password')} />
          {errors.password && <p className='text-red-500 text-sm'>{errors.password.message}</p>}
        </div>
        <Button type='submit' className='w-full' isLoading={loginApi?.isPending}>
          Login
        </Button>
        <div className='relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border'>
          <span className='relative z-10 bg-background px-2 text-muted-foreground'>Or continue with</span>
        </div>
        <Link
          href='https://api.tradevps.net/v1/auth/social/google'
          className='inline-flex items-center cursor-pointer justify-center gap-2 py-2 whitespace-nowrap rounded-md text-sm font-medium  outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] w-full border border-input bg-background shadow-xs hover:bg-accent'
        >
          <FcGoogle className='size-5' />
          Login with Google
        </Link>
      </div>
      <div className='text-center text-sm'>
        Don&apos;t have an account?
        {' '}
        <Link href={PAGE_ROUTES.REGISTER} className='underline underline-offset-4'>
          Sign up
        </Link>
      </div>
    </form>
  );
}
