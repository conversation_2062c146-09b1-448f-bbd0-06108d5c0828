import type { IServerDeleteResponse, IServerDetailResponse } from '@tradevpsnet/client';
import type { ApiError } from 'next/dist/server/api-utils';
import type { IWindowsServerDeleteResponse } from '@/types/windows-server';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_QUERY_KEY } from '@/configs/api-query-key';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';
const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL!);

export const useWindowsServerDetailFetch = (id: string) => {
  return useQuery<IServerDetailResponse>({
    queryKey: [API_QUERY_KEY.WINDOWS_SERVERS_LIST, id],
    enabled: !!id,
    queryFn: () => client.windows.server_detail(id)
  });
};

export const useDeleteWindowsServerMutation = (
  id: string,
  options?: {
    onSuccess?: (data: IWindowsServerDeleteResponse) => void;
    onError?: (error: ApiError) => void;
  }
) => {
  return useMutation<IServerDeleteResponse, ApiError, void>({
    mutationFn: () => client.windows.server_delete(id),
    ...options
  });
};
