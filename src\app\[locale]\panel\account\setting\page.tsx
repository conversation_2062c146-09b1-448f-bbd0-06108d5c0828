'use client';

import { <PERSON>, <PERSON>, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useSettings } from '@/stores/settings';
import { DateFormatEnum } from '@/types/date-format';

export default function SettingPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  const {
    compactMode,
    wideMode,
    reducedMotion,
    fontSize,
    dateFormat,
    useLocalTime,
    setCompactMode,
    setWideMode,
    setReducedMotion,
    setFontSize,
    setDateFormat,
    setUseLocalTime
  } = useSettings();
  // Apply settings to document
  useEffect(() => {
    if (mounted) {
      document.documentElement.classList.remove('text-size-small', 'text-size-normal', 'text-size-large');
      document.documentElement.classList.add(`text-size-${fontSize}`);

      if (reducedMotion) {
        document.documentElement.style.setProperty('--reduce-motion', 'reduce');
      } else {
        document.documentElement.style.removeProperty('--reduce-motion');
      }
    }
  }, [compactMode, reducedMotion, fontSize, mounted]);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className='container space-y-6 p-4 md:p-8'>
      <div>
        <h1 className='text-primary'>Setting</h1>
        <p className='text-base text-muted-foreground'>Manage your application preferences and settings</p>
      </div>

      {/* Theme Settings */}
      <div className='py-4'>
        <Card className='p-4'>
          <CardHeader className='my-2'>
            <CardTitle className='text-primary'>Appeareance settings</CardTitle>
          </CardHeader>
          <CardContent className='space-y-8'>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>Color Theme</Label>
                <p className='text-sm text-muted-foreground'>Theme description</p>
              </div>
              <ToggleGroup
                type='single'
                value={theme}
                onValueChange={setTheme}
                variant='outline'
                size='default'
                className='justify-end gap-0'
              >
                <ToggleGroupItem
                  value='light'
                  aria-label='Light theme'
                  variant='outline'
                  className='px-6 py-2 rounded-tr-none rounded-br-none'
                >
                  <Sun className='h-5 w-5 mr-2' />
                  Light
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='dark'
                  aria-label='Dark theme'
                  variant='outline'
                  className='px-6 py-2 rounded-none'
                >
                  <Moon className='h-5 w-5 mr-2' />
                  Dark
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='system'
                  aria-label='System theme'
                  variant='outline'
                  className='px-6 py-2 rounded-tl-none rounded-bl-none'
                >
                  <Monitor className='h-5 w-5 mr-2' />
                  System
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
            {/* UI Scale Toggle */}
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>UI Scale</Label>
                <p className='text-sm text-muted-foreground'>Adjust the size of user interface elements</p>
              </div>
              <ToggleGroup
                type='single'
                defaultValue='normal'
                variant='outline'
                size='default'
                className='justify-end gap-0'
                value={fontSize}
                onValueChange={(value: 'small' | 'normal' | 'large') => setFontSize(value)}
              >
                <ToggleGroupItem
                  value='small'
                  aria-label='Small scale'
                  className='px-6 py-2 rounded-tr-none rounded-br-none'
                >
                  <span className='text-sm mr-2'>A</span>
                  Small
                </ToggleGroupItem>
                <ToggleGroupItem value='normal' aria-label='Normal scale' className='px-6 py-2 rounded-none'>
                  <span className='text-lg mr-2'>A</span>
                  Normal
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='large'
                  aria-label='Large scale'
                  className='px-6 py-2 rounded-tl-none rounded-bl-none'
                >
                  <span className='text-2xl mr-2'>A</span>
                  Large
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>Reduce animations</Label>
                <p className='text-sm text-muted-foreground'>Minimize motion effects for better accessibility</p>
              </div>
              <Switch id='reduced-motion' checked={reducedMotion} onCheckedChange={setReducedMotion} />
            </div>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label htmlFor='compact-mode'>Compact mode</Label>
                <p className='text-sm text-muted-foreground'>Reduce spacing between elements for a denser layout</p>
              </div>
              <Switch
                id='compact-mode'
                checked={compactMode}
                onCheckedChange={setCompactMode}
                aria-label='Toggle compact mode'
              />
            </div>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label htmlFor='wide-mode'>Wide mode</Label>
                <p className='text-sm text-muted-foreground'>Expand the layout</p>
              </div>
              <Switch id='wide-mode' checked={wideMode} onCheckedChange={setWideMode} aria-label='Toggle wide mode' />
            </div>
            {/* Date Format Settings */}
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>Date Format</Label>
                <p className='text-sm text-muted-foreground'>Choose your prefered time format</p>
              </div>
              <div className='w-auto'>
                <Select
                  value={dateFormat.format}
                  onValueChange={(value: DateFormatEnum) => setDateFormat({ format: value })}
                >
                  <SelectTrigger className='text-primary'>
                    <SelectValue placeholder='Select Date Format' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DateFormatEnum.RFC3339} className=' border-t border-muted'>
                      2025-04-07 11:32:00
                    </SelectItem>
                    <SelectItem value={DateFormatEnum.RFC2822} className=' border-t border-muted'>
                      Mon, 07 Apr 2025 11:32:00 GMT
                    </SelectItem>
                    <SelectItem value={DateFormatEnum.US} className=' border-t border-muted'>
                      April 7, 2025, 11:32 AM
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Use Local Time or Server UTC */}
            <div className='flex items-center justify-between space-x-4'>
              <div className='space-y-1'>
                <Label>Use Local Time</Label>
                <p className='text-sm text-muted-foreground'>Use the local time zone or server UTC time</p>
              </div>
              <Switch checked={useLocalTime} onCheckedChange={setUseLocalTime} aria-label='Use Local Time' />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
