'use client';

import { motion } from 'framer-motion';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { faqItems } from '@/constants/faq';
import { cn } from '@/lib/utils';

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerChildren = {
  visible: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function FAQPage() {
  return (
    <main className='min-h-screen py-12 md:py-20 bg-background text-foreground relative overflow-hidden'>
      <div className='absolute inset-0 bg-grid-primary/[0.03] dark:bg-grid-primary/[0.02]' />
      <div className='absolute inset-0 bg-gradient-to-b from-transparent via-background/50 to-background dark:from-background/0 dark:via-background/80 dark:to-background' />

      <div className='relative max-w-6xl mx-auto px-4 sm:px-6'>
        <motion.div
          initial='hidden'
          animate='visible'
          variants={fadeIn}
          className='max-w-3xl mx-auto text-center pb-12'
        >
          <h1
            className={cn(
              'font-inter-tight text-4xl md:text-5xl font-bold mb-4',
              'bg-clip-text text-transparent',
              'bg-gradient-to-r',
              'from-primary via-primary/80 to-primary',
              'dark:from-blue-300 dark:via-blue-200 dark:to-blue-100',
              'animate-gradient'
            )}
          >
            Frequently Asked Questions
          </h1>
          <p className='text-lg text-muted-foreground'>
            Find answers to common questions about our services and platform
          </p>
        </motion.div>

        <Tabs defaultValue='general' className='space-y-8'>
          <div
            className={cn(
              'rounded-sm p-1',
              'bg-muted dark:bg-muted/50',
              'border border-border dark:border-border/50',
              'max-w-3xl mx-auto'
            )}
          >
            <TabsList className='flex flex-wrap justify-center gap-1 bg-transparent'>
              {Object.keys(faqItems).map(category => (
                <TabsTrigger
                  key={category}
                  value={category}
                  className={cn(
                    'rounded-sm',
                    'bg-muted/50',
                    'text-muted-foreground',
                    'data-[state=active]:bg-background',
                    'data-[state=active]:text-foreground',
                    'dark:bg-muted/20',
                    'dark:text-muted-foreground/70',
                    'dark:data-[state=active]:bg-background/90',
                    'dark:data-[state=active]:text-foreground',
                    'transition-colors duration-200',
                    'px-3 py-1.5',
                    'hover:text-foreground dark:hover:text-foreground/90'
                  )}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {Object.entries(faqItems).map(([category, questions]) => (
            <TabsContent key={category} value={category}>
              <motion.div initial='hidden' animate='visible' variants={staggerChildren} className='max-w-3xl mx-auto'>
                <Accordion type='single' collapsible className='w-full space-y-4' defaultValue={`${category}-0`}>
                  {questions.map((faq, index) => (
                    <motion.div key={index} variants={fadeIn} custom={index} transition={{ duration: 0.3 }}>
                      <AccordionItem
                        value={`${category}-${index}`}
                        className={cn(
                          'rounded-sm overflow-hidden',
                          'bg-background/80 dark:bg-background/20',
                          'backdrop-blur-lg',
                          'border border-border dark:border-border/40',
                          'shadow-sm hover:shadow-sm',
                          'transition-all duration-300',
                          'data-[state=open]:bg-card dark:data-[state=open]:bg-card/80'
                        )}
                      >
                        <AccordionTrigger
                          className={cn(
                            'px-6 py-4 text-left text-lg font-semibold hover:no-underline',
                            'group transition-colors'
                          )}
                        >
                          <span
                            className={cn(
                              'text-foreground/90 dark:text-foreground/70',
                              'group-hover:text-primary dark:group-hover:text-white',
                              'group-data-[state=open]:text-primary dark:group-data-[state=open]:text-white',
                              'transition-colors duration-200'
                            )}
                          >
                            {faq.question}
                          </span>
                        </AccordionTrigger>
                        <AccordionContent className='px-6 pb-4'>
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.2 }}
                            className='text-muted-foreground dark:text-muted-foreground/90'
                          >
                            {faq.answer}
                          </motion.div>
                        </AccordionContent>
                      </AccordionItem>
                    </motion.div>
                  ))}
                </Accordion>
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </main>
  );
}
