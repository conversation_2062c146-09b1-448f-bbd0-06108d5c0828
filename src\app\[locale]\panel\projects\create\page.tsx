import { getTranslations, setRequestLocale } from 'next-intl/server';
import { CreateProject } from '@/components/panel/projects/create';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({
    locale,
    namespace: 'Projects'
  });

  return {
    title: t('create.meta_title'),
    description: t('create.meta_description')
  };
}

export default async function CreateProjectPage({ params }: Props) {
  const { locale } = await params;
  setRequestLocale(locale);

  return <CreateProject />;
}
