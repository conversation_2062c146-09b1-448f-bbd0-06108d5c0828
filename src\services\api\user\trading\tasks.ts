import type { IUseMutationFactoryProps } from '@/hooks/api/use-mutation-factory/use-mutation-factory.type';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { SERVER } from 'libs/Axios';
import { useMutationFactory } from '@/hooks/api/use-mutation-factory';

type ITaskDetail = {
  id: string;
  type: number;
  payload: {
    timeframe?: string;
    auto_trading?: number;
    allow_dll_imports?: number;
    chart?: string;
    file?: string;
    file_sha256?: string;
    url?: string;
  } | null;
  result: {
    runner_result?: string;
    status: number;
  };
  status: number;
  started_at: number;
  finished_at: number;
};
type ITask = {
  id: string;
  type: string;
};
export const useTerminalTaskFetch = (id: string) => {
  return useQuery({
    queryKey: [API_QUERY_KEY.TASK_LIST, id],
    enabled: !!id,
    queryFn: async () => {
      const url = API_ROUTES.TASK_LIST.replace('{id}', id);
      const response = await SERVER.get<ITaskDetail[]>(url);
      return response.data;
    }
  });
};
export const useNewTaskMutation = (options?: Pick<IUseMutationFactoryProps<ITask>, 'onSuccess' | 'onError'>) => {
  return useMutationFactory<ITask>({
    method: 'POST',
    ...options,
    url: API_ROUTES.NEW_TASK,
    query: (data?: ITask) => ({
      type: data?.type
    }),
    config: (data?: ITask) => ({
      url: data?.id ? API_ROUTES.NEW_TASK.replace('{id}', data.id) : API_ROUTES.NEW_TASK
    })
  } as unknown as IUseMutationFactoryProps<ITask>);
};
