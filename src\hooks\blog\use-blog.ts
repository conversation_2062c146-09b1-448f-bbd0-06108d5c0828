import type { IPostInfo } from 'types/blog';
import type { IUseBlog } from './use-blog.type';
import { useEffect, useState } from 'react';
import { useBlogPostFetch } from 'services/api/blog/use-blog-post-fetch';

type Post = {
  id: number;
  title: { rendered: string };
  link: string;
  excerpt: { rendered: string };
  date: string;
  _embedded?: {
    'wp:featuredmedia'?: { source_url: string }[];
    'wp:term'?: { name: string; link: string }[][];
    'author'?: { name: string; link: string; avatar_urls: { [key: string]: string } }[];
  };
  yoast_head_json?: {
    og_image?: { url: string }[];
  };
};

export function useBlog(postLimit: number): IUseBlog {
  const [currentLimit, setCurrentLimit] = useState(postLimit);
  const fetchBlogPost = useBlogPostFetch(currentLimit);

  const [formattedPosts, setFormattedPosts] = useState<IPostInfo[]>([]);

  // Format posts when fetched
  useEffect(() => {
    if (fetchBlogPost.data) {
      const posts = fetchBlogPost.data.map((post: Post) => ({
        id: post.id,
        title: post.title.rendered,
        href: post.link,
        description: post.excerpt.rendered.replace(/<[^>]+>/g, ''),
        imageUrl:
          post._embedded?.['wp:featuredmedia']?.[0]?.source_url
          || post.yoast_head_json?.og_image?.[0]?.url
          || 'https://placehold.co/600x400',
        date: new Date(post.date).toLocaleDateString(),
        datetime: post.date,
        category: {
          title: post._embedded?.['wp:term']?.[0]?.[0]?.name || 'Uncategorized',
          href: post._embedded?.['wp:term']?.[0]?.[0]?.link || '#'
        },
        author: {
          name: post._embedded?.author?.[0]?.name || 'Unknown Author',
          role: 'Author',
          href: post._embedded?.author?.[0]?.link || '#',
          imageUrl: post._embedded?.author?.[0]?.avatar_urls?.['96']
        }
      }));
      setFormattedPosts(posts);
    }
  }, [fetchBlogPost.data]); // Run this effect when the data changes

  // Refetch posts with updated limit
  const refetchPosts = async (newLimit: number): Promise<IPostInfo[]> => {
    setCurrentLimit(newLimit); // Update the limit to trigger a re-fetch
    return formattedPosts; // Return the currently formatted posts
  };

  return {
    posts: formattedPosts,
    isLoading: fetchBlogPost.isLoading,
    isError: fetchBlogPost.isError,
    refetchPosts
  };
}
