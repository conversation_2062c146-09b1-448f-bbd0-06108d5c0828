import type { IMainProviderProps as Props } from './main-provider.type';
import { ReactQueryProvider } from 'providers/react-query';
import { TopLoaderProvider } from 'providers/top-loader';
import { Toaster } from 'sonner';

export const MainProvider: React.FC<Props> = ({ children }) => {
  return (
    <>
      <ReactQueryProvider>{children}</ReactQueryProvider>
      <TopLoaderProvider />
      <Toaster />
    </>
  );
};
