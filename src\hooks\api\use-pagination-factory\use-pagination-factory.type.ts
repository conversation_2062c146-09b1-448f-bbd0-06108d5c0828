import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import type { AxiosError, Method } from 'axios';
import type { QueryRequestType } from 'types/request';
import type { ResponseErrorType, ResponseSuccess, ResponseType } from 'types/response';

export type QueryKeyType = ReadonlyArray<string | number>;
export type IUsePaginationFnData<Response, AdditionalResponse = Record<string, unknown>> = ResponseSuccess<
  Response,
  'pagination',
  AdditionalResponse
>;

export type IUsePaginationFactoryProps<
  Response,
  SelectResponse = Response,
  AdditionalResponse = Record<string, unknown>
> = UseQueryOptions<
  ResponseType<Response>,
  AxiosError<ResponseErrorType>,
  IUsePaginationFnData<SelectResponse, AdditionalResponse>,
  QueryKeyType
> & {
  url: string;
  method?: Method;
  queryKey: QueryKeyType;
  query?: Record<string, unknown>;
  search?: Record<string, unknown>;
  params?: Record<string, unknown>;
  version?: number;
  page: number;
  perPage?: number;
  showError?: boolean;
} & (SelectResponse extends Response
  ? { select?: never }
  : {
      select: (
        data: IUsePaginationFnData<Response, AdditionalResponse>
      ) => IUsePaginationFnData<SelectResponse, AdditionalResponse>;
    });

export type IUsePaginationProps<Response, AdditionalResponse = Record<string, unknown>> = UseQueryResult<
  IUsePaginationFnData<Response, AdditionalResponse>,
  AxiosError<ResponseErrorType>
>;

export type IUsePaginationFactoryResult<Response, AdditionalResponse> = IUsePaginationProps<
  Response,
  AdditionalResponse
> & {
  fetch: (variables: QueryRequestType) => void;
  queryParams: Record<string, unknown>;
  params: Record<string, unknown>;
  search: Record<string, unknown>;
};
