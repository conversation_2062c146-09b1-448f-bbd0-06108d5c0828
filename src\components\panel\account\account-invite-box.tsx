'use client';

import { motion } from 'framer-motion';
import { CheckCircle, Gift, Mail, Users } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

const fadeIn = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.22, 1, 0.36, 1] as any
    }
  }
};

const staggerContainer = {
  visible: {
    transition: {
      staggerChildren: 0.08
    }
  }
};

export function AccountInviteBox() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Add your API call here
      // await inviteFriend(email);

      // Show success toast with action
      toast.success('Invitation sent successfully!', {
        description: `An invitation email has been sent to ${email}`,
        action: {
          label: 'View Invites',
          onClick: () => (window.location.href = '/panel/earn')
        },
        duration: 5000
      });

      setEmail('');
    } catch (error) {
      toast.error('Failed to send invitation', {
        description: 'Please try again later or contact support if the problem persists.'
      });
      console.error('Failed to send invite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial='hidden'
      animate='visible'
      variants={staggerContainer}
      className='h-full' // Add this to ensure full height
    >
      <Card className='h-full flex flex-col'>
        <CardHeader className='border-b border-border/50 pb-4 space-y-1'>
          <motion.div variants={fadeIn} className='flex items-center gap-2'>
            <Users className='h-5 w-5 text-primary' />
            <CardTitle>Invite Friends</CardTitle>
          </motion.div>
          <motion.div variants={fadeIn}>
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <Gift className='h-4 w-4' />
              <span>Earn £10 for each friend who joins</span>
            </div>
          </motion.div>
        </CardHeader>
        <CardContent className='flex flex-col flex-grow justify-between gap-6 pt-6'>
          <motion.div variants={fadeIn} className='space-y-6'>
            <form onSubmit={handleSubmit} className='space-y-4'>
              <div className='relative'>
                <Mail className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                <Input
                  type='email'
                  placeholder="Enter friend's email"
                  className='pl-10'
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  required
                />
              </div>
              <Button type='submit' className='w-full' disabled={isLoading}>
                {isLoading ? (
                  <span className='flex items-center gap-2'>
                    <span className='h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent' />
                    Sending...
                  </span>
                ) : (
                  <span className='flex items-center gap-2'>
                    <Mail className='h-4 w-4' />
                    Send Invite
                  </span>
                )}
              </Button>
            </form>

            <div className='space-y-4'>
              <div className='relative'>
                <div className='absolute inset-0 flex items-center'>
                  <div className='w-full border-t border-border' />
                </div>
                <div className='relative flex justify-center text-xs uppercase'>
                  <span className='bg-card px-2 text-muted-foreground'>Or share your link</span>
                </div>
              </div>

              <div className='flex items-center gap-2 rounded-lg border p-3'>
                <div className='flex-grow truncate text-sm'>https://tradevps.com/ref/username</div>
                <Button
                  variant='ghost'
                  size='sm'
                  className='shrink-0'
                  onClick={() => {
                    navigator.clipboard.writeText('https://tradevps.com/ref/username');
                    toast.success('Referral link copied!');
                  }}
                >
                  Copy
                </Button>
              </div>
            </div>
          </motion.div>

          <motion.div variants={fadeIn} className='space-y-3'>
            <div className='flex items-center gap-2 text-sm'>
              <CheckCircle className='h-4 w-4 text-primary' />
              <span>3 friends joined</span>
            </div>
            <div className='flex items-center gap-2 text-sm'>
              <CheckCircle className='h-4 w-4 text-primary' />
              <span>£30 earned so far</span>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
