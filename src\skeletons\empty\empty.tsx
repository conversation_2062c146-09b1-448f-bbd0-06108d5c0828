import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

type IEmptyProps = {
  link?: string;
  title?: string;
  description?: string;
};

export const Empty: React.FC<IEmptyProps> = ({
  link,
  title = 'No Data Available',
  description = 'There is nothing to display at the moment.'
}) => {
  return (
    <div className='w-full h-full flex flex-col justify-center items-center text-center p-4'>
      <div className='grid justify-center py-5'>
        <Image alt='' className='dark:hidden max-h-[170px]' width={48} height={48} src='/img/skeletons/empty.svg' />
        <Image
          alt=''
          className='light:hidden max-h-[170px]'
          width={48}
          height={48}
          src='/img/skeletons/empty-dark.svg'
        />
      </div>
      <h2 className='text-lg mt-4 font-medium text-gray-900 text-center'>{title}</h2>
      <p className='text-sm mt-2 text-gray-700 text-center gap-1'>{description}</p>
      {link && (
        <Button asChild className='mt-4'>
          <Link href={link} target='_blank' rel='noopener noreferrer'>
            Go to Link
          </Link>
        </Button>
      )}
    </div>
  );
};
