name: Publish Docker image

on:
  push:
    branches:
      - main
      - next
  release:
    types: [published]

jobs:
  push_to_registries:
    name: Push Docker image
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: actionlint
        uses: raven-actions/actionlint@v2

      - name: Log in to Docker Hub
        uses: docker/login-action@f4ef78c080cd8ba55a85445d5b36e214a81df20a
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ secrets.DOCKER_NAMESPACE }}/${{ secrets.DOCKER_REPO }}

      - name: Build and push Docker images
        id: push
        uses: docker/build-push-action@3b5e8027fcad23fda98b2e3ac259d8d67585f671
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            ${{ steps.meta.outputs.tags }}
            ${{ secrets.DOCKER_NAMESPACE }}/${{ secrets.DOCKER_REPO }}:latest
            ${{ secrets.DOCKER_NAMESPACE }}/${{ secrets.DOCKER_REPO }}:${{ github.ref_name }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Send Slack Notification
        if: always()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          webhook: ${{ secrets.SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            text: "*🚀 New Build by ${{ github.event.head_commit.author.username }}*"
            attachments:
              - color: "${{ job.status == 'success' && '#36a64f' || '#ff0000' }}"
                blocks:
                  - type: "section"
                    text:
                      type: "mrkdwn"
                      text: "*GitHub Action Result is*: _*${{ job.status == 'success' && 'SUCCESS' || 'FAILURE' }}*_"
                  - type: "actions"
                    elements:
                      - type: "button"
                        text:
                          type: "plain_text"
                          text: "🔗 View Commit"
                        url: "${{ github.event.pull_request.html_url || github.event.head_commit.url }}"
                        style: "primary"
                      - type: "button"
                        text:
                          type: "plain_text"
                          text: "🐳 View Docker Image"
                        url: "https://hub.docker.com/r/${{ secrets.DOCKER_NAMESPACE }}/${{ secrets.DOCKER_REPO }}"
                  - type: "divider"
                  - type: "section"
                    fields:
                      - type: "mrkdwn"
                        text: "*Repository:*\n${{ github.repository }}"
                      - type: "mrkdwn"
                        text: "*Branch:*\n${{ github.ref_name }}"
                      - type: "mrkdwn"
                        text: "*Tag:*\n${{ steps.meta.outputs.tags || 'N/A' }}"
                      - type: "mrkdwn"
                        text: "*Commit:*\n${{ github.sha }}"
