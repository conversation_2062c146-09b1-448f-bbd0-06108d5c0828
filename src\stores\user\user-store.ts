import type { IUserStore } from './user-store.type';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const useUserStore = create<IUserStore>()(
  persist(
    set => ({
      token: null,
      setToken: (token, refreshToken) => set({ token, refreshToken }),
      removeToken: () => set({ token: null, refreshToken: null })
    }),
    {
      name: 'token',
      storage: createJSONStorage(() => localStorage)
    }
  )
);
