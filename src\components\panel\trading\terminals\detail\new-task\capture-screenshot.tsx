import { useParams } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

export const CaptureScreenshot = () => {
  const { id } = useParams();
  const { mutate, isPending } = useNewTaskMutation();

  const handleSubmit = () => {
    mutate(
      {
        params: { id: id as string },
        queryParams: { type: '6' },
        body: {}
      },
      {
        onSuccess: () => toast.success('Screenshot captured'),
        onError: () => toast.error('Failed to capture screenshot')
      }
    );
  };

  return (
    <div className='flex flex-col md:flex-row mb-10 justify-between gap-5'>
      <div className='w-full'>
        <div className='w-full'>
          <div className='flex w-full items-center justify-center text-gray-400 py-12'>
            <p>There are no options for this type of task.</p>
          </div>
          <div className='flex mt-5 justify-between md:justify-start'>
            <Button onClick={handleSubmit} disabled={isPending} className='ml-auto mt-3'>
              {isPending ? 'Submitting...' : 'Submit'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
