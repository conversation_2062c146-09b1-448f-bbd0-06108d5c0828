import type { IServerDeployParams, IServerDeployResponse } from '@tradevpsnet/client';
import type { ApiError } from 'next/dist/server/api-utils';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';

const raw = localStorage.getItem('token');
const parsed = raw ? JSON.parse(raw) : null;
const token = parsed?.state?.token || '';
const client = new Client(token, process.env.NEXT_PUBLIC_SERVER_URL!);

export const useWindowsServerDeployMutation = (options?: {
  onSuccess?: (data: IServerDeployResponse) => void;
  onError?: (error: ApiError) => void;
}) => {
  return useMutation<IServerDeployResponse, ApiError, IServerDeployParams>({
    mutationFn: payload => client.windows.server_deploy(payload),
    ...options
  });
};
