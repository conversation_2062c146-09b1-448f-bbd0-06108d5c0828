import { ChevronDown, X } from 'lucide-react';
import Image from 'next/image';
import * as React from 'react';
import Logo from '@/public/img/illustrations/27.svg';
import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { Input } from '../ui/input';

type DropDownItem = {
  label: string;
  id: string;
  icon?: string | React.ReactNode;
};

type DropDownComponentProps = {
  items: DropDownItem[];
  title: string;
  selectedItems: string[];
  setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>;
  loading?: boolean;
  showIcon?: boolean;
  badgSize?: number;
};

const DropDownComponent: React.FC<DropDownComponentProps> = ({
  loading,
  badgSize,
  items,
  title,
  selectedItems,
  setSelectedItems,
  showIcon = false
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleDropdownClose = () => {
    setSearchTerm('');
  };

  const handleItemChange = (key: string, checked: boolean) => {
    setSelectedItems(prev => (checked ? [...prev, key] : prev.filter(k => k !== key)));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  const handleReset = () => {
    setSelectedItems([]);
  };

  const filteredItems = items.filter(item => item.label.toLowerCase().includes(searchTerm.toLowerCase()));

  return (
    <DropdownMenu onOpenChange={open => !open && handleDropdownClose()}>
      <div className='relative inline-flex items-center'>
        <DropdownMenuTrigger className='w-full relative px-0 gap-0' asChild>
          <Button
            className='min-w-[110px] flex items-center justify-between pl-3 text-left font-normal text-muted-foreground text-sm'
            size='sm'
            variant='outline'
          >
            <div className='flex items-center'>
              {selectedItems.length > 0 ? (
                <>
                  {selectedItems.slice(0, 3).map((id) => {
                    const selectedItem = items.find(item => item.id === id);
                    if (!selectedItem) {
                      return null;
                    }

                    const value = showIcon ? selectedItem.icon : selectedItem.label;
                    return (
                      <span key={id} className={`p-0 ${showIcon && badgSize ? '-ml-1' : showIcon ? '-ml-1.5' : ''}`}>
                        {showIcon ? (
                          typeof value === 'string' ? (
                            <img
                              src={value}
                              alt=''
                              className='size-4.5 rounded-full object-cover border border-white'
                            />
                          ) : (
                            value
                          )
                        ) : (
                          value
                        )}
                      </span>
                    );
                  })}
                  {selectedItems.length > 3 && showIcon && (
                    <span
                      className={`-ml-1 size-${badgSize ?? '6'}  rounded-full bg-gray-200 text-[10px] text-muted-foreground flex items-center justify-center border border-white`}
                    >
                      +
                      {selectedItems.length - 3}
                    </span>
                  )}
                </>
              ) : (
                title
              )}
            </div>
            <ChevronDown className='h-4 w-4 ml-1' />
          </Button>
        </DropdownMenuTrigger>

        {selectedItems.length > 0 && (
          <button
            type='button'
            onClick={handleReset}
            className='text-muted-foreground absolute right-7 cursor-pointer transition-transform duration-100 hover:scale-110 hover:text-primary p-0 bg-transparent border-0'
            aria-label='Reset selection'
          >
            <X className='h-3.5 w-3.5' />
          </button>
        )}
      </div>

      <DropdownMenuContent className='w-[200px]' align='end'>
        <Input
          type='text'
          placeholder={title}
          className='w-full px-2 py-1 border-b focus:outline-none mb-2'
          ref={inputRef}
          onChange={handleSearch}
        />

        {loading ? (
          <p className='text-muted-foreground text-center py-2'>Loading...</p>
        ) : filteredItems.length > 0 ? (
          filteredItems.map(item => (
            <DropdownMenuCheckboxItem
              key={item.id}
              className='py-2'
              checked={selectedItems.includes(item.id)}
              onCheckedChange={checked => handleItemChange(item.id, checked)}
            >
              <div className='flex cursor-pointer items-center text-primary'>
                {item.icon
                  && (typeof item.icon === 'string' ? (
                    <img src={item.icon} alt='' className='h-4 w-4 rounded-full object-cover me-2' />
                  ) : (
                    <span className='me-2 flex items-center justify-center'>{item.icon}</span>
                  ))}
                {item.label}
              </div>
            </DropdownMenuCheckboxItem>
          ))
        ) : (
          <div className='flex flex-col justify-center items-center py-2'>
            <Image width={120} height={100} src={Logo} alt='' />
            <p className='text-muted-foreground text-sm mt-1.5'>Oops, </p>
            <p className='text-muted-foreground text-sm'>there is no result</p>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DropDownComponent;
