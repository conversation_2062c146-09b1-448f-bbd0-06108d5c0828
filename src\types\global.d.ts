import type React from 'react';

// Use type safe message keys with `next-intl`
type Messages = typeof import('../locales/en.json');

// eslint-disable-next-line
declare interface IntlMessages extends Messages {}

type IDataTable = {
  data: object[];
  pagination: {
    current_page: number;
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string | null;
    per_page: number;
    prev_page_url: string;
    total: number;
  };
};
export type IDropDownItems = { key: number; value: string };
export type ITableInterFace<T = any> = {
  columns: ColumnDef<T>[];
  dataTable: IDataTable;
  isLoading: boolean;
  pageNumber: number;
  title: string | '';
  setPerpage: React.Dispatch<React.SetStateAction<number>>;
  perpage: number;
  setPageNumber: React.Dispatch<React.SetStateAction<number>>;
  clasName?: string;
  component?: React.ReactNode;
  rowclickaction?: (object) => void;
};
