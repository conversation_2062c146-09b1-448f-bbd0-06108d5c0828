'use client';
import type { ColumnDef } from '@tanstack/react-table';
import type { DateRange } from 'react-day-picker';
import type { ITerminal } from '@/services/api/user/trading/terminals';

import { ChevronsUpDown, EllipsisVertical, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/badge';
import { DateTime } from '@/components/date-time';

import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import { terminalStatusMap } from '@/components/panel/trading/terminals/table-statuses';
import { showRegion } from '@/components/regions';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useSearchQuery } from '@/hooks/search-query';

import useTerminalTableQueries from '@/hooks/table-query';
import { useDestroyMutation, useListFetch } from '@/services/api/user/trading/terminals';
import TradingTerminalFiltersComponent from '../../../../../components/panel/trading/terminals/table-component';

export default function TerminalsTable() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [filterValue, setFilterValue] = useState('');
  const [per_page, setPerpage] = useState(15);
  const [columnSort, setColumnSort] = useState<{ sort_by: string; sort_direction: 'asc' | 'desc' } | null>(null);
  const search = useSearchQuery(filterValue, 500);
  const [date_from, setdateFrom] = useState<string>();
  const [date_to, setDateTo] = useState<string>();
  const [status, setSelectedStatuses] = useState<string[]>([]);
  const [region, setRegion] = useState<string[]>([]);
  const [project_id, setProject_id] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>(undefined);
  const [subject, setSubject] = useState<string>('');
  const [deleteId, setDeleteId] = useState<string>('');
  const [Opendialog, setOpenDialog] = useState(false);
  const queries = useTerminalTableQueries({
    page,
    per_page,
    search,
    date_from,
    date_to,
    status: status.join(','),
    sort_by: columnSort?.sort_by,
    sort_direction: columnSort?.sort_direction,
    region: region.join(','),
    project_id: project_id.join(',')
  });
  const {
    data: terminalsData,
    refetch,
    isLoading
  } = useListFetch(
    {
      ...queries
    },
    {
      enabled: true
    }
  );

  const handleSortAction = (sortType: string) => {
    if (columnSort?.sort_by === sortType) {
      const direction = columnSort.sort_direction === 'asc' ? 'desc' : 'asc';
      setColumnSort({ sort_by: sortType, sort_direction: direction });
    } else {
      setColumnSort({ sort_by: sortType, sort_direction: 'desc' });
    }
  };

  const destroyMutation = useDestroyMutation({
    onSuccess: () => {
      toast.success('Terminal deleted successfully');
      setOpenDialog(false);
      setSubject('');
      setDeleteId('');
      refetch();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to delete terminal');
    }
  });

  const handleDeleteClick = (terminal: ITerminal) => {
    setSubject(terminal?.name);
    setDeleteId(terminal?.id);
    setOpenDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (deleteId) {
      destroyMutation.mutate({ queryParams: { id: deleteId } });
    }
  };

  const columns: ColumnDef<ITerminal>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          className='mx-2'
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label='Select all'
        />
      ),
      cell: ({ row }) => (
        <button
          type='button'
          onClick={(e) => {
            e.stopPropagation();
          }}
          className='p-0 bg-transparent border-0 cursor-default'
          tabIndex={-1}
          aria-hidden='true'
        >
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => {
              row.toggleSelected(!!value);
            }}
            aria-label='Select row'
          />
        </button>
      ),
      enableSorting: false,
      enableHiding: false
    },
    {
      accessorKey: 'name',
      meta: {
        label: 'Name'
      },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex cursor-pointer items-center w-fit'
          onClick={() => handleSortAction('name')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSortAction('name');
            }
          }}
        >
          Name
          <ChevronsUpDown className='ml-2 h-3 w-3' />
        </div>
      ),
      cell: ({ row }) => {
        const terminal = row.original;
        return (
          <Link href={PAGE_ROUTES.PANEL_TRADING_TERMINALS_SHOW(terminal.id)} className='hover:underline'>
            {terminal.name}
          </Link>
        );
      }
    },
    {
      accessorKey: 'server_name',
      header: 'Server'
    },
    {
      accessorKey: 'login',
      header: 'Login'
    },
    {
      accessorKey: 'region',
      header: 'Region',
      cell: ({ row }) => {
        const terminal = row.original;
        const region = terminal.region;
        const regionObj = region ? showRegion(Number(region)) : { label: '', icon: '' };
        return (
          <div className='flex'>
            <img src={regionObj?.icon} alt='' className='size-5 rounded-full object-cover border border-white' />
            <span className='text-gray-600 text-sm ms-1'>{regionObj?.label}</span>
          </div>
        );
      }
    },
    {
      accessorKey: 'status',
      meta: { label: 'status' },

      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex cursor-pointer w-fit items-center'
          onClick={() => handleSortAction('status')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSortAction('status');
            }
          }}
        >
          Status
          <ChevronsUpDown className='ml-2 h-3 w-3' />
        </div>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as number;
        const { label, variant } = terminalStatusMap[status] ?? { label: 'Unknown', variant: 'default' };
        return (
          <div>
            <Badge value={label} variant={variant} />
          </div>
        );
      }
    },
    {
      accessorKey: 'updated_at',
      header: 'Update At',
      cell: ({ row }) => {
        const date = new Date(row?.getValue('updated_at'));
        return <DateTime value={date.toISOString()} />;
      }
    },

    {
      accessorKey: 'Actions',
      enableHiding: false,
      cell: ({ row }) => {
        const terminal = row?.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size='sm'
                variant='outline'
                className='flex items-center text-[13px] text-gray-700 dark:border dark:border-input dark:bg-background '
              >
                <EllipsisVertical />
                Actions
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align='end'>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(terminal.id)}>
                Copy terminal ID
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(terminal.login)}>
                Copy login
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className='text-red-600'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDeleteClick(terminal);
                }}
              >
                <Trash2 className='mr-2 h-4 w-4' />
                Delete Terminal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ];

  const tableClickAction = (origin: { id: string }) => {
    router.push(`/panel/trading/terminals/detail/${origin.id}`);
  };

  useEffect(() => {
    if (selectedDateRange === undefined) {
      setdateFrom('');
      setDateTo('');
    }
    if (selectedDateRange?.from && selectedDateRange?.to) {
      const fromDate = selectedDateRange.from.toISOString().split('T')[0];
      const toDate = selectedDateRange.to.toISOString().split('T')[0];
      setdateFrom(fromDate);
      setDateTo(toDate);
    }
  }, [selectedDateRange]);

  return (
    <div className='mt-5'>
      <div className='flex gap-2 mb-5 items-end justify-between'>
        <div>
          <h4 className='text-primary'>Terminals</h4>
          <p className='text-muted-foreground'>Central Hub for Personal Customization</p>
        </div>

        <div className='flex gap-2'>
          <Link href={PAGE_ROUTES.PANEL_TRADING_TERMINALS_DEPLOY}>
            <Button variant='outline' size='sm' className='w-[80px]'>
              Export
            </Button>
          </Link>
          <Button
            onClick={() => {
              router.push('/panel/trading/terminals/deploy');
            }}
            variant='blue_primary'
            className='cursor-pointer hover:shadow-lg transition-shadow duration-200 text-sm'
            size='sm'
          >
            <Plus className='size-3.5' />
            Deploy
          </Button>
        </div>
      </div>
      <DataTable
        pageNumber={page}
        setPageNumber={setPage}
        perpage={per_page}
        setPerpage={setPerpage}
        columns={columns}
        rowclickaction={tableClickAction}
        dataTable={{
          data: Array.isArray(terminalsData) ? terminalsData : terminalsData?.data || [],
          pagination: terminalsData?.pagination || {
            current_page: 1,
            first_page_url: '',
            from: 0,
            last_page: 1,
            last_page_url: '',
            next_page_url: null,
            per_page,
            prev_page_url: '',
            total: 0
          }
        }}
        isLoading={isLoading}
        title='Terminals'
        component={(
          <TradingTerminalFiltersComponent
            setFilterValue={setFilterValue}
            filterValue={filterValue}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            selectedStatus={status}
            setSelectedStatus={setSelectedStatuses}
            region={region}
            setRegion={setRegion}
            project={project_id}
            setProject={setProject_id}
          />
        )}
      />
      <DeleteConfirmDialog
        open={Opendialog}
        setOpen={setOpenDialog}
        confirmSubject={subject}
        onDeleteFunction={handleDeleteConfirm}
        isLoading={destroyMutation?.isPending}
      />
    </div>
  );
}
