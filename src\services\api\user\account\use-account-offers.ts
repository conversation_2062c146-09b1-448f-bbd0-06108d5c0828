import type { IUseQueryFactoryProps } from '@/hooks/api/use-query-factory/use-query-factory.type';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { API_ROUTES } from '@/configs/api-routes';
import { useQueryFactory } from '@/hooks/api/use-query-factory';

type IAccountOffersResponse = {
  ok: boolean;
  msg: string;
  data: {
    welcome_days: number;
  };
};

export const useAccountOffers = (options?: Pick<IUseQueryFactoryProps<IAccountOffersResponse>, 'enabled'>) => {
  return useQueryFactory<IAccountOffersResponse>({
    queryKey: API_QUERY_KEY.ACCOUNT_OFFERS,
    url: API_ROUTES.ACCOUNT_OFFERS,
    ...options
  });
};
