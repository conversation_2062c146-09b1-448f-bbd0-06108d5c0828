'use client';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useGetVpsDtails } from '@/services/api/user/trading/vps';
import CahngeVpsPassword from './change-password';
import Overview from './over-view';

const VpsDetailComponent = () => {
  const [selectedTabs, setSekectedTabs] = useState('Overview');
  const params = useParams();
  const id = params.id as string;
  const { data: VpsDatail } = useGetVpsDtails(id);

  const trigegerTabs = [
    { title: 'Overview', value: 'Overview' },
    { title: 'Cahnge Password', value: 'CahngePassword' }
  ];

  const returnTabContent = () => {
    switch (selectedTabs) {
      case 'Overview':
        return (
          <div className='w-full'>
            {/* {isTerminalLoading ? (
              <SkeletonCard />
            ) : (
              <Overview
                detailInfo={VpsDatail}
              />

            )} */}
            <Overview VpsDatail={VpsDatail} />
          </div>
        );
      case 'CahngePassword':
        return <CahngeVpsPassword />;
      case 'Invoices':
      case 'Settings':
      case 'Support':
      case 'Activity':
      case 'Terminal Logs':
        return null;

      default:
        return <div>Tab content not found</div>;
    }
  };
  return (
    <>
      <div className='flex justify-between items-center'>
        <div className='my-2'>
          <h1 className='font-bold text-lg'>{VpsDatail?.name}</h1>
        </div>
        <div className='flex gap-2'>
          {/* <Link className='cursor-pointer' href='https://docs.tradevps.net/products/trading-terminal'>
            <Button variant='outline' size='sm'>
              <HelpCircle className='w-4 h-4 mr-2' />
              How Works?
            </Button>
          </Link>
          <Link href='/panel/trading/terminals/deploy'>
            <Button size='sm'>
              <PlusIcon />
              Deploy
            </Button>
          </Link> */}

          {/* <Button size='sm' onClick={() => setSekectedTabs('New Task')}>
            <PlusIcon />
            New Task
          </Button> */}
        </div>
      </div>
      <Tabs value={selectedTabs} onValueChange={setSekectedTabs} className='mt-1'>
        <TabsList className='w-full py-5 md:py-0 flex justify-between '>
          <div>
            {trigegerTabs.map(trigger => (
              <TabsTrigger key={trigger.value} className='cursor-pointer  px-5' value={trigger.value}>
                {trigger.title}
              </TabsTrigger>
            ))}
          </div>
          <div className='text-end'></div>
        </TabsList>
        <TabsContent value={selectedTabs}>
          <div className='mt-7'>{returnTabContent()}</div>
        </TabsContent>
      </Tabs>
    </>
  );
};
export default VpsDetailComponent;
