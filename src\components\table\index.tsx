'use client';
import type { ColumnFiltersState, SortingState, VisibilityState } from '@tanstack/react-table';
import type { IDataTable, ITableInterFace } from '@/types/global';
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table';
import { Settings2 } from 'lucide-react';
import * as React from 'react';
import PulseLoader from 'react-spinners/PulseLoader';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import CustomPagination from '../pagination';
import { Card, CardHeader } from '../ui/card';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from '../ui/dropdown-menu';

export type Payment = {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'success' | 'failed';
  email: string;
};
export default function DataTable({
  columns,
  title,
  dataTable,
  isLoading,
  pageNumber,
  setPageNumber,
  perpage,
  setPerpage,
  clasName,
  rowclickaction,
  component
}: ITableInterFace) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const totalPage = Math.ceil(dataTable?.pagination?.total / perpage);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [oldData, setOldData] = React.useState<IDataTable[]>([]);
  const table = useReactTable({
    data: oldData, // ,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    }
  });
  function getPaginationText(currentPage: number): string {
    currentPage = Math.max(1, Math.min(pageNumber, totalPage));
    const start = (currentPage - 1) * perpage + 1;
    const end = Math.min(currentPage * perpage, dataTable?.pagination?.total);
    return `${start} - ${end} of ${dataTable?.pagination?.total}`;
  }
  const paginationText = getPaginationText(pageNumber);
  const handlePerpage = (value: number) => {
    setPerpage(value);
  };
  const handleChangePage = (value: number) => {
    setPageNumber(value);
  };
  if (process.env.NODE_ENV === 'development') {
    console.warn('Debug information:', totalPage);
  }
  React.useEffect(() => {
    if (dataTable?.data?.length === 0 && isLoading === false) {
      setOldData([]);
    } else if (!isLoading) {
      setOldData(dataTable?.data as IDataTable[]);
    }
  }, [isLoading, dataTable?.data, dataTable?.data?.length]);
  return (
    <div className={clasName || 'w-full'}>
      <Card className={`pb-${dataTable?.pagination?.next_page_url || dataTable?.pagination?.prev_page_url ? 4 : 0}`}>
        <CardHeader>
          <div className='flex items-center gap-3 justify-between'>
            {component}
            <div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size='sm' className='text-[13px] w-[90px]' variant='table'>
                    <Settings2 />
                    Columns
                    {' '}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className='w-[180px]' align='end'>
                  {table
                    ?.getAllColumns()
                    ?.filter(column => column.getCanHide())
                    ?.map((column) => {
                      const label
                        = typeof column.columnDef.header === 'function'
                          ? (column.columnDef.meta as { label?: string }).label
                          : column.columnDef.header;
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className='capitalize'
                          checked={column.getIsVisible()}
                          onCheckedChange={value => column.toggleVisibility(!!value)}
                        >
                          {label}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <div className='mt-2'>
          <Table>
            {dataTable?.data.length || oldData?.length ? (
              <TableHeader className='bg-muted border-e'>
                {table?.getHeaderGroups().length
                  && table?.getHeaderGroups()?.map((headerGroup) => {
                    return (
                      <TableRow key={headerGroup.id} className='border-e'>
                        {headerGroup?.headers?.map(header => (
                          <TableHead
                            className='text-muted-foreground border-e'
                            suppressHydrationWarning
                            key={header.id}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(header?.column?.columnDef?.header, header?.getContext())}
                          </TableHead>
                        ))}
                      </TableRow>
                    );
                  })}
              </TableHeader>
            ) : null}

            <TableBody
              className={` transition-all duration-300 ${
                isLoading && oldData?.length ? 'blur-[1.5px] opacity-80' : ''
              }`}
            >
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={() => rowclickaction && rowclickaction(row.original)}
                    className={rowclickaction ? 'cursor-pointer hover:bg-muted' : ''}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        className={`px-4 py-3 border-${
                          dataTable?.pagination?.next_page_url || dataTable?.pagination?.prev_page_url ? 'b' : null
                        } border-e align-middle [&:has([role=checkbox])]:pe-0 text-primary font-normal`}
                        key={cell.id}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}

                    {isLoading && oldData?.length ? (
                      <TableCell className='w-full absolute top-0 right-0  h-[70%] flex justify-center items-center '>
                        <PulseLoader
                          color='blue'
                          loading={isLoading}
                          // cssOverride={override}
                          size={10}
                          aria-label='Loading Spinner'
                          data-testid='loader'
                        />
                      </TableCell>
                    ) : null}
                  </TableRow>
                ))
              ) : isLoading ? (
                <TableRow className='flex justify-center items-center h-[300px]'>
                  <TableCell colSpan={columns?.length} className='h-24 text-center'>
                    <PulseLoader
                      color='blue'
                      loading={isLoading}
                      // cssOverride={override}
                      size={10}
                      aria-label='Loading Spinner'
                      data-testid='loader'
                    />
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow className='h-[15rem] '>
                  <TableCell colSpan={columns?.length} className='h-24 text-center'>
                    <p className='text-gray-500'>{`No ${title} found.`}</p>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {dataTable?.pagination?.next_page_url || dataTable?.pagination?.prev_page_url ? (
          <CustomPagination
            handlePage={handleChangePage}
            perpage={perpage}
            totalPage={totalPage}
            pageNumber={pageNumber}
            paginationText={paginationText}
            handlePerpage={handlePerpage}
          />
        ) : null}
      </Card>
    </div>
  );
}
