describe('Marketing template', () => {
  describe('Render method', () => {
    // it('should have 6 marketplace menu items', () => {
    //   render(
    //     <NextIntlClientProvider locale='en' messages={messages}>
    //       <MarketingTemplate>{null}</MarketingTemplate>
    //     </NextIntlClientProvider>
    //   );
    //   const menuItemList = screen.getAllByRole('listitem');
    //   expect(menuItemList).toHaveLength(6);
    // });
    // it('should have correct marketplace navigation items', () => {
    //   render(
    //     <NextIntlClientProvider locale='en' messages={messages}>
    //       <MarketingTemplate>{null}</MarketingTemplate>
    //     </NextIntlClientProvider>
    //   );
    //   const expectedItems = ['Trading Bots', 'Indicators', 'Services', 'AI Models', 'APIs', 'Solutions'];
    //   expectedItems.forEach((item) => {
    //     expect(screen.getByText(item)).toBeInTheDocument();
    //   });
    // });
    // it('should have a link to TradeVPS', () => {
    //   render(
    //     <NextIntlClientProvider locale='en' messages={messages}>
    //       <MarketingTemplate>{null}</MarketingTemplate>
    //     </NextIntlClientProvider>
    //   );
    //   const copyrightSection = screen.getByText(/© Copyright/);
    //   const copyrightLink = within(copyrightSection).getByRole('link');
    //   expect(copyrightLink).toHaveAttribute('href', 'https://tradevps.net');
    // });
  });
});
