import antfu from '@antfu/eslint-config';
import nextPlugin from '@next/eslint-plugin-next';
import jestDom from 'eslint-plugin-jest-dom';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import playwright from 'eslint-plugin-playwright';
import testingLibrary from 'eslint-plugin-testing-library';

export default antfu({
  react: true,
  typescript: true,

  lessOpinionated: true,
  isInEditor: false,

  stylistic: {
    semi: true,
    quotes: 'single',
    jsx: true,
    commaDangle: 'never',
  },

  formatters: {
    css: true,
  },

  include: ['src/**/*.{js,jsx,ts,tsx}'],


  ignores: [
    '.storybook/**/*',
    '*.config.*',
    'next-env.d.ts',
    'tests/*/*.e2e.ts',
    'tests/*/*.spec.ts',
    'vitest-setup.ts',
  ],
}, jsxA11y.flatConfigs.recommended, {
  plugins: {
    '@next/next': nextPlugin,
  },
  rules: {
    ...nextPlugin.configs.recommended.rules,
    ...nextPlugin.configs['core-web-vitals'].rules,
    '@typescript-eslint/no-explicit-any': 'off',
    'ts/no-explicit-any': 'off',
    'react/no-array-index-key': 'warn',
    'react-hooks/exhaustive-deps': 'warn',
    'react-dom/no-missing-button-type': 'error',
    'react-refresh/only-export-components': 'off',
    '@next/next/no-img-element': 'warn',
    'react/no-unstable-default-props': 'warn',
    'react-hooks-extra/no-direct-set-state-in-use-effect': 'off',
    'style/comma-dangle': ['error', 'never'],
    'style/jsx-quotes': ['error', 'prefer-single'],
    'style/multiline-ternary': 'off'
  },
}, {
  files: [
    '**/*.test.ts?(x)',
  ],
  ...testingLibrary.configs['flat/react'],
  ...jestDom.configs['flat/recommended'],
}, {
  files: [
    '**/*.spec.ts',
    '**/*.e2e.ts',
  ],
  ...playwright.configs['flat/recommended'],
}, {
  files: ['src/types/**/*.ts', 'src/**/*.type.ts', 'src/**/*.types.ts'],
  rules: {
    '@typescript-eslint/no-explicit-any': 'off',
    'ts/no-explicit-any': 'off',
  },
}, {
  rules: {
    'antfu/no-top-level-await': 'off',
    'style/brace-style': ['error', '1tbs'],
    'ts/consistent-type-definitions': ['error', 'type'],
    'react/prefer-destructuring-assignment': 'off',
    'node/prefer-global/process': 'off',
    'test/padding-around-all': 'error',
    'test/prefer-lowercase-title': 'off',
    'eslint-comments/no-unlimited-disable': 'off',
  },
});





