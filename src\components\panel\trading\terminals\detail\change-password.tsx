// 'use client'
// import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import React, { Fragment } from 'react';
// const CahngeVpsPassword = () => {
//   return (
//     <Fragment>
//       {/* <div className='flex justify-center '> */}
//       <form className='flex flex-col gap-6' onSubmit={handleSubmit(onSubmit)}>
//         <div className='flex flex-col items-center gap-2 text-center'>
//           <h1 className='text-2xl font-bold'>Login to your account</h1>
//           <p className='text-balance text-sm text-muted-foreground'>Enter your email below to login to your account</p>
//         </div>
//         <div className='grid gap-6'>
//           <div className='grid gap-2'>
//             <Label htmlFor='email'>Email</Label>
//             <Input id='email' type='email' placeholder='<EMAIL>' {...register('email')} />
//             {errors.email && <p className='text-red-500 text-sm'>{errors.email.message}</p>}
//           </div>
//           <div className='grid gap-2'>

//             <Input id='password' type='password' {...register('password')} />
//             {/* {errors.password && <p className='text-red-500 text-sm'>{errors.password.message}</p>} */}
//           </div>
//           <Button type='submit' className='w-full' isLoading={loginApi?.isPending}>
//             Login
//           </Button>

//         </div>
//         <div className='text-center text-sm'>
//           Don&apos;t have an account?
//           {' '}
//           <Link href={PAGE_ROUTES.REGISTER} className='underline underline-offset-4'>
//             Sign up
//           </Link>
//         </div>
//       </form>
//       {/* </div> */}
//     </Fragment>
//   );
// }

// export default CahngeVpsPassword;
