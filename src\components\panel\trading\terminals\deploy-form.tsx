'use client';
import type { IProject } from '@/types/project';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Check, ChevronsUpDown, HelpCircle } from 'lucide-react';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { IMAGE_URL } from '@/configs/image-url';
import { cn } from '@/lib/utils';
import { useProjectsList } from '@/services/api/user/projects';
import { useDeployMutation, useRegionsFetch } from '@/services/api/user/trading/terminals';
import { getConsistentColor } from '@/utils/avatar';

type IRegion = {
  id: number;
  label: string;
  icon?: string;
};

const formSchema = z.object({
  region: z.number().optional(),
  project: z.string().optional(),
  name: z.string().optional(),
  login: z.string().min(1, 'Login is required'),
  password: z.string().min(1, 'Password is required'),
  server_name: z.string().min(1, 'Server name is required')
});

export function DeployTerminal() {
  const router = useRouter();
  const { data: projectsResponse, isLoading: isProjectsLoading } = useProjectsList();
  const { data: regionsResponse, isLoading: isRegionsLoading } = useRegionsFetch();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      region: undefined,
      project: '',
      name: '',
      login: '',
      password: '',
      server_name: ''
    }
  });

  const [regions, setRegions] = React.useState<any>([]);
  const [projects, setProjects] = React.useState<any>([]);
  const [openRegion, setOpenRegion] = React.useState(false);
  const [openProject, setOpenProject] = React.useState(false);

  const projectColors = useMemo(() => {
    return (
      projects?.reduce(
        (acc: Record<string | number, string>, project: IProject) => ({
          ...acc,
          [project.id]: getConsistentColor(project.name)
        }),
        {} as Record<string | number, string>
      ) || {}
    );
  }, [projects]);

  React.useEffect(() => {
    if (regionsResponse && Array.isArray(regionsResponse)) {
      const formattedRegions = regionsResponse.map(region => ({
        id: Number(region.id),
        label: region.label,
        icon: region.icon
      }));
      setRegions(formattedRegions);

      if (formattedRegions.length > 0 && formattedRegions[0]?.id !== undefined) {
        form.setValue('region', formattedRegions[0].id);
      }
    }
  }, [regionsResponse, form]);

  React.useEffect(() => {
    if (projectsResponse) {
      setProjects(projectsResponse);
      if (projectsResponse[0]?.id) {
        form.setValue('project', projectsResponse[0].id);
      }
    }
  }, [projectsResponse, form]);
  const { theme } = useTheme();

  const imgQuestionSrc = theme === 'dark' ? IMAGE_URL.QUESTION_ASKED_DARK : IMAGE_URL.QUESTION_ASKED;
  const imgSupportSrc = theme === 'dark' ? IMAGE_URL.CONTACT_SUPPORT_DARK : IMAGE_URL.CONTACT_SUPPORT;
  const deployMutation = useDeployMutation({
    onSuccess: () => {
      toast.success('Terminal deployed successfully');
      router.push('/panel/trading/terminals');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to deploy terminal');
    }
  });
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    deployMutation.mutate({ body: values });
  };
  const faqItems = [
    {
      question: 'How is pricing determined for each plan?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    },
    {
      question: 'What payment methods are accepted for subscriptions ?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    },
    {
      question: 'Are there any hidden fees in the pricing ?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    },
    {
      question: 'Is there a discount for annual subscriptions ?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    },
    {
      question: 'Do you offer refunds on subscription cancellations ?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    },
    {
      question: 'Can I add extra features to my current plan ?',
      answer:
        'Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project\'s needs and budget...'
    }
    // Ad
    // d other FAQ items
  ];

  return (
    <div className='flex flex-col gap-5 lg:gap-7.5'>
      <div className='flex justify-between'>
        <div>
          <h1 className='text-primary '>Deploy Terminal</h1>
        </div>
        <div className='flex gap-2'>
          <Link href='/panel/trading/terminals'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back
            </Button>
          </Link>
          <Link href='https://docs.tradevps.net/products/trading-terminal' target='_blank'>
            <Button variant='outline' size='sm'>
              <HelpCircle className='w-4 h-4 mr-2' />
              How Works?
            </Button>
          </Link>
        </div>
      </div>

      <div className='grid lg:grid-cols-2 gap-5'>
        <Card>
          <CardHeader>
            <div className='flex justify-between items-center'>
              <CardTitle className='text-primary'>Deploy Terminal</CardTitle>
              <div className='flex items-center gap-2'>
                <span className='text-lg font-semibold line-through text-muted-foreground'>$3.99</span>
                <span className='text-lg font-semibold text-primary'>$2.99</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
                <FormField
                  control={form.control}
                  name='region'
                  render={({ field }) => {
                    return (
                      <FormItem className='relative'>
                        <FormLabel>Region</FormLabel>
                        <Popover open={openRegion} onOpenChange={setOpenRegion}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant='outline'
                                role='combobox'
                                aria-expanded={openRegion}
                                className={cn(
                                  'w-full justify-between text-primary',
                                  field.value === undefined && 'text-muted-foreground'
                                )}
                                disabled={isRegionsLoading}
                              >
                                <div className='flex items-center gap-2 flex-1 justify-start'>
                                  {isRegionsLoading ? (
                                    'Loading regions...'
                                  ) : field.value !== undefined ? (
                                    <>
                                      {regions.find((region: IRegion) => region.id === field.value)?.icon && (
                                        <img
                                          src={regions.find((region: IRegion) => region.id === field.value)?.icon}
                                          alt=''
                                          className='h-4 w-4 rounded-full object-cover'
                                        />
                                      )}
                                      {regions.find((region: IRegion) => region.id === field.value)?.label
                                        || 'Select region'}
                                    </>
                                  ) : (
                                    'Select region'
                                  )}
                                </div>
                                <ChevronsUpDown className='h-4 w-4 shrink-0 opacity-50' />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent
                            className='w-[--radix-popover-trigger-width] p-0 z-[60]'
                            align='start'
                            side='bottom'
                            sideOffset={4}
                          >
                            <Command>
                              <CommandInput placeholder='Search region...' className='h-9' />
                              <CommandEmpty>No region found.</CommandEmpty>
                              <CommandGroup>
                                {regions.map((region: IRegion) => (
                                  <CommandItem
                                    key={region.id}
                                    value={String(region.label)}
                                    onSelect={() => {
                                      form.setValue('region', region.id);
                                      setOpenRegion(false);
                                    }}
                                    className='flex items-center gap-2'
                                  >
                                    {region.icon && (
                                      <img src={region.icon} alt='' className='h-4 w-4 rounded-full object-cover' />
                                    )}
                                    <span className='flex-1 text-left'>{region.label}</span>
                                    <Check
                                      className={cn(
                                        'h-4 w-4 ml-auto',
                                        field.value === region.id ? 'opacity-100' : 'opacity-0'
                                      )}
                                    />
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  key='project-field'
                  control={form.control}
                  name='project'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project</FormLabel>
                      <Popover open={openProject} onOpenChange={setOpenProject}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant='outline'
                              role='combobox'
                              aria-expanded={openProject}
                              className='w-full justify-between text-primary'
                            >
                              {isProjectsLoading ? (
                                'Loading...'
                              ) : field.value ? (
                                <div className='flex items-center gap-2'>
                                  <div
                                    className={cn(
                                      'flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs font-medium text-white',
                                      projectColors[field.value]
                                    )}
                                  >
                                    {projects
                                      ?.find((project: IProject) => project.id === field.value)
                                      ?.name
                                      .charAt(0)
                                      .toUpperCase()}
                                  </div>
                                  {projects?.find((project: IProject) => project.id === field.value)?.name}
                                </div>
                              ) : (
                                'Select project'
                              )}
                              <ChevronsUpDown className='h-4 w-4 shrink-0 opacity-50' />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className='w-[--radix-popover-trigger-width] p-0' align='start'>
                          <Command>
                            <CommandInput placeholder='Search project...' className='h-9' />
                            <CommandEmpty>No project found.</CommandEmpty>
                            <CommandGroup>
                              {isProjectsLoading ? (
                                <CommandItem disabled>Loading projects...</CommandItem>
                              ) : projects && projects.length > 0 ? (
                                projects.map((project: IProject) => (
                                  <CommandItem
                                    key={project.id}
                                    value={project.name}
                                    onSelect={() => {
                                      field.onChange(project.id);
                                      setOpenProject(false);
                                    }}
                                    className='flex items-center gap-2'
                                  >
                                    <div
                                      className={cn(
                                        'flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs font-medium text-white',
                                        projectColors[project.id]
                                      )}
                                    >
                                      {project.name.charAt(0).toUpperCase()}
                                    </div>
                                    <span className='flex-1 text-left'>{project.name}</span>
                                    <Check
                                      className={cn(
                                        'h-4 w-4 ml-auto',
                                        field.value === project.id ? 'opacity-100' : 'opacity-0'
                                      )}
                                    />
                                  </CommandItem>
                                ))
                              ) : (
                                <CommandItem disabled>No projects available</CommandItem>
                              )}
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                      {!isProjectsLoading && (!projects || projects.length === 0) && (
                        <p className='text-sm text-muted-foreground mt-2'>
                          No projects found.
                          <Link href='/panel/projects/create' className='text-primary hover:underline ml-1'>
                            Create a new project
                          </Link>
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  key='name-field'
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter terminal name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  key='login-field'
                  control={form.control}
                  name='login'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Login</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter login' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  key='password-field'
                  control={form.control}
                  name='password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type='password' placeholder='Enter password' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  key='server-name-field'
                  control={form.control}
                  name='server_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Server Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter server name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type='submit' className='w-full'>
                  {deployMutation.isPending ? <div className='animate-spin mr-2'>⌛</div> : null}
                  Deploy Terminal
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-xl mb-4'>FAQ</CardTitle>
            <hr />
          </CardHeader>
          <CardContent>
            <Accordion type='single' collapsible className='w-full'>
              {faqItems.map((item, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className='cursor-pointer text-lg text-primary font-normal'>
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className='text-muted-foreground '>{item.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      </div>

      {/* Support Cards */}
      <div className='grid lg:grid-cols-2 gap-5 '>
        <SupportCard
          title='Questions?'
          description='Visit our Help Center for detailed assistance on billing, payments, and subscriptions.'
          linkText='Go to Help Center'
          linkHref='https://docs.tradevps.net'
          imageSrc={imgQuestionSrc}
        />

        <SupportCard
          title='Contact Support'
          description='Need assistance? Contact our support team for prompt, personalized help your queries & concerns.'
          linkText='Contact Support'
          linkHref='/panel/support'
          imageSrc={imgSupportSrc}
        />
      </div>
      <div></div>
    </div>
  );
}

// Support Card Component
function SupportCard({
  title,
  description,
  linkText,
  linkHref,
  imageSrc
}: {
  title: string;
  description: string;
  linkText: string;
  linkHref: string;
  imageSrc: string;
}) {
  return (
    <Card className='pb-0 gap-1'>
      <CardContent className='px-5 h-[180px] mb-0 pb-0'>
        <div className='flex flex-wrap md:flex-nowrap items-center gap-3 md:gap-10'>
          <div className='flex flex-col items-start gap-3'>
            <h2 className='text-primary '>{title}</h2>
            <p className='text-muted-foreground '>{description}</p>
          </div>
          <Image src={imageSrc} alt={description} width={120} height={140} />
        </div>
      </CardContent>
      <Separator />
      <CardFooter className='flex justify-center items-center pb-1 mt-0'>
        <Link
          href={linkHref}
          className='border-b-2 text-sm border-dotted border-gray-500 my-1 text-primary hover:text-blue-400 hover:border-blue-400'
        >
          {linkText}
        </Link>
      </CardFooter>
    </Card>
  );
}
