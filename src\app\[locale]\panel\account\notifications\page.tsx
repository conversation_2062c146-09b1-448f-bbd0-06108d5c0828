'use client';
import { formatDistanceToNow } from 'date-fns';
import { Bell, BellOff, EyeIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import CustomPagination from '@/components/pagination';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import {
  useMarkAllNotificationsAsReadMutation,
  useMarkAsReadMutation,
  useNotificationsList
} from '@/services/api/user/notifications';

const NotificationSkeleton = () => (
  <div className='flex items-start justify-between p-4 rounded-lg border my-5'>
    <div className='flex items-start space-x-4'>
      <Skeleton className='h-10 w-10 rounded-full' />
      <div className='space-y-2'>
        <Skeleton className='h-4 w-[200px]' />
        <Skeleton className='h-3 w-[150px]' />
      </div>
    </div>
    <Skeleton className='h-8 w-[90px]' />
  </div>
);
const NotificationsPage = () => {
  const [page, setPage] = useState(1);
  const [unreadMessage, setUnreadMessage] = useState(false);
  const [per_page, setPerpage] = useState(10);
  const {
    data: notificationsData,
    isLoading,
    refetch,
    isFetching
  } = useNotificationsList(page, per_page, unreadMessage);
  const totalPage = Math.ceil(notificationsData?.pagination?.total / per_page);
  const [readMarkId, setReadMarkId] = useState<string>('');
  function getPaginationText(currentPage: number): string {
    currentPage = Math.max(1, Math.min(page, 4));
    const start = (currentPage - 1) * per_page + 1;
    const end = Math.min(currentPage * per_page, 36);
    return `${start} - ${end} of ${36}`;
  }

  const paginationText = getPaginationText(page);
  const pageLoading = isLoading || isFetching;
  const markAsReadMutation = useMarkAsReadMutation({
    onSuccess: () => {
      refetch();
      toast.success('Notification marked as read');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to mark notification as read');
    }
  });
  const handleMarkAsRead = (id: string) => {
    setReadMarkId(id);
    markAsReadMutation.mutate({ params: { id } });
  };
  const markAllNotificationsAsRead = useMarkAllNotificationsAsReadMutation({
    onSuccess: () => {
      refetch();
      toast.success('Notification marked as read');
    }
  });
  const handlemarkAllNotificationsAsRead = () => {
    markAllNotificationsAsRead.mutate({ params: { id: 1 } });
  };
  return (
    <Card className='mt-10'>
      <CardHeader className='flex flex-row justify-between items-center py-1'>
        <p className=' text-lg'>Notifications</p>
        <div className='flex items-center'>
          <button
            type='button'
            className='text-blue-600 dark:text-blue-300 flex items-center me-5 text-[15px] cursor-pointer hover:text-blue-600 me-3 bg-transparent p-0 border-none focus:outline-none focus:ring-0 underline underline-offset-4 decoration-dotted decoration-blue-500'
            onClick={() => handlemarkAllNotificationsAsRead()}
          >
            <EyeIcon className='size-5 text-blue-500 dark:text-blue-300 me-1' />
            Mark all notifications as read
          </button>
          <p className='text-sm me-2 text-muted-foreground font-semibold'>Unread Notifications</p>

          <Switch
            onClick={() => setUnreadMessage(!unreadMessage)}
            className='cursor-pointer data-[state=checked]:bg-blue-600'
          />
        </div>
      </CardHeader>
      <Separator />
      <CardContent>
        {isLoading ? (
          <>
            <NotificationSkeleton />
            <NotificationSkeleton />
            <NotificationSkeleton />
          </>
        ) : notificationsData?.data?.length > 0 ? (
          <div className='mt-3'>
            {notificationsData?.data?.map((item: any, index: number) => {
              const bellIconType = item?.read_at ? <BellOff /> : <Bell />;
              const isLastItem = index === notificationsData?.data?.length - 1;
              return (
                <div key={item?.id} className='flex items-start relative'>
                  {!isLastItem ? (
                    <div className='w-9 start-0 top-9 absolute bottom-0 rtl:-translate-x-1/2 translate-x-1/2 border-s border-s-gray-300'></div>
                  ) : null}

                  <div className='flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600'>
                    {bellIconType}
                  </div>
                  <div className='ps-2.5 mb-7 text-md grow'>
                    <div className='flex justify-between'>
                      <div className='flex flex-col me-5'>
                        <div className='text text-primary'>{item?.data?.name}</div>
                        <p className='text-sm text-muted-foreground'>
                          {formatDistanceToNow(new Date(item?.created_at), {
                            addSuffix: true
                          })}
                        </p>
                      </div>
                      <div className='flex items-center'>
                        <button
                          type='button'
                          className='text-blue-600 dark:text-blue-300 me-5 text-[15px] cursor-pointer hover:text-blue-600 me-3 bg-transparent p-0 border-none focus:outline-none focus:ring-0 underline underline-offset-4 decoration-dotted decoration-blue-500'
                        >
                          view
                        </button>
                        {/* <Button size='sm' variant='outline' className='me-3'>view</Button> */}
                        {!item?.read_at && (
                          <Button
                            onClick={() => handleMarkAsRead(item.id)}
                            disabled={markAsReadMutation.isPending}
                            variant='table'
                            className='w-fit text-sm'
                            size='sm'
                          >
                            {readMarkId === item?.id && markAsReadMutation.isPending ? (
                              <CyrcleSvg />
                            ) : (
                              <EyeIcon size='sm' />
                            )}
                            Mark as read
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div>
            {!pageLoading && notificationsData?.data?.length === 0 ? (
              <div className='text-center py-8 '>
                <Bell className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                <h3 className='text-lg font-medium text-gray-900'>No notifications</h3>
                <p className='text-sm text-gray-500'>You don't have any notifications yet.</p>
              </div>
            ) : null}
          </div>
        )}
      </CardContent>

      {!isLoading && notificationsData?.pagination?.total > 15 ? (
        <div>
          <Separator className='-mt-5' />
          <div className='mt-3'>
            <CustomPagination
              pageNumber={page}
              totalPage={totalPage}
              perpage={per_page}
              handlePage={setPage}
              handlePerpage={setPerpage}
              paginationText={paginationText}
            />
          </div>
        </div>
      ) : null}
    </Card>
  );
};

export default NotificationsPage;
