import type { AxiosError } from 'axios';
import type { ResponseErrorType } from 'types/response';
import { ResponseStatusCode } from 'types/response-status';

export const handleRequestError = (failureCount: number, error: AxiosError<ResponseErrorType>): boolean => {
  if (
    error.response?.status === ResponseStatusCode.NotFound
    || error.response?.status === ResponseStatusCode.InternalServerError
    || error.response?.status === ResponseStatusCode.Unauthorized
  ) {
    return false;
  }
  return failureCount <= 1;
};
