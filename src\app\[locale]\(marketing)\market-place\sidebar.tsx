'use client';
import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

const MarketPlaceSideBare = () => {
  const [activeSidebar, setActiveSidebar] = useState<number | null>(null);
  const [openItems, setOpenItems] = useState<{ [key: number]: boolean }>({});

  const toggleItem = (id: number) => {
    setOpenItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
    setActiveSidebar(id === activeSidebar ? null : id);
  };

  const sideBarItems = [
    { id: 1, title: 'Featured', children: [] },
    { id: 2, title: 'Models', children: [] },
    {
      id: 3,
      title: 'Copilot',
      children: [
        { id: 31, title: 'Copilot' },
        { id: 32, title: 'Copilot' }
      ]
    },
    {
      id: 4,
      title: 'Apps',
      children: [
        { id: 41, title: 'Featured' },
        { id: 42, title: 'Models' }
      ]
    },
    {
      id: 5,
      title: 'Actions',
      children: [
        { id: 51, title: 'Featured' },
        { id: 52, title: 'Models' }
      ]
    }
  ];
  return (
    <div className='w-full h-screen px-3'>
      {sideBarItems.map(item => (
        <div key={item.id} className='w-full'>
          <div className='flex items-center my-1'>
            <div
              className={`w-1.5 rounded h-[20px] bg-${item.id === activeSidebar && !item.children.length ? 'blue-500' : 'transparent'} mr-1.5`}
            >
            </div>
            <Button
              onClick={() => toggleItem(item.id)}
              className={`flex cursor-pointer    bg-${item.id === activeSidebar && !item?.children.length ? 'gray-100' : 'transparent'} 
            font-${item.id === activeSidebar ? 'bold' : 'normal'} py-2 w-full 
            justify-between items-center px-2  rounded-lg hover:bg-gray-100`}
            >
              <p className='text-sm'>{item.title}</p>
              {item.children.length > 0
                && (openItems[item.id] ? (
                  <ArrowUpIcon width={10} height={10} />
                ) : (
                  <ArrowDownIcon width={10} height={10} />
                ))}
            </Button>
          </div>
          {openItems[item.id] && item.children.length > 0 && (
            <div className='mt-1'>
              {item.children.map(child => (
                <div key={child.id} className='flex items-center my-1'>
                  <div
                    className={`w-1.5 rounded h-[20px] bg-${child.id === activeSidebar ? 'blue-500' : 'transparent'} mr-1.5`}
                  >
                  </div>

                  <Button
                    onClick={() => setActiveSidebar(child.id)}
                    className={`py-1  bg-${child.id === activeSidebar ? 'gray-100' : 'transparent'}  pl-4 rounded-lg text-start w-full text-sm text-gray-600 cursor-pointer hover:bg-gray-100`}
                  >
                    {child.title}
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MarketPlaceSideBare;
