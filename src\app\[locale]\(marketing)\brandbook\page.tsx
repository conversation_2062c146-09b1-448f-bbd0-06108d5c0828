'use client';

import { motion } from 'framer-motion';
import { Box, Component, Grid, Image as ImageIcon, MessageSquare, Palette, Type } from 'lucide-react';
import Logo from '@/components/logo';

export default function BrandBook() {
  const brandSections = [
    {
      icon: <Palette className='w-6 h-6' />,
      title: 'Color System',
      description: 'Our brand colors and their usage guidelines',
      href: '#colors'
    },
    {
      icon: <Type className='w-6 h-6' />,
      title: 'Typography',
      description: 'Font families, sizes, and hierarchies',
      href: '#typography'
    },
    {
      icon: <Component className='w-6 h-6' />,
      title: 'Components',
      description: 'UI components and design patterns',
      href: '#components'
    },
    {
      icon: <ImageIcon className='w-6 h-6' />,
      title: 'Imagery',
      description: 'Photography and illustration guidelines',
      href: '#imagery'
    },
    {
      icon: <MessageSquare className='w-6 h-6' />,
      title: 'Voice & Tone',
      description: 'Communication style and messaging',
      href: '#voice'
    },
    {
      icon: <Box className='w-6 h-6' />,
      title: 'Logo Usage',
      description: 'Logo variations and spacing rules',
      href: '#logo'
    }
  ];

  return (
    <div className='min-h-screen bg-background'>
      {/* Hero Section */}
      <section className='relative py-24 overflow-hidden'>
        <div className='absolute inset-0'>
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_top_right,_var(--primary-foreground)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,_var(--secondary)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-grid-primary/[0.03] bg-[size:20px_20px]' />
        </div>

        <div className='container mx-auto px-4 relative'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='text-center max-w-3xl mx-auto'
          >
            <div className='inline-flex items-center gap-2 bg-primary/5 border border-primary/10 rounded-full px-4 py-1.5 mb-8'>
              <span className='flex h-2 w-2 rounded-full bg-primary animate-pulse' />
              <span className='text-sm font-medium'>Brand Guidelines v1.0</span>
            </div>
            <h1 className='text-5xl md:text-6xl font-bold mb-6'>Brand Guidelines</h1>
            <p className='text-lg text-muted-foreground'>
              Our comprehensive brand guidelines define the visual and verbal elements that shape our identity. Use
              these resources to maintain consistency across all touchpoints.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Brand Sections Grid */}
      <section className='py-16 bg-gray-50 dark:bg-gray-900/50'>
        <div className='container mx-auto px-4'>
          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {brandSections.map((section, index) => (
              <motion.a
                key={section.title}
                href={section.href}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className='group relative bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200'
              >
                <div className='flex items-center gap-4 mb-4'>
                  <div className='p-2 rounded-lg bg-primary/10 text-primary'>{section.icon}</div>
                  <h3 className='text-xl font-semibold'>{section.title}</h3>
                </div>
                <p className='text-muted-foreground'>{section.description}</p>
                <div className='absolute bottom-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity'>
                  <div className='p-2 rounded-full bg-primary/10'>
                    <Grid className='w-4 h-4 text-primary' />
                  </div>
                </div>
              </motion.a>
            ))}
          </div>
        </div>
      </section>

      {/* Color System Section */}
      <section id='colors' className='py-16'>
        <div className='container mx-auto px-4'>
          <h2 className='text-3xl font-bold mb-8'>Color System</h2>

          {/* Primary Colors */}
          <div className='mb-12'>
            <h3 className='text-xl font-semibold mb-4'>Primary Colors</h3>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='space-y-2'>
                <div className='h-24 bg-primary rounded-lg'></div>
                <p className='font-medium'>Primary</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(30.37% 0.1233 259.91)</p>
                <p className='text-xs font-mono text-muted-foreground'>#00296B</p>
              </div>
              <div className='space-y-2'>
                <div className='h-24 bg-primary-foreground rounded-lg'></div>
                <p className='font-medium'>Primary Foreground</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(90.49% 0.118 89.16)</p>
                <p className='text-xs font-mono text-muted-foreground'>#FFDC80</p>
              </div>
            </div>
          </div>

          {/* Secondary Colors */}
          <div className='mb-12'>
            <h3 className='text-xl font-semibold mb-4'>Secondary Colors</h3>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='space-y-2'>
                <div className='h-24 bg-secondary rounded-lg'></div>
                <p className='font-medium'>Secondary</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(90.49% 0.118 89.16)</p>
                <p className='text-xs font-mono text-muted-foreground'>#FFDC80</p>
              </div>
              <div className='space-y-2'>
                <div className='h-24 bg-secondary-foreground rounded-lg'></div>
                <p className='font-medium'>Secondary Foreground</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(30.37% 0.1233 259.91)</p>
                <p className='text-xs font-mono text-muted-foreground'>#00296B</p>
              </div>
            </div>
          </div>

          {/* UI Colors */}
          <div className='mb-12'>
            <h3 className='text-xl font-semibold mb-4'>UI Colors</h3>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='space-y-2'>
                <div className='h-24 bg-muted rounded-lg'></div>
                <p className='font-medium'>Muted</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(0.967 0.001 286.375)</p>
                <p className='text-xs font-mono text-muted-foreground'>#F5F5F6</p>
              </div>
              <div className='space-y-2'>
                <div className='h-24 bg-accent rounded-lg'></div>
                <p className='font-medium'>Accent</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(0.967 0.001 286.375)</p>
                <p className='text-xs font-mono text-muted-foreground'>#F5F5F6</p>
              </div>
              <div className='space-y-2'>
                <div className='h-24 bg-destructive rounded-lg'></div>
                <p className='font-medium'>Destructive</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(0.577 0.245 27.325)</p>
                <p className='text-xs font-mono text-muted-foreground'>#FF4444</p>
              </div>
              <div className='space-y-2'>
                <div className='h-24 bg-border rounded-lg'></div>
                <p className='font-medium'>Border</p>
                <p className='text-xs font-mono text-muted-foreground'>oklch(0.92 0.004 286.32)</p>
                <p className='text-xs font-mono text-muted-foreground'>#E5E5E5</p>
              </div>
            </div>
          </div>

          {/* Chart Colors */}
          <div>
            <h3 className='text-xl font-semibold mb-4'>Chart Colors</h3>
            <div className='grid grid-cols-2 md:grid-cols-5 gap-4'>
              {[
                { num: 1, oklch: 'oklch(0.646 0.222 41.116)', hex: '#A55C1B' },
                { num: 2, oklch: 'oklch(0.6 0.118 184.704)', hex: '#5497B8' },
                { num: 3, oklch: 'oklch(0.398 0.07 227.392)', hex: '#2E5DA8' },
                { num: 4, oklch: 'oklch(0.828 0.189 84.429)', hex: '#E67A22' },
                { num: 5, oklch: 'oklch(0.769 0.188 70.08)', hex: '#D45C16' }
              ].map(({ num, oklch, hex }) => (
                <div key={num} className='space-y-2'>
                  <div className='h-24 rounded-lg' style={{ backgroundColor: `var(--chart-${num})` }}></div>
                  <p className='font-medium'>
                    Chart
                    {num}
                  </p>
                  <p className='text-xs font-mono text-muted-foreground'>{oklch}</p>
                  <p className='text-xs font-mono text-muted-foreground'>{hex}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Dark Mode Notice */}
          <div className='mt-12 p-4 bg-muted rounded-lg'>
            <p className='text-sm text-muted-foreground'>
              Note: Colors automatically adjust in dark mode. Switch themes to see the dark mode variants.
            </p>
          </div>
        </div>
      </section>

      {/* Typography Section */}
      <section id='typography' className='py-16 bg-muted/30'>
        <div className='container mx-auto px-4'>
          <h2 className='text-3xl font-bold mb-8'>Typography</h2>

          {/* Primary Font */}
          <div className='mb-12'>
            <h3 className='text-xl font-semibold mb-4'>Primary Font - Figtree</h3>
            <div className='space-y-8'>
              <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm'>
                <div className='mb-4'>
                  <span className='text-sm text-muted-foreground'>Font Weights</span>
                </div>
                <div className='space-y-4'>
                  <div>
                    <p className='font-light text-2xl'>Light (300) - Figtree Light</p>
                    <p className='font-light italic text-2xl'>Light Italic (300) - Figtree Light Italic</p>
                  </div>
                  <div>
                    <p className='font-normal text-2xl'>Regular (400) - Figtree Regular</p>
                    <p className='font-normal italic text-2xl'>Regular Italic (400) - Figtree Regular Italic</p>
                  </div>
                  <div>
                    <p className='font-medium text-2xl'>Medium (500) - Figtree Medium</p>
                    <p className='font-medium italic text-2xl'>Medium Italic (500) - Figtree Medium Italic</p>
                  </div>
                  <div>
                    <p className='font-semibold text-2xl'>SemiBold (600) - Figtree SemiBold</p>
                    <p className='font-semibold italic text-2xl'>SemiBold Italic (600) - Figtree SemiBold Italic</p>
                  </div>
                  <div>
                    <p className='font-bold text-2xl'>Bold (700) - Figtree Bold</p>
                    <p className='font-bold italic text-2xl'>Bold Italic (700) - Figtree Bold Italic</p>
                  </div>
                </div>
              </div>

              {/* Type Scale */}
              <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm'>
                <div className='mb-4'>
                  <span className='text-sm text-muted-foreground'>Type Scale</span>
                </div>
                <div className='space-y-6'>
                  <h1 className='text-5xl md:text-6xl font-bold'>Heading 1 (3.5rem/4rem)</h1>
                  <h2 className='text-4xl md:text-5xl font-bold'>Heading 2 (3rem/3.5rem)</h2>
                  <h3 className='text-3xl md:text-4xl font-bold'>Heading 3 (2.5rem/3rem)</h3>
                  <h4 className='text-2xl md:text-3xl font-bold'>Heading 4 (2rem/2.5rem)</h4>
                  <p className='text-xl'>Large Body (1.25rem)</p>
                  <p className='text-base'>Body (1rem)</p>
                  <p className='text-sm'>Small (0.875rem)</p>
                  <p className='text-xs'>Extra Small (0.75rem)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Logo Usage Section */}
      <section id='logo' className='py-16'>
        <div className='container mx-auto px-4'>
          <h2 className='text-3xl font-bold mb-8'>Logo Usage</h2>

          {/* Logo Variations */}
          <div className='mb-12'>
            <h3 className='text-xl font-semibold mb-4'>Logo Variations</h3>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
              {/* Primary Logo */}
              <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm'>
                <div className='mb-4'>
                  <span className='text-sm text-muted-foreground'>Primary Logo</span>
                </div>
                <div className='flex items-center justify-center h-32 bg-background rounded'>
                  <Logo className='fill-white' showText={true} />
                </div>
              </div>

              {/* Icon Only */}
              <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm'>
                <div className='mb-4'>
                  <span className='text-sm text-muted-foreground'>Icon Only</span>
                </div>
                <div className='flex items-center justify-center h-32 bg-background rounded'>
                  <Logo className='fill-white' showText={false} />
                </div>
              </div>

              {/* Inverted */}
              <div className='bg-primary p-6 rounded-lg shadow-sm'>
                <div className='mb-4'>
                  <span className='text-sm text-primary-foreground'>Inverted Logo</span>
                </div>
                <div className='flex items-center justify-center h-32 rounded'>
                  <Logo className='fill-white' showText={true} inverted={true} />
                </div>
              </div>
            </div>
          </div>

          {/* Logo Guidelines */}
          <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm'>
            <h3 className='text-xl font-semibold mb-4'>Usage Guidelines</h3>
            <div className='space-y-4'>
              <div className='flex items-start gap-4'>
                <div className='flex-shrink-0 p-2 rounded-lg bg-primary/10'>
                  <Box className='w-6 h-6 text-primary' />
                </div>
                <div>
                  <h4 className='font-medium mb-1'>Clear Space</h4>
                  <p className='text-muted-foreground'>
                    Maintain minimum clear space equal to the height of the icon around all sides of the logo.
                  </p>
                </div>
              </div>
              <div className='flex items-start gap-4'>
                <div className='flex-shrink-0 p-2 rounded-lg bg-primary/10'>
                  <ImageIcon className='w-6 h-6 text-primary' />
                </div>
                <div>
                  <h4 className='font-medium mb-1'>Minimum Size</h4>
                  <p className='text-muted-foreground'>
                    Do not use the full logo smaller than 100px wide. For smaller applications, use the icon only
                    version.
                  </p>
                </div>
              </div>
              <div className='flex items-start gap-4'>
                <div className='flex-shrink-0 p-2 rounded-lg bg-destructive/10'>
                  <MessageSquare className='w-6 h-6 text-destructive' />
                </div>
                <div>
                  <h4 className='font-medium mb-1'>Don'ts</h4>
                  <ul className='text-muted-foreground list-disc list-inside'>
                    <li>Don't alter the logo colors except for the approved white version</li>
                    <li>Don't stretch or distort the logo</li>
                    <li>Don't add effects or shadows</li>
                    <li>Don't rotate the logo</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional sections for Colors, Typography, etc. would go here */}
      {/* Each section would follow similar styling patterns seen in other marketing pages */}
    </div>
  );
}
