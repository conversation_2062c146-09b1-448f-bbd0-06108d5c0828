import type { DateRange } from 'react-day-picker';
import { CalendarIcon, Search, X } from 'lucide-react';
import * as React from 'react';
import DropDownComponent from '@/components/dropDown-field';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const statusItems = [
  { id: 'pending', name: 'Pending' },
  { id: 'paid', name: 'Paid' },
  { id: 'failed', name: 'Failed' },
  { id: 'cancelled', name: 'Cancelled' }
];

type IComponentProps = {
  selectedStatus: string[];
  setSelectedStatus: React.Dispatch<React.SetStateAction<string[]>>;
  setFilterValue: React.Dispatch<React.SetStateAction<string>>;
  selectedDateRange: DateRange | undefined;
  setSelectedDateRange: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  filterValue: string;
};

const InvoicesFiltersComponent = ({
  filterValue,
  setFilterValue,
  selectedDateRange,
  setSelectedDateRange,
  selectedStatus,
  setSelectedStatus
}: IComponentProps) => {
  return (
    <div className='w-full flex items-center'>
      <div className='w-1/5'>
        <div className='relative group'>
          <div className='relative flex items-center bg-background/80 backdrop-blur-none border border-primary/10 rounded-md pl-4 pr-2 h-10'>
            <Search className='h-4 w-4 text-muted-foreground/60' />
            <Input
              placeholder='Search Invoices...'
              className='border-0 bg-transparent px-3 h-9 placeholder:text-muted-foreground'
              style={{
                WebkitAppearance: 'none',
                MozAppearance: 'none',
                appearance: 'none',
                outline: 'none',
                boxShadow: 'none'
              }}
              value={filterValue}
              onChange={e => setFilterValue(e.target.value)}
            />
            {filterValue?.trim() ? (
              <button
                type='button'
                onClick={() => setFilterValue('')}
                className='absolute hover:scale-105 hover:text-gray-600 cursor-pointer right-3 top-1/2 -translate-y-1/2 text-muted-foreground'
              >
                <X className='h-4 w-4' />
              </button>
            ) : null}
          </div>
        </div>
      </div>

      <div className='w-4/5 flex justify-end items-center gap-3'>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              className='w-[220px] flex justify-between pl-3 text-left font-normal text-muted-foreground text-sm'
            >
              {selectedDateRange?.from && selectedDateRange?.to ? (
                `${selectedDateRange.from.toLocaleDateString()} - ${selectedDateRange.to.toLocaleDateString()}`
              ) : (
                <>
                  <span>Select Date Range</span>
                  <CalendarIcon className='h-4 w-4' />
                </>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-auto p-0' align='end'>
            <Calendar
              initialFocus
              mode='range'
              defaultMonth={selectedDateRange?.from}
              selected={selectedDateRange}
              onSelect={setSelectedDateRange}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>

        <DropDownComponent
          items={statusItems.map(item => ({ id: item.id, label: item.name }))}
          selectedItems={selectedStatus}
          setSelectedItems={setSelectedStatus}
          title='Status'
        />
      </div>
    </div>
  );
};

export default InvoicesFiltersComponent;
