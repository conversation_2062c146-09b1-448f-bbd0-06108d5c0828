import type { IDateFormat } from '@/types/date-format';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DateFormatEnum } from '@/types/date-format';

type SettingsState = {
  compactMode: boolean;
  wideMode: boolean;
  reducedMotion: boolean;
  fontSize: 'small' | 'normal' | 'large';
  dateFormat: IDateFormat;
  useLocalTime: boolean;
  setCompactMode: (value: boolean) => void;
  setWideMode: (value: boolean) => void;
  setReducedMotion: (value: boolean) => void;
  setFontSize: (size: 'small' | 'normal' | 'large') => void;
  setDateFormat: (format: IDateFormat) => void;
  setUseLocalTime: (value: boolean) => void;
};

export const useSettings = create<SettingsState>()(
  persist(
    set => ({
      compactMode: false,
      reducedMotion: false,
      wideMode: false,
      fontSize: 'normal',
      dateFormat: { format: DateFormatEnum.RFC3339 },
      useLocalTime: true,
      setCompactMode: value => set({ compactMode: value }),
      setWideMode: value => set({ wideMode: value }),
      setReducedMotion: value => set({ reducedMotion: value }),
      setFontSize: size => set({ fontSize: size }),
      setDateFormat: format => set({ dateFormat: format }),
      setUseLocalTime: value => set({ useLocalTime: value })
    }),
    {
      name: 'user-settings'
    }
  )
);
