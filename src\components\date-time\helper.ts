import { DateFormatEnum } from '@/types/date-format';

export const formatDate = (date: Date, dateFormat: DateFormatEnum, useUTC: boolean = false): string => {
  if (Number.isNaN(date.getTime())) {
    return 'Invalid date';
  }

  const get = {
    year: (): number => (useUTC ? date.getUTCFullYear() : date.getFullYear()),
    month: (): number => (useUTC ? date.getUTCMonth() : date.getMonth()) + 1,
    day: (): number => (useUTC ? date.getUTCDate() : date.getDate()),
    hours: (): number => (useUTC ? date.getUTCHours() : date.getHours()),
    minutes: (): number => (useUTC ? date.getUTCMinutes() : date.getMinutes()),
    seconds: (): number => (useUTC ? date.getUTCSeconds() : date.getSeconds()),
    weekday: (): number => (useUTC ? date.getUTCDay() : date.getDay())
  };

  const pad = (num: number): string => {
    if (typeof num !== 'number' || Number.isNaN(num)) {
      return '00';
    }
    return num.toString().padStart(2, '0');
  };

  try {
    switch (dateFormat) {
      case DateFormatEnum.RFC3339: {
        const year = get.year();
        const month = pad(get.month());
        const day = pad(get.day());
        const hours = pad(get.hours());
        const minutes = pad(get.minutes());
        const seconds = pad(get.seconds());

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      case DateFormatEnum.RFC2822: {
        if (useUTC) {
          const utcString = date.toUTCString();
          if (typeof utcString === 'string') {
            return utcString;
          }
          throw new Error('UTC string conversion failed');
        }

        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as const;
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] as const;

        const weekday = get.weekday();
        const day = pad(get.day());
        const monthIndex = get.month() - 1;
        const year = get.year();
        const hours = pad(get.hours());
        const minutes = pad(get.minutes());

        if (weekday < 0 || weekday >= days.length || monthIndex < 0 || monthIndex >= months.length) {
          throw new Error('Invalid date component');
        }

        return `${days[weekday]}, ${day} ${months[monthIndex]} ${year} ${hours}:${minutes}`;
      }

      case DateFormatEnum.US: {
        const options: Intl.DateTimeFormatOptions = {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          timeZone: useUTC ? 'UTC' : undefined
        };

        const formatted = new Intl.DateTimeFormat('en-US', options).format(date);
        if (typeof formatted !== 'string') {
          throw new TypeError('DateTimeFormat returned non-string value');
        }
        return formatted;
      }

      default:
        throw new Error(`Invalid format specified: ${dateFormat}`);
    }
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid date format';
  }
};
export const getTimeAgo = (currentTime: Date, serverTime: Date): string => {
  if (Number.isNaN(currentTime.getTime()) || Number.isNaN(serverTime.getTime())) {
    return 'Invalid date';
  }
  try {
    const diffSec = Math.floor((currentTime.getTime() - serverTime.getTime()) / 1000);

    if (diffSec < 60) {
      return `${diffSec} second${diffSec !== 1 ? 's' : ''} ago`;
    }

    const mins = Math.floor(diffSec / 60);
    if (mins < 60) {
      return `${mins} minute${mins !== 1 ? 's' : ''} ago`;
    }

    const hrs = Math.floor(mins / 60);
    if (hrs < 24) {
      return `${hrs} hour${hrs !== 1 ? 's' : ''} ago`;
    }
  } catch (error) {
    console.error(error);
    return 'An error occurred';
  }
  return String(currentTime);
};
