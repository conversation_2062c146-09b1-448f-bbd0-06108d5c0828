'use client';
import { Copy } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

type CopyableIPProps = {
  ip: string | undefined | null;
  className?: string;
  showIcon?: boolean;
  variant?: 'default' | 'ghost' | 'outline' | 'secondary' | 'destructive' | 'link';
};

export const CopyableIP: React.FC<CopyableIPProps> = ({ ip, className = '', showIcon = true, variant = 'ghost' }) => {
  const [, setCopied] = useState(false);

  const displayValue = ip || 'Not assigned';

  const handleCopy = () => {
    if (!ip) {
      toast.error('No IP address to copy');
      return;
    }

    navigator.clipboard.writeText(ip);
    setCopied(true);
    toast.success('IP address copied to clipboard');

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <span
        className='text-sm cursor-pointer'
        onClick={handleCopy}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            handleCopy();
          }
        }}
        tabIndex={0}
        role='button'
        title={ip ? 'Click to copy IP address' : 'No IP address available'}
      >
        {displayValue}
      </span>
      {ip && showIcon && (
        <Button variant={variant} size='sm' className='h-7 w-7 p-0' onClick={handleCopy}>
          <Copy className='h-3 w-3 text-gray-400 dark:text-gray-200' />
          <span className='sr-only'>Copy IP address</span>
        </Button>
      )}
    </div>
  );
};
