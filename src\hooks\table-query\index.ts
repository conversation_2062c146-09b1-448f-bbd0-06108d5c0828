import type { ITableQuery } from '@/types/table-query';
import { useEffect, useState } from 'react';

const useTableSearchQueries = ({
  page,
  per_page,
  search,
  date_from,
  date_to,
  sort_by,
  sort_direction,
  status,
  region,
  version,
  project_id,
  type
}: ITableQuery) => {
  const [queries, setQueries] = useState<ITableQuery>({ page, per_page });

  useEffect(() => {
    setQueries(() => {
      const newQueries: ITableQuery = { page, per_page };

      if (search && search.trim()) {
        newQueries.search = search;
      }
      if (date_from) {
        newQueries.date_from = date_from;
      }
      if (sort_by) {
        newQueries.sort_by = sort_by;
      }
      if (sort_direction) {
        newQueries.sort_direction = sort_direction;
      }
      if (date_to) {
        newQueries.date_to = date_to;
      }

      if (sort_by?.trim()) {
        newQueries.sort_by = sort_by;
      }
      if (status) {
        newQueries.status = status;
      }
      if (region) {
        newQueries.region = region;
      }
      if (version) {
        newQueries.version = version;
      }
      if (project_id) {
        newQueries.project_id = project_id;
      }
      if (type) {
        newQueries.type = type;
      }
      return newQueries;
    });
  }, [
    page,
    per_page,
    search,
    sort_by,
    sort_direction,
    date_from,
    date_to,
    status?.length,
    project_id,
    region,
    type,
    status,
    version
  ]);

  return queries;
};

export default useTableSearchQueries;
