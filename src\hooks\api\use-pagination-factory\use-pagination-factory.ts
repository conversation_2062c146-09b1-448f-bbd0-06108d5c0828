import type { AxiosRequestConfig } from 'axios';
import type { QueryRequestType } from 'types/request';
import type { IUsePaginationFactoryProps, IUsePaginationFactoryResult } from './use-pagination-factory.type';
import { useQuery } from '@tanstack/react-query';
import { SERVER } from 'libs/Axios';
import { useState } from 'react';
import { replaceParamsWithValue } from 'utils/replace-params-with-value';

/**
 * Custom React Query hook for paginated data fetching with additional features.
 *
 * @template Response - The type of the raw response data.
 * @template SelectResponse - The type of the selected response data.
 *
 * @typedef {object} QueryKeyType - Type definition for the query key used in React Query.
 * @type {ReadonlyArray<string | number>}
 *
 * @typedef {object} IUsePaginationFnData - Type definition for the data returned by the fetch function.
 * @property {Response} rawData - The raw response data.
 * @property {SelectResponse} transformedData - The transformed response data.
 *
 * @typedef {object} IUsePaginationFactoryProps - Props for the usePaginationFactory hook.
 * @property {string} url - The base URL for the API request.
 * @property {QueryKeyType} queryKey - The query key used in React Query.
 * @property {Record<string, unknown>} [query] - Additional query parameters.
 * @property {Record<string, unknown>} [search] - Search parameters for filtering.
 * @property {Record<string, unknown>} [params] - URL parameters.
 * @property {number} version - API version.
 * @property {number} page - Current page number.
 * @property {number} [perPage=10] - Number of items per page.
 * @property {boolean} [showError=true] - Flag to show or hide errors.
 * @property {number} [staleTime=180000] - Stale time for caching.
 * @property {number} [gcTime=600000] - Garbage collection time.
 *
 * @typedef {object} IUsePaginationProps - Props for the usePaginationFactory result.
 * @property {IUsePaginationFnData<Response>} data - The selected response data.
 * @property {AxiosError<ResponseErrorType>} error - The error object, if any.
 * @property {boolean} isLoading - Flag indicating whether the data is loading.
 * @property {boolean} isSuccess - Flag indicating whether the data fetching was successful.
 * @property {boolean} isError - Flag indicating whether an error occurred during data fetching.
 *
 * @typedef {object} IUsePaginationFactoryResult - Result of the usePaginationFactory hook.
 * @property {function(variables: QueryRequestType): void} fetch - Function to trigger a refetch with new parameters.
 * @property {object} queryParams - Merged query parameters.
 * @property {object} params - Merged request parameters.
 * @property {IUsePaginationProps<SelectResponse>} ... - Other props from usePaginationFactory result.
 *
 * @param {IUsePaginationFactoryProps<Response, SelectResponse>} props - Hook configuration.
 * @throws {Error} If there is an issue with the API request.
 * @returns {IUsePaginationFactoryResult<SelectResponse>} - Result object containing paginated data and additional features.
 * @example
 * // Example usage of usePaginationFactory hook
 * const paginationResult = usePaginationFactory({
 *    url: '/api/data/{lang}',
 *    queryKey: ['data'],
 *    page: 1,
 *    perPage: 10,
 *    version: 1,
 *    search: { keyword: 'example' },
 *    query: { category: 'example' },
 *    params: { lang: 'en' },
 *    showError: true,
 *    staleTime: 300000,
 *    gcTime: 600000,
 *    select: (data) => ({ data: { modifiedData: data.dataSubset }, success: true }),
 * });
 *
 * TODO: fix select for when map another type.
 */
export const usePaginationFactory = <
  Response,
  SelectResponse = Response,
  AdditionalResponse = Record<string, unknown>
>({
  url,
  method = 'GET',
  queryKey,
  query = {},
  search = {},
  params = {},
  version,
  page,
  perPage = 10,
  showError = true,
  staleTime = 180000,
  gcTime = 600000,
  ...anotherConfigs
}: IUsePaginationFactoryProps<Response, SelectResponse, AdditionalResponse>): IUsePaginationFactoryResult<
  SelectResponse,
  AdditionalResponse
> => {
  const [dynamicParams, setDynamicParams] = useState<QueryRequestType | undefined>(undefined);

  // Ensure we have valid objects for our parameters
  const allSearchParams = { ...search, ...(dynamicParams?.search || {}) };
  const allQueryParams = {
    ...query,
    ...(dynamicParams?.queryParams || {}),
    page: page || 1,
    per_page: perPage
  };
  const allParams = { ...params, ...(dynamicParams?.params || {}) };

  const requestConfig: AxiosRequestConfig = {
    method,
    params: allQueryParams,
    headers: {
      silent: !showError
    }
  };

  const finalUrl = replaceParamsWithValue(url, allParams);

  const paginateQuery = useQuery({
    queryKey,
    queryFn: async () => {
      const response = await SERVER.request({
        url: finalUrl,
        ...requestConfig
      });
      return response;
    },
    refetchOnWindowFocus: false,
    refetchInterval: false,
    refetchOnReconnect: true,
    refetchIntervalInBackground: true,
    staleTime,
    gcTime,
    ...anotherConfigs
  });

  const fetch = (variables: QueryRequestType) => {
    setDynamicParams(variables);
  };

  return {
    ...paginateQuery,
    fetch,
    queryParams: allQueryParams,
    params: allParams,
    search: allSearchParams
  };
};
