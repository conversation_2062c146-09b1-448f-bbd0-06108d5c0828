import { getTranslations, setRequestLocale } from 'next-intl/server';
import ServerDetailComponent from '@/components/panel/windows-server/detail';

type IWidnowsServerDetailPageProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IWidnowsServerDetailPageProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'WindowsServerDetail'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default async function UserProfilePage(props: IWidnowsServerDetailPageProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div>
      <ServerDetailComponent />
    </div>
  );
}
