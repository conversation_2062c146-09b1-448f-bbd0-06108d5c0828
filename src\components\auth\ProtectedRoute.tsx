'use client';

import { useIsPublicRoute } from 'hooks/route/is-public/use-is-public';
import { useUser } from 'hooks/user/user';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { PAGE_ROUTES } from '@/configs/page-routes';

export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { isLogin } = useUser();
  const isPublic = useIsPublicRoute();

  useEffect(() => {
    // Only redirect if we're sure about the auth state
    if (isLogin === undefined) {
      return;
    }

    // If user is logged in and tries to access auth pages, redirect to panel
    if (isLogin && pathname.startsWith('/auth')) {
      router.replace(PAGE_ROUTES.PANEL);
      return;
    }

    // If user is not logged in and tries to access protected pages
    if (isLogin === false && !isPublic && !pathname.startsWith('/auth')) {
      router.replace(PAGE_ROUTES.LOGIN);
    }
  }, [isLogin, isPublic, router, pathname]);

  // Show loading or nothing while checking auth status
  if (isLogin === undefined) {
    return null; // or return a loading spinner
  }

  // If not logged in and trying to access protected route, show nothing
  if (!isLogin && !isPublic && !pathname.startsWith('/auth')) {
    return null;
  }

  return <>{children}</>;
}
