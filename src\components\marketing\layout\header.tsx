'use client';
import { Bot, Box, Boxes, BrainCircuit, ChartCandlestick, Monitor, Moon, PhoneIcon, Sun, Webhook } from 'lucide-react';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import * as React from 'react';
import { useEffect, useState } from 'react';
import Logo from '@/components/logo';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle
} from '@/components/ui/navigation-menu';
import { PAGE_ROUTES } from '@/configs/page-routes';

const marketplace = [
  {
    title: 'Trading Bots',
    href: '/trading-bots',
    description: 'Automated strategies for traders.',
    icon: Bot
  },
  {
    title: 'Indicators',
    href: '/indicators',
    description: 'Technical tools for better trading decisions.',
    icon: ChartCandlestick
  },
  {
    title: 'Services',
    href: '/services',
    description: 'Professional trading-related services.',
    icon: Box
  },
  {
    title: 'AI Models',
    href: '/ai-models',
    description: 'Cutting-edge AI for market analysis.',
    icon: BrainCircuit
  },
  {
    title: 'APIs',
    href: '/apis',
    description: 'Access powerful trading data and features.',
    icon: Webhook
  },
  {
    title: 'Solutions',
    href: '/solutions',
    description: 'Custom solutions for trading businesses.',
    icon: Boxes
  }
];

// Remove the about array since we won't need it anymore

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const { setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Wait until mounted to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`
      fixed top-0 left-0 right-0 z-50
      transition-all duration-300
      ${
    isScrolled
      ? 'backdrop-blur-md bg-background/50 dark:bg-background/50 border-b border-border/10 shadow-sm dark:shadow-sm dark:shadow-gray-900/50'
      : 'bg-background dark:bg-background border-b border-border/20 shadow-xs dark:shadow-xs dark:shadow-gray-900/30'
    }
    `}
    >
      <div className='container max-w-7xl mx-auto !px-0'>
        <div className='flex items-center justify-between w-full gap-4 h-[var(--header-height)]'>
          {/* Logo - Left */}
          <div className='flex-shrink-0'>
            <Link href='/'>
              <Logo className='h-8 w-auto fill-white' />
            </Link>
          </div>

          {/* Navigation - Center */}
          <div className='flex-1 flex justify-center'>
            <NavigationMenu>
              <NavigationMenuList className='flex items-center'>
                <NavigationMenuItem>
                  <Link href='/' legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>Home</NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>Products</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className='grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]'>
                      <li className='row-span-3'>
                        <NavigationMenuLink asChild>
                          <Link
                            className='flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md dark:from-muted/20 dark:to-muted/30'
                            href={PAGE_ROUTES.PRODUCTS_TRADING_TERMINAL}
                          >
                            <Logo className='h-6 w-6' showText={false} />
                            <div className='mb-2 mt-4 text-lg font-medium'>Trading Terminal</div>
                            <p className='text-sm leading-tight text-muted-foreground'>
                              Beautifully designed components built with Radix UI and Tailwind CSS.
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                      <li>
                        <Link href='/products/trading-vps' legacyBehavior passHref>
                          <NavigationMenuLink className='block p-2 rounded-md hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent dark:hover:text-accent-foreground'>
                            <span className='font-medium'>Trading VPS</span>
                            <p className='text-sm text-muted-foreground dark:text-muted-foreground'>
                              The lightweight trading machine
                            </p>
                          </NavigationMenuLink>
                        </Link>
                      </li>
                      <li>
                        <Link href='/products/trading-desktop' legacyBehavior passHref>
                          <NavigationMenuLink className='block p-2 rounded-md hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent dark:hover:text-accent-foreground'>
                            <span className='font-medium'>Trading Desktop</span>
                            <p className='text-sm text-muted-foreground dark:text-muted-foreground'>
                              The lightweight trading machine
                            </p>
                          </NavigationMenuLink>
                        </Link>
                      </li>
                      <li>
                        <Link href='/products/windows-server' legacyBehavior passHref>
                          <NavigationMenuLink className='block p-2 rounded-md hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent dark:hover:text-accent-foreground'>
                            <span className='font-medium'>Windows Server</span>
                            <p className='text-sm text-muted-foreground dark:text-muted-foreground'>
                              The lightweight trading machine
                            </p>
                          </NavigationMenuLink>
                        </Link>
                      </li>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Marketplace</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className='grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]'>
                      {marketplace.map(item => (
                        <li key={item.title}>
                          <Link href={item.href} legacyBehavior passHref>
                            <NavigationMenuLink className='block p-2 rounded-md hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent dark:hover:text-accent-foreground'>
                              <div className='flex items-center gap-2 mb-1'>
                                <item.icon className='h-4 w-4' />
                                <span className='font-medium'>{item.title}</span>
                              </div>
                              <p className='text-sm text-muted-foreground dark:text-muted-foreground pl-6'>
                                {item.description}
                              </p>
                            </NavigationMenuLink>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href='/pricing' legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>Pricing</NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href='https://docs.tradevps.net' target='_blank' legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>Docs</NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href='https://blog.tradevps.net' target='_blank' legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>Blog</NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href='/about' legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>About</NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right side - Theme Toggle, Contact & Get Started */}
          <div className='flex items-center gap-4'>
            {mounted && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='h-9 w-9 text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100 hover:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 focus:ring-0'
                  >
                    <Sun className='h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0' />
                    <Moon className='absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100' />
                    <span className='sr-only'>Toggle theme</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='text-gray-900 dark:text-gray-300'>
                  <DropdownMenuItem
                    onClick={() => setTheme('light')}
                    className='hover:text-gray-900 dark:hover:text-gray-100'
                  >
                    <Sun className='h-4 w-4 mr-2 text-gray-700 dark:text-gray-400' />
                    Light
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setTheme('dark')}
                    className='hover:text-gray-900 dark:hover:text-gray-100'
                  >
                    <Moon className='h-4 w-4 mr-2 text-gray-700 dark:text-gray-400' />
                    Dark
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setTheme('system')}
                    className='hover:text-gray-900 dark:hover:text-gray-100'
                  >
                    <Monitor className='h-4 w-4 mr-2 text-gray-700 dark:text-gray-400' />
                    System
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <div className='hidden md:flex items-center gap-2 text-sm text-gray-900 dark:text-gray-300'>
              <PhoneIcon className='h-4 w-4 text-gray-700 dark:text-gray-300' />
              <span>+44 20 453 84 248</span>
            </div>
            <Link
              href='/auth/sign-up'
              className='inline-flex items-center justify-center rounded-lg px-6 py-2.5 text-sm font-medium bg-primary text-primary-foreground dark:bg-primary dark:text-primary-foreground btn-edge-light'
            >
              <div className='flex items-center gap-2'>
                Get Started
                <svg className='w-4 h-4 btn-arrow' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3'
                  />
                </svg>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
}
