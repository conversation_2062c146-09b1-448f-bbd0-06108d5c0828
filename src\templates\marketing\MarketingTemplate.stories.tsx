import type { Meta, StoryObj } from '@storybook/react';
import { userEvent, within } from '@storybook/test';
import { NextIntlClientProvider } from 'next-intl';
import messages from '@/locales/en.json';
import { MarketingTemplate } from './MarketingTemplate';

const meta = {
  title: 'MarketingTemplate',
  component: MarketingTemplate,
  parameters: {
    layout: 'fullscreen'
  },
  tags: ['autodocs'],
  decorators: [
    Story => (
      <NextIntlClientProvider locale='en' messages={messages}>
        <Story />
      </NextIntlClientProvider>
    )
  ]
} satisfies Meta<typeof MarketingTemplate>;

export default meta;
type Story = StoryObj<typeof meta>;

export const MarketingWithReactComponent = {
  args: {
    children: <div>Children node</div>
  }
} satisfies Story;

export const BaseWithString = {
  args: {
    children: 'String'
  }
} satisfies Story;

// More on interaction testing: https://storybook.js.org/docs/7.0/react/writing-tests/interaction-testing
export const BaseWithHomeLink: Story = {
  args: {
    children: <div>Children node</div>
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const link = canvas.getByText('Link 1');

    await userEvent.click(link);
  }
} satisfies Story;
