import { useEffect, useRef, useState } from 'react';

export const useSearchQuery = (filterValue: string, delay: number = 500) => {
  const [searchValue, setSearchValue] = useState('');
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setSearchValue('');

    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = setTimeout(() => {
      setSearchValue(filterValue);
    }, delay);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [filterValue, delay]);

  return searchValue;
};
