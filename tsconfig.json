/* eslint-disable jsonc/sort-keys */
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "removeComments": true,
    "preserveConstEnums": true,
    "strict": true,
    "alwaysStrict": true,
    "strictNullChecks": true,
    "noUncheckedIndexedAccess": true,

    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowUnreachableCode": false,
    "noFallthroughCasesInSwitch": true,

    "target": "es2017",
    "outDir": "out",
    "sourceMap": true,

    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "allowJs": true,
    "checkJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    "jsx": "preserve",
    "noEmit": true,
    "isolatedModules": true,
    "incremental": true,

    // Load types
    "types": ["vitest/globals"],

    // Path aliases
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/public/*": ["./public/*"],
      "components/*": ["./src/components/*"],
      "containers/*": ["./src/containers/*"],
      "skeletons/*": ["./src/skeletons/*"],
      "libs/*": ["./src/libs/*"],
      "configs/*": ["./src/configs/*"],
      "hooks/*": ["./src/hooks/*"],
      "providers/*": ["./src/providers/*"],
      "services/*": ["./src/services/*"],
      "stores/*": ["./src/stores/*"],
      "types/*": ["./src/types/*"],
      "utils/*": ["./src/utils/*"],
      "validations/*": ["./src/validations/*"]
    },

    // Editor support
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "exclude": ["./out/**/*", "./node_modules/**/*", "**/*.spec.ts", "**/*.e2e.ts"],
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".storybook/*.ts", ".next/types/**/*.ts", "**/*.mts"]
}
