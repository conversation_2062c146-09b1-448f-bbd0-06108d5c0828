import type { APIError, IRegisterParams, IRegisterResponse } from '@tradevpsnet/client';
import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useRegisterMutate = (options?: Pick<IUseMutationFactoryProps<any>, 'refetchQueries' | 'onSuccess'>) => {
  return useMutationFactory({
    url: API_ROUTES.AUTH_REGISTER,
    method: 'POST',
    ...options
  });
};

export const useRegisterClientMutate = (options?: {
  onSuccess?: (data: IRegisterResponse) => void;
  onError?: (error: APIError) => void;
}) => {
  const client = new Client('', process.env.NEXT_PUBLIC_SERVER_URL);

  return useMutation<IRegisterResponse, APIError, IRegisterParams>({
    mutationFn: (params: IRegisterParams) => client.auth.register(params),
    ...options
  });
};
