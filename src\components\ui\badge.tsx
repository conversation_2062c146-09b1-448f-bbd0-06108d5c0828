import type { VariantProps } from 'class-variance-authority';
import { Slot } from '@radix-ui/react-slot';
import { cva } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',
        secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',
        destructive:
          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70',
        outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
        primay_light: 'text-blue-500 text-xs bg-blue-100',
        success_light: 'text-green-500 text-xs bg-green-100',
        warning_light: 'text-yellow-500 text-xs bg-yellow-100',
        danger_light: 'text-red-500 text-xs bg-red-100',
        stone_light: 'text-stone-500 text-xs bg-stone-100',
        fuchsia_ligth: 'text-fuchsi-500 text-xs bg-fuchsia-100',
        sky_light: 'text-sky-500 text-xs bg-sky-100',
        emerald_light: 'text-emerald-500 text-xs bg-emerald-100',
        neutral_light: 'text-neutral-500 text-xs bg-neutral-100',
        rose_light: 'text-rose-500 text-xs bg-rose-100',
        amber_light: 'text-amber-500 text-xs bg-amber-100',
        gray_light: 'text-gray-500 text-xs bg-gray-100',
        slate_light: 'text-slate-500 text-xs bg-slate-100',
        fuchsia_light: 'text-fuchsia-500 text-xs bg-fuchsia-100'
      }
    },
    defaultVariants: {
      variant: 'default'
    }
  }
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : 'span';

  return <Comp data-slot='badge' className={cn(badgeVariants({ variant }), className)} {...props} />;
}

export { Badge, badgeVariants };
