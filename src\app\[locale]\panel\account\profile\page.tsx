'use client';

import { useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Activity,
  BadgeCheck,
  Check,
  FolderGit2,
  Globe,
  Mail,
  Pencil,
  Phone,
  Plus,
  Server,
  Shield,
  User,
  Wallet,
  X
} from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';
import { toast } from 'sonner';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useProjectsList } from '@/services/api/user/projects';
import { useUserInfoFetch } from '@/services/api/user/use-user-info-fetch';
import { useUserUpdateMutate } from '@/services/api/user/use-user-update-mutate';

import { generateInitials, getConsistentColor } from '@/utils/avatar';

const ProfileSkeleton = () => (
  <div className='space-y-4'>
    <Skeleton className='h-8 w-[200px]' />
    <div className='space-y-2'>
      <Skeleton className='h-10 w-full' />
      <Skeleton className='h-10 w-full' />
    </div>
    <Skeleton className='h-10 w-[100px]' />
  </div>
);

const formatDate = (date: string | null) => {
  if (!date) {
    return null;
  }
  return format(new Date(date), 'PPP');
};

type PlanLimit = {
  servers: number;
  api: number;
};

type PlanInfo = {
  name: string;
  className: string;
  price: number;
  limits: PlanLimit;
};

const getPlanStatus = (plan: number): PlanInfo => {
  const plans: Record<number, PlanInfo> = {
    0: {
      name: 'Default Plan',
      className: 'bg-gray-100 text-gray-800',
      price: 0,
      limits: {
        servers: 10,
        api: 5000
      }
    },
    1: {
      name: 'Pro Plan',
      className: 'bg-blue-100 text-blue-800',
      price: 19.85,
      limits: {
        servers: 50,
        api: 10000
      }
    },
    2: {
      name: 'Premium Plan',
      className: 'bg-purple-100 text-purple-800',
      price: 119.58,
      limits: {
        servers: 500,
        api: 250000
      }
    },
    3: {
      name: 'Advance Plan',
      className: 'bg-indigo-100 text-indigo-800',
      price: 220.0,
      limits: {
        servers: -1,
        api: 5000000
      }
    }
  };

  return (
    plans[plan as keyof typeof plans] || {
      name: 'Unknown Plan',
      className: 'bg-gray-100 text-gray-800',
      price: 0,
      limits: {
        servers: 0,
        api: 0
      }
    }
  );
};

const getAccountStatus = (status: number) => {
  const statuses = {
    0: { name: 'Registered', className: 'bg-yellow-100 text-yellow-800' },
    1: { name: 'Verified', className: 'bg-green-100 text-green-800' },
    2: { name: 'Supervision', className: 'bg-blue-100 text-blue-800' },
    3: { name: 'Inactive', className: 'bg-red-100 text-red-800' }
  };
  return statuses[status as keyof typeof statuses] || { name: 'Unknown', className: 'bg-gray-100 text-gray-800' };
};

type InfoFieldProps = {
  label: string;
  value?: string | null | React.ReactNode;
  icon: any;
  verified?: boolean;
  isEditing?: boolean;
  isLoading?: boolean;
  onEdit?: () => void;
  onChange?: (value: string) => void;
  onSave?: () => void;
  onCancel?: () => void;
  editable?: boolean;
};

const InfoField = ({
  label,
  value,
  icon: Icon,
  verified,
  isEditing,
  isLoading,
  onEdit,
  onChange,
  onSave,
  onCancel,
  editable = false
}: InfoFieldProps) => (
  <div className='flex items-center gap-3 p-3 rounded-lg bg-muted/30'>
    <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10'>
      <Icon className='h-4 w-4 text-primary' />
    </div>
    <div className='flex-1 min-w-0'>
      <p className='text-sm text-muted-foreground/80'>{label}</p>
      <div className='flex items-center gap-2 mt-1'>
        {isEditing ? (
          <div className='flex-1 flex items-center gap-2'>
            <Input
              value={typeof value === 'string' ? value : ''}
              onChange={e => onChange?.(e.target.value)}
              className='h-8 bg-background/50'
              disabled={isLoading}
            />
            <button
              type='button'
              onClick={onSave}
              disabled={isLoading}
              className='text-green-500 hover:text-green-600 transition-colors disabled:opacity-50'
            >
              {isLoading ? <CyrcleSvg color='green' /> : <Check className='h-4 w-4' />}
            </button>
            <button
              type='button'
              onClick={onCancel}
              disabled={isLoading}
              className='text-red-500 hover:text-red-600 transition-colors disabled:opacity-50'
            >
              <X className='h-4 w-4' />
            </button>
          </div>
        ) : (
          <>
            <p className='text-sm font-medium truncate'>{value || 'Not set'}</p>
            {verified && <BadgeCheck className='h-4 w-4 text-green-500 flex-shrink-0' />}
            {editable && (
              <button
                type='button'
                onClick={onEdit}
                disabled={isLoading}
                className='text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50'
              >
                <Pencil className='h-3.5 w-3.5' />
              </button>
            )}
          </>
        )}
      </div>
    </div>
  </div>
);

export default function ProfilePage() {
  const { data: userInfo, isLoading, refetch } = useUserInfoFetch();
  const { data: projects, isLoading: isProjectsLoading } = useProjectsList();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(userInfo?.name || '');
  const [projectSearchTerm, setProjectSearchTerm] = useState('');

  const updateProfileMutation = useUserUpdateMutate({
    onSuccess: async () => {
      toast.success('Profile updated successfully');
      setIsEditing(false);
      await refetch();
      queryClient.invalidateQueries({ queryKey: [API_QUERY_KEY.USER_PROFILE] });
    },
    onError: () => {
      toast.error('Failed to update profile');
    }
  });

  const userAvatarFallback = React.useMemo(() => {
    const initials = generateInitials(userInfo?.name || 'Guest User');
    const bgColor = getConsistentColor(userInfo?.name || 'Guest User');
    return { initials, bgColor };
  }, [userInfo?.name]);

  const handleNameUpdate = () => {
    if (newName.trim() && newName !== userInfo?.name) {
      updateProfileMutation.mutate({ body: { name: newName } });
    } else {
      setIsEditing(false);
    }
  };

  const handleDefaultProjectUpdate = (projectId: string) => {
    updateProfileMutation.mutate({
      body: {
        default_project_id: projectId
      }
    });
  };

  const filteredProjects
    = projects?.filter(project => project.name.toLowerCase().includes(projectSearchTerm.toLowerCase())) || [];

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <ProfileSkeleton />
          </CardContent>
        </Card>
      </div>
    );
  }

  const planStatus = getPlanStatus(userInfo?.plan || 0);
  const accountStatus = getAccountStatus(userInfo?.status || 0);

  return (
    <div className='max-w-5xl mx-auto space-y-8'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-semibold tracking-tight bg-gradient-to-r from-foreground/80 to-foreground bg-clip-text text-transparent'>
            Profile
          </h1>
          <p className='text-muted-foreground/80 mt-1'>Manage your account personal information</p>
        </div>
      </div>

      <div className='grid gap-6 grid-cols-1 lg:grid-cols-3'>
        <div className='lg:col-span-1 space-y-4'>
          <Card className='glass-card'>
            <CardContent className='p-6 text-center'>
              <Avatar className='size-20 mx-auto mb-4'>
                <AvatarImage src={userInfo?.avatar} alt={userInfo?.name || 'User avatar'} />
                <AvatarFallback style={{ backgroundColor: userAvatarFallback.bgColor }}>
                  {userAvatarFallback.initials}
                </AvatarFallback>
              </Avatar>
              <h2 className='font-semibold text-lg'>{userInfo?.name || 'Not set'}</h2>
              <p className='text-sm text-muted-foreground/80'>{userInfo?.email}</p>
              <div className='flex justify-center gap-2 mt-3'>
                {userInfo?.email_verified_at ? (
                  <Badge variant='outline' className={accountStatus.className}>
                    {accountStatus.name}
                  </Badge>
                ) : (
                  <Badge variant='outline' className='bg-yellow-100 text-yellow-800'>
                    Unverified
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className='glass-card'>
            <CardContent className='p-6 space-y-4'>
              <div className='flex items-center justify-between'>
                <p className='text-sm text-muted-foreground/80'>Current Plan</p>
                <Badge variant='outline' className={planStatus.className}>
                  {planStatus.name}
                </Badge>
              </div>
              <Separator className='bg-border/50' />

              <div className='grid grid-cols-3 gap-4'>
                <div className='text-center p-3 rounded-lg bg-background/50'>
                  <Wallet className='h-5 w-5 mx-auto mb-2 text-primary' />
                  <p className='text-sm font-medium'>
                    {planStatus.price === 0 ? 'Free' : `$${planStatus.price.toFixed(2)}`}
                  </p>
                  <p className='text-xs text-muted-foreground'>Subscription</p>
                </div>

                <div className='text-center p-3 rounded-lg bg-background/50'>
                  <Server className='h-5 w-5 mx-auto mb-2 text-primary' />
                  <p className='text-sm font-medium'>
                    {planStatus.limits.servers === -1 ? 'Unlimited' : planStatus.limits.servers}
                  </p>
                  <p className='text-xs text-muted-foreground'>Servers</p>
                </div>

                {/* API Calls */}
                <div className='text-center p-3 rounded-lg bg-background/50'>
                  <Activity className='h-5 w-5 mx-auto mb-2 text-primary' />
                  <p className='text-sm font-medium'>
                    {(planStatus.limits.api / 1000).toFixed(0)}
                    k
                  </p>
                  <p className='text-xs text-muted-foreground'>API Calls</p>
                </div>
              </div>

              <Separator className='bg-border/50' />
              <div className='flex items-center justify-between'>
                <p className='text-sm text-muted-foreground/80'>Member Since</p>
                <p className='text-sm font-medium'>{formatDate(userInfo?.created_at)}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='lg:col-span-2 space-y-6'>
          <Card className='glass-card'>
            <CardContent className='p-6 flex items-center justify-between'>
              <div className='flex items-center gap-4'>
                <div className='rounded-lg bg-primary/10 p-3'>
                  <Wallet className='size-6 text-primary' />
                </div>
                <div>
                  <p className='text-sm text-muted-foreground/80'>Available Balance</p>
                  <p className='text-2xl font-semibold text-primary'>
                    $
                    {userInfo?.balance?.toFixed(2) || '0.00'}
                  </p>
                </div>
              </div>
              <Link
                href={PAGE_ROUTES.PANEL_BILLING}
                className={buttonVariants({
                  size: 'sm',
                  className: 'gap-1.5 bg-primary/90 hover:bg-primary'
                })}
              >
                <Plus className='size-4' />
                Top Up
              </Link>
            </CardContent>
          </Card>

          <Card className='glass-card'>
            <CardContent className='p-6 space-y-6'>
              <div className='grid gap-6'>
                <div className='grid gap-4 sm:grid-cols-2'>
                  <InfoField
                    label='Full Name'
                    value={isEditing ? newName : userInfo?.name}
                    icon={User}
                    editable
                    isEditing={isEditing}
                    isLoading={updateProfileMutation.isPending}
                    onEdit={() => {
                      setNewName(userInfo?.name || '');
                      setIsEditing(true);
                    }}
                    onChange={setNewName}
                    onSave={handleNameUpdate}
                    onCancel={() => {
                      setNewName(userInfo?.name || '');
                      setIsEditing(false);
                    }}
                  />
                  <InfoField label='Account ID' value={userInfo?.id} icon={Shield} />
                  <InfoField label='Email' value={userInfo?.email} icon={Mail} verified={userInfo?.email_verified_at} />
                  <InfoField label='Phone' value={userInfo?.phone} icon={Phone} />
                  <InfoField label='Country' value={userInfo?.country} icon={Globe} />
                  <InfoField
                    label='Default Project'
                    icon={FolderGit2}
                    value={(
                      <Select
                        disabled={isProjectsLoading || updateProfileMutation.isPending}
                        value={userInfo?.default_project_id || ''}
                        onValueChange={handleDefaultProjectUpdate}
                      >
                        <SelectTrigger className='w-full bg-background/50'>
                          {updateProfileMutation.isPending ? (
                            <div className='flex items-center gap-2'>
                              <span className='text-muted-foreground'>Updating...</span>
                              <CyrcleSvg color='currentColor' />
                            </div>
                          ) : (
                            <SelectValue placeholder='Select default project' />
                          )}
                        </SelectTrigger>
                        <SelectContent className='w-full min-w-[200px]'>
                          <div className='p-2 border-b'>
                            <Input
                              placeholder='Search projects...'
                              value={projectSearchTerm}
                              onChange={e => setProjectSearchTerm(e.target.value)}
                              className='w-full'
                            />
                          </div>
                          <div className='max-h-[250px] overflow-y-auto'>
                            {filteredProjects.length > 0 ? (
                              filteredProjects.map(project => (
                                <SelectItem key={project.id} value={project.id}>
                                  {project.name}
                                </SelectItem>
                              ))
                            ) : (
                              <div className='py-2 text-center text-sm text-muted-foreground'>No projects found</div>
                            )}
                          </div>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
