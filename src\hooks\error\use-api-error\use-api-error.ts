import type { AxiosError } from 'axios';
import type { ResponseErrorType } from 'types/response';
import type { IUseApiError } from './use-api-error.type';
import { toast } from 'sonner';
import { ResponseStatusCode } from 'types/response-status';

export const useApiError = ({ toastError = true }: IUseApiError = {}) => {
  const handleRequestError = (failureCount: number, error: AxiosError<ResponseErrorType>): boolean => {
    let errorMessage = error?.response?.data?.message;
    if (error.response?.status === ResponseStatusCode?.BadRequest && !errorMessage) {
      errorMessage = 'Validation error';
    }

    if (toastError) {
      toast.error(errorMessage);
    }
    if (
      error.response?.status === ResponseStatusCode.NotFound
      || error.response?.status === ResponseStatusCode.InternalServerError
      || error.response?.status === ResponseStatusCode?.BadRequest
    ) {
      return false;
    }
    return failureCount <= 1;
  };

  return { handleRequestError };
};
