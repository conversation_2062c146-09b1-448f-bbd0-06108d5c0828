import { render, screen, within } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import messages from '@/locales/en.json';
import { AuthTemplate } from './AuthTemplate';

describe('Base template', () => {
  describe('Render method', () => {
    it('should have 3 menu items', () => {
      render(
        <NextIntlClientProvider locale='en' messages={messages}>
          <AuthTemplate>{null}</AuthTemplate>
        </NextIntlClientProvider>
      );

      const menuItemList = screen.getAllByRole('listitem');

      expect(menuItemList).toHaveLength(3);
    });

    it('should have a link to support creativedesignsguru.com', () => {
      render(
        <NextIntlClientProvider locale='en' messages={messages}>
          <AuthTemplate>{null}</AuthTemplate>
        </NextIntlClientProvider>
      );

      const copyrightSection = screen.getByText(/© Copyright/);
      const copyrightLink = within(copyrightSection).getByRole('link');

      expect(copyrightLink).toHaveAttribute('href', 'https://tradevps.net');
    });
  });
});
