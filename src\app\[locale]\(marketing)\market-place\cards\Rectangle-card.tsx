import Image from 'next/image';
import * as React from 'react';
import { Card } from '@/components/ui/card';

type CardPropsType = {
  title: string;
  image: any;

  description: string;
  type: string;
};
const RectangleCard = ({ title, image, description, type }: CardPropsType) => {
  return (
    <Card className='w-full px-3'>
      <div className='flex items-start'>
        <Image className='mt-[-10]' width={50} height={50} alt='' src={image} />
        <div>
          <div className='flex items-start justify-between'>
            <p className='font-bold'>{title}</p>
            <span className='border px-3  rounded-lg w-fit text-gray-500  text-xs py-1'>{type}</span>
          </div>
          <p className='text-sm text-gray-600'>{description}</p>
        </div>
      </div>
    </Card>
  );
};

export default RectangleCard;
