import type { Metadata } from 'next';

import { Figtree } from 'next/font/google';
import { InterfaceLayout } from '@/components/layout/interface-layout';
import { routing } from '@/libs/i18nNavigation';
import { ThemeProvider } from '@/providers/theme-provider';
import '@/styles/global.css';
import '@/styles/marketing.css';

const figtree = Figtree({
  weight: ['300', '500', '600', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  variable: '--font-figtree',
  display: 'swap'
});

export const metadata: Metadata = {
  icons: [
    {
      rel: 'apple-touch-icon',
      url: '/apple-touch-icon.png'
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '32x32',
      url: '/favicon-32x32.png'
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '16x16',
      url: '/favicon-16x16.png'
    },
    {
      rel: 'icon',
      url: '/favicon.ico'
    }
  ]
};

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className={figtree.className}>
        <ThemeProvider>
          <InterfaceLayout>{children}</InterfaceLayout>
        </ThemeProvider>
      </body>
    </html>
  );
}
