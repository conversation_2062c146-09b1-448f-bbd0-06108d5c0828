import { Badge } from '@/components/ui/badge';

export const statusBadge = (status: number) => {
  switch (status) {
    case 0:
      return (
        <Badge variant='primay_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-primary-500 me-1.5'></span>
          Created
        </Badge>
      );
    case 1:
      return (
        <Badge variant='warning_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-yellow-500 me-1.5'></span>
          Warning Payment
        </Badge>
      );
    case 2:
      return (
        <Badge variant='primay_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-blue-500 me-1.5'></span>
          Queue
        </Badge>
      );
    case 3:
      return (
        <Badge variant='amber_light' className='w-[100px] h-[23px] '>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-yellow-400 opacity-75' />
            <span className='relative  h-full w-full  inline-flex size-2 rounded-full bg-yellow-500' />
          </span>
          Deploying
        </Badge>
      );
    case 4:
      return (
        <Badge variant='amber_light' className='w-[100px] h-[23px]'>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-yellow-400 opacity-75' />
            <span className='relative  h-full w-full  inline-flex size-2 rounded-full bg-yellow-500' />
          </span>
          Installing
        </Badge>
      );
    case 5:
      return (
        <Badge variant='success_light' className='w-[100px] h-[23px] flex items-center gap-2'>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-emerald-400 opacity-75' />
            <span className='relative  h-full w-full  inline-flex size-2 rounded-full bg-emerald-500' />
          </span>
          <span>Running</span>
        </Badge>
      );
    case 6:
      return (
        <Badge variant='success_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-emerald-500 me-1.5'></span>
          Finished
        </Badge>
      );
    case 7:
      return (
        <Badge variant='rose_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-rose-500 me-1.5'></span>
          Canceled
        </Badge>
      );
    case 8:
      return (
        <Badge variant='rose_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-rose-500 me-1.5'></span>
          Blocked
        </Badge>
      );

    default:
      return (
        <Badge variant='default' className='h-[23px] w-[100px] '>
          <span className='size-1.5 rounded-full bg-gray-500 me-1.5'></span>
          Unknown
        </Badge>
      );
  }
};

export const ProviderBadge = (status: number) => {
  switch (status) {
    case 0:
      return (
        <Badge variant='primay_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-primary-500 me-1.5'></span>
          Digital Ocean
        </Badge>
      );
    case 1:
      return (
        <Badge variant='sky_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-sky-500 me-1.5'></span>
          Vultr
        </Badge>
      );
    case 2:
      return (
        <Badge variant='success_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-green-500 me-1.5'></span>
          Linode
        </Badge>
      );
    case 3:
      return (
        <Badge variant='amber_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-yellow-500 me-1.5'></span>
          Aws
        </Badge>
      );
    case 4:
      return (
        <Badge variant='amber_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-yellow-500 me-1.5'></span>
          Google Cloud
        </Badge>
      );
    case 5:
      return (
        <Badge variant='primay_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-blue-500 me-1.5'></span>
          Azure
        </Badge>
      );
    case 6:
      return (
        <Badge variant='amber_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-orange-500 me-1.5'></span>
          Alibaba Cloud
        </Badge>
      );
    case 7:
      return (
        <Badge variant='success_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-green-500 me-1.5'></span>
          Ctr Cloud
        </Badge>
      );

    default:
      return (
        <Badge variant='default' className='h-[23px] w-[100px] '>
          <span className='size-1.5 rounded-full bg-gray-500 me-1.5'></span>
          Unknown
        </Badge>
      );
  }
};
