import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useCloseTicketMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'query'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.CLOSE_TICKET,
    method: 'POST',
    ...options
  });
};
