import type { ColumnDef } from '@tanstack/react-table';
import { NotepadText } from 'lucide-react';
import React, { useState } from 'react';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';

import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';

type TaskData = {
  id: string;
  type: number;
  payload: {
    timeframe?: string;
    auto_trading?: number;
    allow_dll_imports?: number;
    chart?: string;
    file?: string;
    file_sha256?: string;
    url?: string;
  } | null;
  result?: {
    runner_result?: string | null;
    status?: number;
  } | null;
  status: number;
  started_at: number;
  finished_at: number;
};
type FormattedTaskData = {
  Action: string;
  Status: string;
  StatusVariant: 'info' | 'warning' | 'success' | 'danger' | 'default';
  Duration: string;
  payload: TaskData['payload'];
  result: TaskData['result'] | null;
  id: string;
  type: number;
  status: number;
  started_at: number;
  finished_at: number;
};
type ModalContent = {
  title: string;
  content: TaskData['payload'] | TaskData['result'] | null;
};
const formatObjectDisplay = (obj: unknown) => {
  if (obj === null || obj === undefined) {
    return (
      <div className='p-6 text-center text-gray-400 border border-dashed border-gray-300 rounded-lg'>
        No data available
      </div>
    );
  }

  if (typeof obj !== 'object') {
    return (
      <div className='p-4 bg-gray-50 rounded-md border border-gray-300'>
        <div className='grid grid-cols-2 gap-8 items-center py-2'>
          <div className='font-semibold text-gray-600'>Value</div>
          <div className='font-mono text-gray-800'>{String(obj)}</div>
        </div>
      </div>
    );
  }

  if (Object.keys(obj as object).length === 0) {
    return (
      <div className='p-6 text-center text-gray-400 border border-dashed border-gray-300 rounded-lg'>
        Empty data object
      </div>
    );
  }

  return (
    <div className='space-y-2 border p-4 bg-gray-50 rounded-md border-gray-300'>
      {Object.entries(obj as object).map(([key, value]) => {
        // Safely handle nested objects
        let displayValue: React.ReactNode;
        if (value === null || value === undefined) {
          displayValue = <span className='text-gray-400'>null</span>;
        } else if (typeof value === 'object') {
          displayValue = <pre className='p-2 rounded-md text-sm overflow-x-auto'>{JSON.stringify(value, null, 2)}</pre>;
        } else {
          displayValue = <span className='text-gray-700'>{String(value)}</span>;
        }

        return (
          <div key={key} className='grid grid-cols-2 gap-8 items-center py-2 border-b border-gray-200 last:border-b-0'>
            <div className='font-semibold text-gray-600 capitalize'>{key.replace(/_/g, ' ')}</div>
            <div className='font-mono text-gray-800 overflow-x-auto'>{displayValue}</div>
          </div>
        );
      })}
    </div>
  );
};
const TasksTable = ({ taskData }: { taskData: TaskData[] }) => {
  const [modalContent, setModalContent] = useState<ModalContent | null>(null);
  const [page, setPage] = useState(1);
  const [per_page, setPerPage] = useState(1);

  const getActionDetails = (type: number) => {
    const actionMap = {
      0: { name: 'TERMINAL_INSTALL' },
      1: { name: 'TERMINAL_DESTROY' },
      2: { name: 'EA_INSTALL' },
      3: { name: 'EA_REMOVE' },
      4: { name: 'EA_UPDATE' },
      5: { name: 'EA_LOGS' },
      6: { name: 'SCREENSHOT' },
      7: { name: 'SCREENSHOT_LATENCY' },
      8: { name: 'ACCOUNT_SERVER' },
      9: { name: 'ACCOUNT_LOGIN' },
      10: { name: 'ACCOUNT_LOGOUT' },
      11: { name: 'ACCOUNT_OPEN_DEMO' },
      12: { name: 'OPTIONS_AUTO_TRADE' },
      13: { name: 'OPTIONS_WEB_REQUEST' },
      14: { name: 'MARKET_WATCH_SHOW_ALL' }
    };

    return actionMap[type as keyof typeof actionMap] || { name: `UNKNOWN_ACTION (${type})` };
  };
  const getStatusDetails = (status: number) => {
    const statusMap = {
      0: { name: 'PENDING', variant: 'info' },
      1: { name: 'QUEUED', variant: 'info' },
      2: { name: 'DELIVERED', variant: 'warning' },
      3: { name: 'RUNNING', variant: 'warning' },
      4: { name: 'COMPLETED', variant: 'success' },
      5: { name: 'FAILED', variant: 'danger' },
      6: { name: 'CANCELED', variant: 'danger' },
      7: { name: 'TERMINATED', variant: 'danger' }
    };

    return (
      statusMap[status as keyof typeof statusMap] || {
        name: `UNKNOWN (${status})`,
        variant: 'default'
      }
    );
  };
  const formatDuration = (started_at: number, finished_at: number): string => {
    if (!started_at || !finished_at) {
      return 'N/A';
    }

    const duration = finished_at - started_at;
    if (duration <= 0) {
      return 'Instant';
    }

    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      const minutes = Math.floor(duration / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(duration / 3600);
      const remainingMinutes = Math.floor((duration % 3600) / 60);
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const formattedTask: FormattedTaskData[] = taskData.map((task) => {
    const actionDetails = getActionDetails(task.type);
    const statusDetails = getStatusDetails(task.status || 0);
    const duration = formatDuration(task.started_at || 0, task.finished_at || 0);

    return {
      Action: actionDetails.name,
      Status: statusDetails.name,
      StatusVariant: statusDetails.variant as 'info' | 'warning' | 'success' | 'danger' | 'default',
      Duration: duration,
      payload: task.payload || null,
      result: task.result || null,
      id: task.id,
      type: task.type,
      status: task.status || 0,
      started_at: task.started_at || 0,
      finished_at: task.finished_at || 0
    };
  });

  const columns: ColumnDef<FormattedTaskData>[] = [
    {
      accessorKey: 'Action',
      header: 'Action',
      cell: ({ row }) => {
        return <div className='text-start text-sm font-medium text-gray-900'>{row.original.Action}</div>;
      }
    },
    {
      accessorKey: 'Data',
      header: 'Data',
      cell: ({ row }) => (
        <div className='flex items-center justify-start gap-x-2  text-gray-600 font-semibold'>
          <Button
            variant='outline'
            size='xs'
            onClick={() =>
              setModalContent({
                title: 'Payload Details',
                content: row.original.payload
              })}
            disabled={!row.original.payload}
          >
            <NotepadText />
            Payload
          </Button>
          <Button
            variant='outline'
            size='xs'
            onClick={() =>
              setModalContent({
                title: 'Result Details',
                content: row.original.result
              })}
          >
            <NotepadText />
            Result
          </Button>
        </div>
      )
    },
    {
      accessorKey: 'Status',
      header: 'Status',
      cell: ({ row }) => {
        const variant = row.original.StatusVariant;
        const baseClasses = 'inline-flex items-center rounded-sm px-1 py-0.5 text-[10px] font-medium ring-1 ring-inset';

        const variantClasses = {
          info: 'bg-blue-100 text-blue-700 ring-blue-600/10',
          warning: 'bg-yellow-100 text-yellow-700 ring-yellow-600/10',
          success: 'bg-green-100 text-green-700 ring-green-600/10',
          danger: 'bg-red-100 text-red-700 ring-red-600/10',
          default: 'bg-gray-100 text-gray-700 ring-gray-600/10'
        };

        return (
          <div className='text-start badge badge-sm badge-outline rounded-[30px]'>
            <span className={`${baseClasses} ${variantClasses[variant]}`}>{row.original.Status}</span>
          </div>
        );
      }
    },
    {
      accessorKey: 'Duration',
      header: 'Duration',
      cell: ({ row }) => {
        return <div className='text-start  text-gray-800'>{row.original.Duration}</div>;
      }
    }
  ];

  return (
    <>
      <DataTable
        title='Tasks'
        setPerpage={setPerPage}
        setPageNumber={setPage}
        columns={columns}
        dataTable={{
          data: formattedTask,
          pagination: {
            current_page: page,
            first_page_url: '',
            from: 0,
            last_page: Math.ceil(formattedTask.length / per_page),
            last_page_url: '',
            next_page_url: null,
            per_page,
            prev_page_url: '',
            total: formattedTask.length
          }
        }}
        pageNumber={page}
        isLoading={false}
        perpage={per_page}
      />
      <Dialog open={!!modalContent} onOpenChange={open => !open && setModalContent(null)}>
        <DialogContent className='sm:max-w-[800px] max-h-[80vh] overflow-auto'>
          <DialogHeader>
            <DialogTitle>{modalContent?.title}</DialogTitle>
          </DialogHeader>
          <div>{formatObjectDisplay(modalContent?.content)}</div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TasksTable;
