import fs from 'node:fs/promises';
import os from 'node:os';
import path from 'node:path';

export type RdpConfig = {
  address: string;
  username: string;
  password?: string;
  deleteCredentialsAfter?: boolean;
  safeMode?: boolean;
  autoReconnect?: boolean;
  fullscreen?: boolean;
  colors?: number;
  compression?: boolean;
  connectionType?: string;
  networkAutoDetect?: boolean;
  bandwidthAutoDetect?: boolean;
  showWallpaper?: boolean;
  fontSmoothing?: boolean;
  desktopComposition?: boolean;
  showDraggedWindow?: boolean;
  showMenuAnimations?: boolean;
  showThemes?: boolean;
  showBlinkingCursor?: boolean;
  audioPlayMode?: string;
  audioCaptureMode?: boolean;
  enableLocalPrinters?: boolean;
  enableLocalCOMPorts?: boolean;
  enableSmartCards?: boolean;
  enableClipboard?: boolean;
  enablePlugAndPlayDevices?: string;
  enableDrives?: string;
  enablePos?: boolean;
  launch?: string;
  launchWorkingDirectory?: string;
};
type FieldDefinition = {
  def: unknown;
  name?: string;
  fn?: (value: unknown) => unknown;
};

const mappingStructure: Record<keyof RdpConfig, FieldDefinition> = {
  address: { def: '', name: 'full address:s:' },
  username: { def: '', name: 'username:s:' },
  password: { def: '' },
  deleteCredentialsAfter: { def: true },
  safeMode: { def: false },
  autoReconnect: { def: true, name: 'autoreconnection enabled:i:' },
  fullscreen: { def: true, name: 'screen mode id:i:' },
  colors: { def: 32, name: 'session bpp:i:' },
  compression: { def: true, name: 'compression:i:' },
  connectionType: { def: 'auto', name: 'connection type:i:' },
  networkAutoDetect: { def: true, name: 'networkautodetect:i:' },
  bandwidthAutoDetect: { def: true, name: 'bandwidthautodetect:i:' },
  showWallpaper: { def: false, name: 'disable wallpaper:i:' },
  fontSmoothing: { def: false, name: 'allow font smoothing:i:' },
  desktopComposition: { def: false, name: 'allow desktop composition:i:' },
  showDraggedWindow: { def: false, name: 'disable full window drag:i:' },
  showMenuAnimations: { def: false, name: 'disable menu anims:i:' },
  showThemes: { def: true, name: 'disable themes:i:' },
  showBlinkingCursor: { def: true, name: 'disable cursor setting:i:' },
  audioPlayMode: { def: 'local', name: 'audiomode:i:' },
  audioCaptureMode: { def: false, name: 'audiocapturemode:i:' },
  enableLocalPrinters: { def: true, name: 'redirectprinters:i:' },
  enableLocalCOMPorts: { def: false, name: 'redirectcomports:i:' },
  enableSmartCards: { def: true, name: 'redirectsmartcards:i:' },
  enableClipboard: { def: true, name: 'redirectclipboard:i:' },
  enablePlugAndPlayDevices: { def: '', name: 'devicestoredirect:s:' },
  enableDrives: { def: '', name: 'drivestoredirect:s:' },
  enablePos: { def: false, name: 'redirectposdevices:i:' },
  launch: { def: '', name: 'alternate shell:s:' },
  launchWorkingDirectory: { def: '', name: 'shell working directory:s:' }
};

const defaultValues = Object.entries(mappingStructure).reduce(
  (acc, [key, value]) => {
    acc[key] = value.def;
    return acc;
  },
  {} as Record<string, unknown>
);

function sanitizeFilename(filename: string): string {
  // eslint-disable-next-line no-control-regex
  return filename.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_').replace(/^\.|\.$|^\s+|\s+$/g, '_');
}

const getRdpFileContent = (config: RdpConfig): string => {
  const fullConfig: RdpConfig = {
    ...defaultValues,
    ...config
  };

  return Object.entries(mappingStructure)
    .map(([key, field]) => {
      if (!field.name) {
        return '';
      }

      const value = fullConfig[key as keyof RdpConfig];
      const transformedValue = typeof value === 'boolean' ? (value ? '1' : '0') : value;

      return `${field.name}${transformedValue}\n`;
    })
    .join('');
};

export async function buildRdpFile(config: RdpConfig): Promise<string> {
  const fileContent = getRdpFileContent(config);
  const fileName = path.join(os.tmpdir(), `${sanitizeFilename(config.address)}.rdp`);
  await fs.writeFile(fileName, fileContent);
  return fileName;
}
