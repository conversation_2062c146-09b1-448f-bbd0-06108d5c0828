import { z } from 'zod';

export const DateTimePropsSchema = z.object({
  value: z.union([
    z.string().refine(
      (val) => {
        try {
          const date = new Date(val);
          return !Number.isNaN(date.getTime()) && val === date.toISOString();
        } catch {
          return false;
        }
      },
      {
        message: 'Must be a valid ISO 8601 string'
      }
    ),

    z.number().refine(
      (val) => {
        const date = val > 1e12 ? new Date(val) : new Date(val * 1000);
        return !Number.isNaN(date.getTime());
      },
      {
        message: 'Must be a valid UNIX timestamp (seconds or milliseconds)'
      }
    ),

    z
      .undefined()
      .or(z.null())
      .transform(() => new Date())
  ]),
  className: z.string().default('').optional()
});

export type IDateTimeProps = z.infer<typeof DateTimePropsSchema>;
