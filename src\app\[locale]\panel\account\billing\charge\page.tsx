'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { AnimatePresence, motion } from 'framer-motion';
import { useUser } from 'hooks/user/user';
import { ArrowLeft, CreditCard } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { MastercardIcon, VisaIcon } from '@/components/icons/card-icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

type PaymentMethod = 'card' | 'paypal' | 'crypto';

type CardNumberSegments = [string, string, string, string];

const cardFormSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  cardNumber: z.string().length(16, 'Card number must be 16 digits'),
  expiryDate: z.string().min(5, 'Invalid expiry date'),
  cvv: z.string().min(3, 'Invalid CVV')
});

const cryptoFormSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  currency: z.string().min(1, 'Please select a currency')
});

const paypalFormSchema = z.object({
  amount: z.string().min(1, 'Amount is required')
});

const detectCardType = (number: string) => {
  const firstDigit = number.charAt(0);
  const firstTwoDigits = number.slice(0, 2);

  if (firstDigit === '4') {
    return 'visa';
  }
  if (firstTwoDigits >= '51' && firstTwoDigits <= '55') {
    return 'mastercard';
  }
  return 'unknown';
};

export default function ChargePage() {
  const { data: userInfo } = useUser();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('card');
  const [cardSegments, setCardSegments] = useState<CardNumberSegments>(['', '', '', '']);
  const [isCardFlipped, setIsCardFlipped] = useState(false);
  const [focusedField, setFocusedField] = useState<'number' | 'name' | 'expiry' | 'cvv' | null>(null);

  const cardForm = useForm<z.infer<typeof cardFormSchema>>({
    resolver: zodResolver(cardFormSchema),
    defaultValues: {
      amount: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    }
  });

  const cryptoForm = useForm<z.infer<typeof cryptoFormSchema>>({
    resolver: zodResolver(cryptoFormSchema),
    defaultValues: {
      amount: '',
      currency: ''
    }
  });

  const paypalForm = useForm<z.infer<typeof paypalFormSchema>>({
    resolver: zodResolver(paypalFormSchema),
    defaultValues: {
      amount: ''
    }
  });

  function onSubmit(_values: any) {
    // Implementation here when needed
  }

  return (
    <div className='container py-8'>
      <div className='flex items-center justify-between mb-8'>
        <h1 className='text-3xl font-bold'>Add Funds</h1>
        <Link
          href='/panel/account/billing'
          className='text-sm text-muted-foreground hover:text-primary flex items-center gap-1 transition-colors'
        >
          <ArrowLeft className='size-4' />
          Back to Billing
        </Link>
      </div>

      <div className='flex flex-col lg:flex-row gap-8'>
        {/* Payment Method Selection - Left Section */}
        <div className='lg:w-1/2 flex flex-col gap-3 h-full overflow-auto'>
          <Card
            className={cn(
              'cursor-pointer transition-all hover:bg-muted/50',
              selectedMethod === 'card' && 'border-primary bg-muted/50'
            )}
            onClick={() => setSelectedMethod('card')}
          >
            <CardHeader className='p-4'>
              <div className='flex items-center gap-3'>
                <CreditCard className='size-8 text-primary shrink-0' />
                <div>
                  <CardTitle className='text-base'>Credit Card</CardTitle>
                  <CardDescription>Pay securely with your credit card</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card
            className={cn(
              'cursor-pointer transition-all hover:bg-muted/50',
              selectedMethod === 'paypal' && 'border-primary bg-muted/50'
            )}
            onClick={() => setSelectedMethod('paypal')}
          >
            <CardHeader className='p-4'>
              <div className='flex items-center gap-3'>
                <svg
                  className='size-8 text-[#00457C] shrink-0'
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 48 48'
                >
                  <path
                    fill='#001C64'
                    d='M37.972 13.82c.107-5.565-4.485-9.837-10.799-9.837H14.115a1.278 1.278 0 0 0-1.262 1.079L7.62 37.758a1.038 1.038 0 0 0 1.025 1.2h7.737l-1.21 7.572a1.038 1.038 0 0 0 1.026 1.2H22.5c.305 0 .576-.11.807-.307.231-.198.269-.471.316-.772l1.85-10.885c.047-.3.2-.69.432-.888.231-.198.433-.306.737-.307H30.5c6.183 0 11.43-4.394 12.389-10.507.678-4.34-1.182-8.287-4.916-10.244Z'
                  />
                  <path
                    fill='#0070E0'
                    d='m18.056 26.9-1.927 12.22-1.21 7.664a1.038 1.038 0 0 0 1.026 1.2h6.67a1.278 1.278 0 0 0 1.261-1.079l1.758-11.14a1.277 1.277 0 0 1 1.261-1.078h3.927c6.183 0 11.429-4.51 12.388-10.623.68-4.339-1.504-8.286-5.238-10.244-.01.462-.05.923-.121 1.38-.959 6.112-6.206 10.623-12.389 10.623h-6.145a1.277 1.277 0 0 0-1.261 1.077Z'
                  />
                  <path
                    fill='#003087'
                    d='M16.128 39.12h-7.76a1.037 1.037 0 0 1-1.025-1.2l5.232-33.182a1.277 1.277 0 0 1 1.262-1.078h13.337c6.313 0 10.905 4.595 10.798 10.16-1.571-.824-3.417-1.295-5.44-1.295H21.413a1.278 1.278 0 0 0-1.261 1.078L18.057 26.9l-1.93 12.22Z'
                  />
                </svg>
                <div>
                  <CardTitle className='text-base'>PayPal</CardTitle>
                  <CardDescription>Fast and secure payment with PayPal</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          <div
            role='button'
            tabIndex={0}
            className={cn(
              'cursor-pointer transition-all hover:bg-muted/50',
              selectedMethod === 'crypto' && 'border-primary bg-muted/50'
            )}
            onClick={() => setSelectedMethod('crypto')}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setSelectedMethod('crypto');
              }
            }}
          >
            <CardHeader className='p-4'>
              <div className='flex items-center gap-3'>
                <svg className='size-8 shrink-0' viewBox='0 0 88 88' xmlns='http://www.w3.org/2000/svg'>
                  <path fill='#F7931A' d='M44 88c24.3 0 44-19.7 44-44S68.3 0 44 0 0 19.7 0 44s19.7 44 44 44z' />
                  <path
                    fill='#FFFFFF'
                    d='M63.4 39.7c0.8-5.9-3.6-9-9.6-11.1l2-7.9-4.8-1.2-1.9 7.7c-1.3-0.3-2.6-0.6-3.8-0.9l1.9-7.8-4.8-1.2-2 7.9c-1-0.2-2.1-0.5-3.1-0.7l0 0-6.7-1.7-1.3 5.1c0 0 3.6 0.8 3.5 0.9 2 0.5 2.3 1.8 2.3 2.8l-2.3 9.1c0.1 0 0.3 0.1 0.5 0.2-0.2-0.1-0.3-0.1-0.5-0.1l-3.2 12.7c-0.2 0.6-0.9 1.5-2.2 1.2 0 0-3.5-0.9-3.5-0.9l-2.4 5.5 6.3 1.6c1.2 0.3 2.3 0.6 3.4 0.9l-2 8 4.8 1.2 2-8c1.3 0.4 2.6 0.7 3.8 1l-2 7.9 4.8 1.2 2-8c8.2 1.5 14.4 0.9 17-6.5 2.1-6-0.1-9.5-4.5-11.7 3.2-0.7 5.6-2.9 6.2-7.3zm-11 16c-1.5 6-11.6 2.8-14.9 2l2.6-10.6c3.3 0.8 13.9 2.4 12.3 8.6zm1.5-15.5c-1.3 5.4-9.8 2.7-12.5 2l2.4-9.7c2.7 0.7 11.3 2 10.1 7.7z'
                  />
                </svg>
                <div>
                  <CardTitle className='text-base'>Cryptocurrency</CardTitle>
                  <CardDescription>Pay with Bitcoin, Ethereum, and more</CardDescription>
                </div>
              </div>
            </CardHeader>
          </div>
        </div>

        {/* Payment Forms - Right Section */}
        <div className='lg:w-1/2'>
          <Card className='h-full'>
            <CardHeader>
              <CardTitle>
                {selectedMethod === 'card' && 'Credit Card Payment'}
                {selectedMethod === 'paypal' && 'PayPal Payment'}
                {selectedMethod === 'crypto' && 'Cryptocurrency Payment'}
              </CardTitle>
              <CardDescription>
                {selectedMethod === 'card' && 'Enter your card details to proceed'}
                {selectedMethod === 'paypal' && 'Continue to PayPal checkout'}
                {selectedMethod === 'crypto' && 'Select cryptocurrency and amount'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedMethod === 'card' ? (
                <Form {...cardForm}>
                  <form onSubmit={cardForm.handleSubmit(onSubmit)} className='space-y-6'>
                    <div className='space-y-3'>
                      <FormLabel>Amount (USD)</FormLabel>
                      <div className='grid grid-cols-2 md:grid-cols-3 gap-2'>
                        {[25, 50, 100, 200, 500].map(amount => (
                          <Button
                            key={amount}
                            type='button'
                            variant={cardForm.getValues('amount') === amount.toString() ? 'default' : 'outline'}
                            className='w-full'
                            onClick={() => cardForm.setValue('amount', amount.toString())}
                          >
                            $
                            {amount}
                          </Button>
                        ))}
                        <FormField
                          control={cardForm.control}
                          name='amount'
                          render={({ field }) => (
                            <FormItem className='m-0'>
                              <FormControl>
                                <Input
                                  placeholder='Custom amount'
                                  className='h-10'
                                  {...field}
                                  onChange={(e) => {
                                    // Only allow numbers and decimal point
                                    const value = e.target.value.replace(/[^\d.]/g, '');
                                    // Ensure only one decimal point
                                    const parts = value.split('.');
                                    if (parts.length > 2) {
                                      return;
                                    }
                                    if (parts && parts[1] && parts[1].length > 2) {
                                      return;
                                    }
                                    field.onChange(value);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className='relative perspective-1000 w-full max-w-[430px] mx-auto mb-8'>
                      <AnimatePresence initial={false} mode='wait'>
                        <motion.div
                          className={cn(
                            'relative w-full h-[240px] rounded-xl transition-all',
                            'preserve-3d cursor-pointer',
                            isCardFlipped ? 'rotate-y-180' : ''
                          )}
                          animate={{ rotateY: isCardFlipped ? 180 : 0 }}
                          transition={{
                            duration: 0.4, // Reduced from 0.7
                            ease: 'easeInOut'
                          }}
                        >
                          {/* Front of the card */}
                          <div
                            className={cn(
                              'absolute inset-0 w-full h-full rounded-xl p-6',
                              'bg-gradient-to-br from-primary/80 to-primary',
                              'backface-hidden shadow-xl'
                            )}
                          >
                            <div className='flex flex-col justify-between h-full'>
                              <div className='flex justify-between items-start'>
                                <div className='w-12 h-12'>
                                  <svg className='w-full h-full' viewBox='0 0 24 24'>
                                    <path
                                      fill='currentColor'
                                      d='M12 2L2 7l10 5l10-5l-10-5zM2 17l10 5l10-5M2 12l10 5l10-5'
                                    />
                                  </svg>
                                </div>
                                {cardSegments.join('') && (
                                  <motion.div
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    className='text-2xl text-white'
                                  >
                                    {detectCardType(cardSegments.join('')) === 'visa' && <VisaIcon />}
                                    {detectCardType(cardSegments.join('')) === 'mastercard' && <MastercardIcon />}
                                    {detectCardType(cardSegments.join('')) === 'unknown' && <CreditCard size={40} />}
                                  </motion.div>
                                )}
                              </div>

                              <div className='space-y-4'>
                                <div className='flex gap-4'>
                                  {cardSegments.map((segment, index) => (
                                    <div
                                      key={`display-segment-${['first', 'second', 'third', 'fourth'][index]}`}
                                      className={cn(
                                        'text-xl font-mono tracking-wider text-white/90',
                                        focusedField === 'number' ? 'text-white' : 'text-white/80'
                                      )}
                                    >
                                      {segment || '••••'}
                                    </div>
                                  ))}
                                </div>

                                <div className='flex justify-between items-end'>
                                  <div>
                                    <div className='text-xs text-white/60 uppercase'>Card Holder</div>
                                    <div className='text-white font-medium'>
                                      {userInfo?.name?.toUpperCase() || 'CARD HOLDER'}
                                    </div>
                                  </div>
                                  <div>
                                    <div className='text-xs text-white/60 uppercase'>Expires</div>
                                    <div className='text-white font-medium'>
                                      {cardForm.watch('expiryDate') || 'MM/YY'}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Back of the card */}
                          <div
                            className={cn(
                              'absolute inset-0 w-full h-full rounded-xl',
                              'bg-gradient-to-br from-primary/90 to-primary',
                              'backface-hidden rotate-y-180 shadow-xl'
                            )}
                          >
                            <div className='w-full h-12 bg-black/30 my-4' />
                            <div className='px-6'>
                              <div className='flex justify-end items-center gap-4'>
                                <div className='text-xs text-white/60 uppercase'>CVV</div>
                                <div className='bg-white/20 rounded h-8 w-16 flex items-center justify-center'>
                                  <span className='font-mono text-white'>{cardForm.watch('cvv') || '•••'}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      </AnimatePresence>

                      {/* Form inputs */}
                      <div className='mt-8 space-y-4'>
                        <FormField
                          control={cardForm.control}
                          name='cardNumber'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Card Number</FormLabel>
                              <FormControl>
                                <div className='flex gap-2'>
                                  {[0, 1, 2, 3].map(segmentIndex => (
                                    <Input
                                      key={`card-segment-${segmentIndex}`}
                                      placeholder='0000'
                                      maxLength={4}
                                      className='h-10 text-center'
                                      value={cardSegments[segmentIndex]}
                                      onFocus={() => setFocusedField('number')}
                                      onBlur={() => setFocusedField(null)}
                                      onChange={(e) => {
                                        const value = e.target.value.replace(/\D/g, '').slice(0, 4);
                                        const newSegments = [...cardSegments] as CardNumberSegments;
                                        newSegments[segmentIndex] = value;
                                        setCardSegments(newSegments);
                                        field.onChange(newSegments.join(''));

                                        if (value.length === 4 && segmentIndex < 3) {
                                          const nextInput = e.target.parentElement?.querySelector(
                                            `input:nth-of-type(${segmentIndex + 2})`
                                          ) as HTMLInputElement;
                                          if (nextInput) {
                                            nextInput.focus();
                                          }
                                        }
                                      }}
                                      onKeyDown={(e) => {
                                        if (e.key === 'Backspace' && !cardSegments[segmentIndex] && segmentIndex > 0) {
                                          const prevInput = e.currentTarget.parentElement?.querySelector(
                                            `input:nth-of-type(${segmentIndex})`
                                          ) as HTMLInputElement;
                                          if (prevInput) {
                                            prevInput.focus();
                                          }
                                        }
                                      }}
                                    />
                                  ))}
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className='grid grid-cols-2 gap-4'>
                          <FormField
                            control={cardForm.control}
                            name='expiryDate'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Expiry Date</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder='MM/YY'
                                    {...field}
                                    onFocus={() => setFocusedField('expiry')}
                                    onBlur={() => setFocusedField(null)}
                                    onChange={(e) => {
                                      let value = e.target.value.replace(/\D/g, '');
                                      if (value.length >= 2) {
                                        value = `${value.slice(0, 2)}/${value.slice(2, 4)}`;
                                      }
                                      field.onChange(value);
                                    }}
                                    maxLength={5}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={cardForm.control}
                            name='cvv'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>CVV</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder='123'
                                    maxLength={3}
                                    {...field}
                                    onFocus={() => {
                                      requestAnimationFrame(() => setIsCardFlipped(true)); // Use requestAnimationFrame for smoother transition
                                      setFocusedField('cvv');
                                    }}
                                    onBlur={() => {
                                      requestAnimationFrame(() => setIsCardFlipped(false));
                                      setFocusedField(null);
                                    }}
                                    onChange={(e) => {
                                      const value = e.target.value.replace(/\D/g, '').slice(0, 3);
                                      field.onChange(value);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>

                    <Button type='submit' className='w-full' disabled={!cardForm.getValues('amount')}>
                      Pay $
                      {Number(cardForm.watch('amount') || 0).toFixed(2)}
                    </Button>
                  </form>
                </Form>
              ) : selectedMethod === 'paypal' ? (
                <Form {...paypalForm}>
                  <form onSubmit={paypalForm.handleSubmit(onSubmit)} className='space-y-4'>
                    <FormField
                      control={paypalForm.control}
                      name='amount'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Amount (USD)</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Enter amount' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type='submit' className='w-full'>
                      Continue to PayPal
                    </Button>
                  </form>
                </Form>
              ) : (
                <Form {...cryptoForm}>
                  <form onSubmit={cryptoForm.handleSubmit(onSubmit)} className='space-y-6'>
                    <FormField
                      control={cryptoForm.control}
                      name='amount'
                      render={({ field }) => (
                        <FormItem className='space-y-3'>
                          <FormLabel>Amount (USD)</FormLabel>
                          <div className='grid grid-cols-2 md:grid-cols-3 gap-2'>
                            {[25, 50, 100, 200, 500].map(amount => (
                              <Button
                                key={amount}
                                type='button'
                                variant={field.value === amount.toString() ? 'default' : 'outline'}
                                className='w-full'
                                onClick={() => field.onChange(amount.toString())}
                              >
                                $
                                {amount}
                              </Button>
                            ))}
                            <FormControl>
                              <Input
                                placeholder='Custom amount'
                                className='h-10'
                                {...field}
                                onChange={(e) => {
                                  const value = e.target.value.replace(/[^\d.]/g, '');
                                  const parts = value.split('.');
                                  if (parts.length > 2) {
                                    return;
                                  }
                                  if (parts && parts[1] && parts[1].length > 2) {
                                    return;
                                  }
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={cryptoForm.control}
                      name='currency'
                      render={({ field }) => (
                        <FormItem className='space-y-3'>
                          <FormLabel>Select Cryptocurrency</FormLabel>
                          <div className='grid grid-cols-1 gap-2'>
                            {[
                              {
                                value: 'btc',
                                name: 'Bitcoin',
                                symbol: 'BTC',
                                price: 43250.65,
                                color: '#F7931A',
                                icon: (
                                  <svg
                                    className='size-8 shrink-0'
                                    viewBox='0 0 88 88'
                                    xmlns='http://www.w3.org/2000/svg'
                                  >
                                    <path
                                      fill='currentColor'
                                      d='M44 88c24.3 0 44-19.7 44-44S68.3 0 44 0 0 19.7 0 44s19.7 44 44 44z'
                                    />
                                    <path
                                      fill='#FFFFFF'
                                      d='M63.4 39.7c0.8-5.9-3.6-9-9.6-11.1l2-7.9-4.8-1.2-1.9 7.7c-1.3-0.3-2.6-0.6-3.8-0.9l1.9-7.8-4.8-1.2-2 7.9c-1-0.2-2.1-0.5-3.1-0.7l0 0-6.7-1.7-1.3 5.1c0 0 3.6 0.8 3.5 0.9 2 0.5 2.3 1.8 2.3 2.8l-2.3 9.1c0.1 0 0.3 0.1 0.5 0.2-0.2-0.1-0.3-0.1-0.5-0.1l-3.2 12.7c-0.2 0.6-0.9 1.5-2.2 1.2 0 0-3.5-0.9-3.5-0.9l-2.4 5.5 6.3 1.6c1.2 0.3 2.3 0.6 3.4 0.9l-2 8 4.8 1.2 2-8c1.3 0.4 2.6 0.7 3.8 1l-2 7.9 4.8 1.2 2-8c8.2 1.5 14.4 0.9 17-6.5 2.1-6-0.1-9.5-4.5-11.7 3.2-0.7 5.6-2.9 6.2-7.3zm-11 16c-1.5 6-11.6 2.8-14.9 2l2.6-10.6c3.3 0.8 13.9 2.4 12.3 8.6zm1.5-15.5c-1.3 5.4-9.8 2.7-12.5 2l2.4-9.7c2.7 0.7 11.3 2 10.1 7.7z'
                                    />
                                  </svg>
                                )
                              },
                              {
                                value: 'eth',
                                name: 'Ethereum',
                                symbol: 'ETH',
                                price: 2280.3,
                                color: '#343434',
                                icon: (
                                  <svg
                                    className='size-8 shrink-0'
                                    viewBox='0 0 784.37 1277.39'
                                    xmlns='http://www.w3.org/2000/svg'
                                  >
                                    <g>
                                      <polygon
                                        fill='#343434'
                                        points='392.07,0 383.5,29.11 383.5,873.74 392.07,882.29 784.13,650.54'
                                      />
                                      <polygon fill='#8C8C8C' points='392.07,0 -0,650.54 392.07,882.29 392.07,472.33' />
                                      <polygon
                                        fill='#3C3C3B'
                                        points='392.07,956.52 387.24,962.41 387.24,1263.28 392.07,1277.38 784.37,724.89'
                                      />
                                      <polygon fill='#8C8C8C' points='392.07,1277.38 392.07,956.52 -0,724.89' />
                                      <polygon fill='#141414' points='392.07,882.29 784.13,650.54 392.07,472.33' />
                                      <polygon fill='#393939' points='0,650.54 392.07,882.29 392.07,472.33' />
                                    </g>
                                  </svg>
                                )
                              },
                              {
                                value: 'usdt',
                                name: 'Tether',
                                symbol: 'USDT',
                                price: 1.0,
                                color: '#50AF95',
                                icon: (
                                  <svg
                                    className='size-8 shrink-0'
                                    viewBox='0 0 2000 2000'
                                    xmlns='http://www.w3.org/2000/svg'
                                  >
                                    <path
                                      fill='#50AF95'
                                      d='M1000,0c552.26,0,1000,447.74,1000,1000S1552.26,2000,1000,2000S0,1552.26,0,1000S447.74,0,1000,0'
                                    />
                                    <path
                                      fill='#FFFFFF'
                                      d='M1123.44,866.06c-9.62,4.52-57.42,23.76-144.12,23.76h-0.84c-86.7,0-134.5-19.24-144.12-23.76 c-576.28-270.28-497.64-563.76-487.6-589.94c4.1-10.78,9.46-19.66,15.98-26.88c96.08-106.74,485.28-66.96,614.32-66.96h2.48 c129.04,0,518.24-39.78,614.32,66.96c6.52,7.22,11.88,16.1,15.98,26.88C1621.08,302.3,1699.72,595.78,1123.44,866.06z'
                                    />
                                  </svg>
                                )
                              },
                              {
                                value: 'usdc',
                                name: 'USD Coin',
                                symbol: 'USDC',
                                price: 1.0,
                                color: '#2775CA',
                                icon: (
                                  <svg
                                    className='size-8 shrink-0'
                                    viewBox='0 0 2000 2000'
                                    xmlns='http://www.w3.org/2000/svg'
                                  >
                                    <circle cx='1000' cy='1000' r='1000' fill='#2775CA' />
                                    <path
                                      fill='#FFFFFF'
                                      d='M1275.24,976.45c0-145.39-87.23-195.22-261.04-215.89c-126.94-15.25-152.33-48.15-152.33-104.86 c0-57.99,42.19-96.09,124.85-96.09c73.25,0,114.17,23.51,134.84,82.77c4.17,12.51,15.94,20.84,29.19,20.84h66.09 c17.29,0,31.28-14,31.28-31.28v-5.21c-20.84-102.77-99.74-151.59-216.63-151.59c-145.39,0-246.53,77.42-246.53,198.13 c0,134.84,82.03,190.31,255.39,211.72c121.68,16.68,157.54,41.45,157.54,107.77c0,66.83-56.99,112.82-134.84,112.82 c-118.34,0-162.91-49.89-172.75-116.25c-2.09-14.6-14.6-25.76-29.93-25.76h-69.74c-17.29,0-31.28,14-31.28,31.28v5.21 c19.76,117.6,94.53,192.92,250.7,192.92C1173.21,1203.98,1275.24,1122.58,1275.24,976.45z'
                                    />
                                  </svg>
                                )
                              }
                            ].map(crypto => (
                              <button
                                key={crypto.value} // Add unique key using crypto.value
                                type='button'
                                className={cn(
                                  'relative w-full p-4 rounded-lg border cursor-pointer transition-all text-left',
                                  'hover:bg-muted/50',
                                  field.value === crypto.value
                                  && 'border-primary bg-muted/50 ring-2 ring-primary ring-offset-2'
                                )}
                                onClick={() => field.onChange(crypto.value)}
                              >
                                <div className='flex items-center justify-between w-full'>
                                  <div className='flex items-center gap-3'>
                                    <div
                                      className='text-[--icon-color]'
                                      style={{ '--icon-color': crypto.color } as React.CSSProperties}
                                    >
                                      {crypto.icon}
                                    </div>
                                    <div className='text-left'>
                                      <div className='font-medium'>{crypto.name}</div>
                                      <div className='text-sm text-muted-foreground'>{crypto.symbol}</div>
                                    </div>
                                  </div>
                                  <div className='text-right'>
                                    <div className='font-medium'>
                                      $
                                      {crypto.price.toLocaleString()}
                                    </div>
                                    <div className='text-sm text-muted-foreground'>per coin</div>
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type='submit'
                      className='w-full'
                      disabled={!cryptoForm.watch('amount') || !cryptoForm.watch('currency')}
                    >
                      Generate Payment Address
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
