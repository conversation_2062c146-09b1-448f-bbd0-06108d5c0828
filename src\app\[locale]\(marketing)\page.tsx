import { getTranslations, setRequestLocale } from 'next-intl/server';
import BlogPosts from '@/components/blog/blog';
import Cta from '@/components/marketing/cta';
import Hero from '@/components/marketing/hero';
import StartBuild from '@/components/marketing/start-build';

import Stats from '@/components/marketing/stats';

type IIndexProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IIndexProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Index'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default async function Index(props: IIndexProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  // const t = await getTranslations({
  // locale,
  // namespace: "Index",
  // });

  return (
    <>
      <Hero />
      <StartBuild />
      <BlogPosts postLimit={3} />
      <Stats />
      {/* <Features02 /> */}
      {/* <Features03 /> */}
      {/* <PricingTabs /> */}
      {/* <Testimonials /> */}
      <Cta />
    </>
  );
}
