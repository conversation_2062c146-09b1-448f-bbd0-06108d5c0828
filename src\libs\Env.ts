import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const Env = createEnv({
  server: {
    ARCJET_KEY: z.string().startsWith('ajkey_').optional(),
    LOGTAIL_SOURCE_TOKEN: z.string().optional()
  },
  client: {
    NEXT_PUBLIC_APP_URL: z.string().optional()
  },
  shared: {
    NODE_ENV: z.enum(['test', 'development', 'production']).optional()
  },
  runtimeEnv: {
    ARCJET_KEY: process.env.ARCJET_KEY,
    LOGTAIL_SOURCE_TOKEN: process.env.LOGTAIL_SOURCE_TOKEN,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NODE_ENV: process.env.NODE_ENV
  }
});
