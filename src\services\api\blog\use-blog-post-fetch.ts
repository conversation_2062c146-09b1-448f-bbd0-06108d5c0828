import type { IUseQueryFactoryProps } from 'hooks/api/use-query-factory/use-query-factory.type';
import type { IPostInfo } from 'types/blog';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { useQueryFactory } from 'hooks/api/use-query-factory';

export const useBlogPostFetch = (postLimit: number, options?: Pick<IUseQueryFactoryProps<IPostInfo[]>, 'enabled'>) => {
  const url = `${API_ROUTES.BLOG_POSTS}?_embed&per_page=${postLimit}`;

  return useQueryFactory<IPostInfo[]>({
    queryKey: [...API_QUERY_KEY.BLOG_POSTS, postLimit],
    url,
    staleTime: 0,
    refetchOnWindowFocus: true,
    ...options,
    select: (data) => {
      return Array.isArray(data) ? data : [];
    },
    queryFn: async () => {
      try {
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Error fetching data');
        }

        const data = await response.json();

        if (Array.isArray(data)) {
          return data;
        } else {
          console.error('Fetched data is not an array:', data);
          return [];
        }
      } catch (error) {
        console.error('Fetch error:', error);
        return [];
      }
    }
  });
};
