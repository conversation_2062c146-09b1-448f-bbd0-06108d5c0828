import { getTranslations } from 'next-intl/server';

type IBrandBookProps = {
  params: Promise<{ locale: string }>;
  children: React.ReactNode;
};

export async function generateMetadata(props: IBrandBookProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'About'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default function Layout({ children }: IBrandBookProps) {
  return <>{children}</>;
}
