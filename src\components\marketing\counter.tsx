'use client';

import { useEffect, useRef, useState } from 'react';

type CounterProps = {
  number: number;
  duration?: number;
};

export default function Counter({ number = 0, duration = 3000 }: CounterProps) {
  const counterElement = useRef<HTMLSpanElement | null>(null);
  const startTimestamp = useRef<number | null>(null);
  const [counterValue, setCounterValue] = useState<string>('0');
  const [animationCompleted, setAnimationCompleted] = useState<boolean>(false);
  let animationRequestId: number | null = null;
  const observerRef = useRef<IntersectionObserver | null>(null);

  const precision: number = number % 1 === 0 ? 0 : (number.toString().split('.')[1] || []).length;

  const easeOut = (t: number): number => {
    return 1 - (1 - t) ** 5;
  };

  const startAnimation = () => {
    const step = (timestamp: number) => {
      if (!startTimestamp.current) {
        startTimestamp.current = timestamp;
      }
      const progress: number = Math.min((timestamp - (startTimestamp.current || 0)) / duration, 1);
      const easedProgress: number = easeOut(progress);
      const newRawValue: number = Number.parseFloat((easedProgress * number).toFixed(precision));
      setCounterValue(newRawValue.toFixed(precision));

      if (progress < 1) {
        animationRequestId = window.requestAnimationFrame(step);
      } else {
        setAnimationCompleted(true);
      }
    };

    animationRequestId = window.requestAnimationFrame(step);
  };

  useEffect(() => {
    observerRef.current = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !animationCompleted) {
          startAnimation();
        }
      });
    });

    if (counterElement.current) {
      observerRef.current.observe(counterElement.current);
    }

    return () => {
      if (animationRequestId) {
        window.cancelAnimationFrame(animationRequestId);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    if (!animationCompleted) {
      const animate = () => {
        // Animation logic
      };

      animationRequestId = requestAnimationFrame(animate);
      return () => {
        if (animationRequestId !== null) {
          cancelAnimationFrame(animationRequestId);
        }
      };
    }

    // Add return for the case when animationCompleted is true
    return () => {};
  }, [animationCompleted]);

  return <span ref={counterElement}>{counterValue}</span>;
}
