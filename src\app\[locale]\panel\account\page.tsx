'use client';
import { AnimatePresence, motion } from 'framer-motion';
import {
  // Activity,
  ArrowRight,
  BadgeCheck,
  Bell,
  // Box,
  CreditCard,
  Key,
  Paintbrush,
  Search,
  ShieldCheck
} from 'lucide-react';
import { useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { cn } from '@/lib/utils';

export default function AccountPage() {
  const [searchQuery, setSearchQuery] = useState('');

  const accountItems = [
    {
      icon: BadgeCheck,
      title: 'Personal info',
      description: 'Manage your profile details and preferences',
      href: '/panel/account/profile',
      color: 'from-blue-500/20 to-cyan-500/20 hover:from-blue-500/40 hover:to-cyan-500/40',
      category: 'profile'
    },
    {
      icon: ShieldCheck,
      title: 'Security Center',
      description: 'Enhanced password and authentication settings',
      href: '/panel/account/security',
      color: 'from-green-500/20 to-emerald-500/20 hover:from-green-500/40 hover:to-emerald-500/40',
      category: 'security'
    },
    {
      icon: CreditCard,
      title: 'Billing Dashboard',
      description: 'Payment methods and subscription management',
      href: '/panel/account/billing',
      color: 'from-purple-500/20 to-pink-500/20 hover:from-purple-500/40 hover:to-pink-500/40',
      category: 'billing'
    },
    {
      icon: Bell,
      title: 'Notification Hub',
      description: 'Customize your alert and message preferences',
      href: '/panel/account/notifications',
      color: 'from-yellow-500/20 to-orange-500/20 hover:from-yellow-500/40 hover:to-orange-500/40',
      category: 'notifications'
    },
    // {
    //   icon: Box,
    //   title: 'Integration Suite',
    //   description: 'Connect and manage third-party services',
    //   href: '/panel/account/integrations',
    //   color: 'from-red-500/20 to-orange-500/20 hover:from-red-500/40 hover:to-orange-500/40',
    //   category: 'integrations'
    // },
    {
      icon: Key,
      title: 'API Management',
      description: 'Generate and monitor API keys and usage',
      href: '/panel/account/api-keys',
      color: 'from-indigo-500/20 to-purple-500/20 hover:from-indigo-500/40 hover:to-purple-500/40',
      category: 'developer'
    },
    {
      icon: Paintbrush,
      title: 'Settings',
      description: 'Customize your interface and theme preferences',
      href: PAGE_ROUTES.PANEL_SETTING,
      color: 'from-teal-500/20 to-green-500/20 hover:from-teal-500/40 hover:to-green-500/40',
      category: 'customization'
    }
    // {
    //   icon: Activity,
    //   title: 'Activity Monitor',
    //   description: 'Track account activities and system logs',
    //   href: '/panel/account/activity',
    //   color: 'from-blue-500/20 to-indigo-500/20 hover:from-blue-500/40 hover:to-indigo-500/40',
    //   category: 'monitoring'
    // }
  ];

  const filteredItems = accountItems.filter(
    item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase())
      || item.description.toLowerCase().includes(searchQuery.toLowerCase())
      || item.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className='min-h-screen w-full bg-background'>
      <div className='relative border-b overflow-hidden bg-gradient-to-b from-background to-background/80'>
        <div className='absolute inset-0 bg-grid-white/[0.02] bg-grid-pattern' />
        <div className='absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent' />
        <div className='mx-auto max-w-7xl px-6 py-12 relative'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='space-y-6'
          >
            <div className='flex items-center justify-between'>
              <div className='space-y-1'>
                <h1 className='text-4xl  font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/50'>
                  Account Center
                </h1>
                <p className='text-lg text-muted-foreground max-w-2xl'>
                  Manage your account settings and preferences in one place
                </p>
              </div>
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <div className='relative group'>
                  <div className='relative flex items-center bg-background/80 backdrop-blur-none border border-primary/10 rounded-md pl-4 pr-2 h-10'>
                    <Search className='h-4 w-4 text-muted-foreground/60' />
                    <Input
                      placeholder='Search settings...'
                      className='border-0 bg-transparent px-3 h-9 placeholder:text-muted-foreground/50'
                      style={{
                        WebkitAppearance: 'none',
                        MozAppearance: 'none',
                        appearance: 'none',
                        outline: 'none',
                        boxShadow: 'none'
                      }}
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                    />
                    <kbd className='hidden sm:inline-flex h-6 select-none items-center gap-1 rounded border border-primary/10 bg-muted px-2 font-mono text-[10px] font-medium text-muted-foreground opacity-100'>
                      <span className='text-xs'>⌘</span>
                      K
                    </kbd>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      <div className='mx-auto max-w-7xl px-6 py-8'>
        <AnimatePresence mode='wait'>
          <motion.div
            key={searchQuery}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3'
          >
            {filteredItems.map((item, index) => (
              <ServiceCard key={index} {...item} index={index} />
            ))}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

function ServiceCard({
  icon: Icon,
  title,
  description,
  href,
  color,
  index
}: {
  icon: React.ElementType;
  title: string;
  description: string;
  href: string;
  color: string;
  index: number;
}) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) {
      return;
    }
    const rect = cardRef.current.getBoundingClientRect();

    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setMousePosition({ x, y });
  };

  const springConfig = {
    type: 'spring' as const,
    stiffness: 300,
    damping: 30,
    mass: 1
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        delay: index * 0.1,
        ease: 'easeOut'
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className='group relative perspective-1000'
    >
      <motion.div
        className={cn('absolute inset-0 rounded-md opacity-0 transition-all duration-300', color)}
        animate={{
          opacity: isHovered ? 1 : 0,
          background: `
            radial-gradient(
              1200px circle at ${mousePosition.x}% ${mousePosition.y}%,
              var(--primary)/15%,
              transparent 40%
            )
          `
        }}
        transition={{ duration: 0.3 }}
      />

      <motion.a
        href={href}
        className='relative flex flex-col h-[190px] rounded-md border bg-card/50 backdrop-blur-sm p-6
                   overflow-hidden transition-colors duration-300 hover:border-primary/20'
        animate={{
          scale: isHovered ? 1.02 : 1,
          rotateX: isHovered ? 2 : 0,
          rotateY: isHovered ? 2 : 0
        }}
        transition={springConfig}
        style={{ transformStyle: 'preserve-3d' }}
      >
        {isHovered && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className='absolute inset-0 pointer-events-none'
          >
            {[...Array.from({ length: 3 })].map((_, i) => (
              <motion.div
                key={i}
                className='absolute size-2 rounded-full bg-primary/20'
                animate={{
                  x: [0, Math.random() * 100 - 50],
                  y: [0, Math.random() * -100],
                  opacity: [0.8, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: 'loop',
                  delay: i * 0.2,
                  ease: 'easeOut'
                }}
                style={{
                  left: `${50 + Math.random() * 20}%`,
                  top: `${70 + Math.random() * 20}%`
                }}
              />
            ))}
          </motion.div>
        )}

        <motion.div
          className={cn('rounded-xl p-3 w-fit transition-colors', isHovered ? 'bg-primary' : 'bg-primary/10')}
          animate={{
            scale: isHovered ? 1.1 : 1
          }}
          transition={springConfig}
        >
          <Icon className={cn('h-6 w-6 transition-colors', isHovered ? 'text-primary-foreground' : 'text-primary')} />
        </motion.div>

        <motion.div className='mt-4 space-y-1.5' animate={{ y: isHovered ? -2 : 0 }} transition={springConfig}>
          <div className='font-semibold text-primary tracking-tight text-[18px] leading-tight'>{title}</div>
          <p className='text-sm text-muted-foreground line-clamp-2'>{description}</p>
        </motion.div>

        <motion.div
          className='mt-3 flex items-center text-sm font-medium text-primary'
          initial={false}
          animate={{
            opacity: isHovered ? 1 : 0.5,
            x: isHovered ? 0 : 0
          }}
          transition={{ duration: 0.2 }}
        >
          <span className='mr-1'>Learn more</span>
          <motion.div animate={{ x: isHovered ? 5 : 0 }} transition={springConfig}>
            <ArrowRight className='h-4 w-4' />
          </motion.div>
        </motion.div>

        <motion.div
          className='absolute inset-x-0 bottom-0 h-[2px] bg-gradient-to-r from-transparent via-primary/50 to-transparent'
          initial={{ scaleX: 0 }}
          animate={{
            scaleX: isHovered ? 1 : 0,
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.3 }}
        />

        <motion.div
          className='absolute inset-0 pointer-events-none'
          initial={false}
          animate={
            isHovered
              ? {
                  background: `
              linear-gradient(to right, var(--primary) 2px, transparent 2px) 0 0 / 15px 2px no-repeat,
              linear-gradient(to right, var(--primary) 2px, transparent 2px) 0 100% / 15px 2px no-repeat,
              linear-gradient(to bottom, var(--primary) 2px, transparent 2px) 0 0 / 2px 15px no-repeat,
              linear-gradient(to bottom, var(--primary) 2px, transparent 2px) 100% 0 / 2px 15px no-repeat
            `,
                  opacity: 0.3
                }
              : {
                  background: 'none',
                  opacity: 0
                }
          }
          transition={{ duration: 0.3 }}
        />
      </motion.a>
    </motion.div>
  );
}
