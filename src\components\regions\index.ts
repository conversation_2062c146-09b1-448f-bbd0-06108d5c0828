export const showRegion = (region: number) => {
  switch (region) {
    case 0:
      return {
        label: 'New York',
        icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/united-states.svg'
      };
    case 1:
      return {
        label: 'Amsterdam',
        icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/netherlands.svg'
      };

    case 2:
      return {
        label: 'Frankfurt',
        icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/germany.svg'
      };
    case 3:
      return {
        label: 'London',
        icon: 'https:\/\/api.tradevps.net\/assets\/media\/flags\/united-kingdom.svg'
      };
    default:
      return { label: '', icon: '' };
  }
};
