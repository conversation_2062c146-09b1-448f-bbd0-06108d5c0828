'use client';

import type { ColumnDef } from '@tanstack/react-table';
import type { SetStateAction } from 'react';
import type { Invoice } from '@/types/invoice';
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  CreditCard,
  Download,
  Eye,
  Mail,
  MoreHorizontal,
  Plus,
  RefreshCcw,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useInvoicesList } from '@/services/api/user/billing/invoices';

enum InvoiceStatus {
  PENDING = 0,
  PAID = 1,
  IN_PROCESS = 2,
  CANCELED = 3,
  BLOCKED = 4
}

type InvoicesTableProps = {
  pageNumber: number;
  setPageNumber: (page: number) => void;
  perpage: number;
  setPerpageAction: (perPage: number) => void;
  search?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
  component?: React.ReactNode;
};

export function InvoicesTable({ pageNumber, perpage, setPerpageAction, component }: InvoicesTableProps) {
  const { data: invoicesData, isLoading } = useInvoicesList();

  const columns: ColumnDef<Invoice>[] = [
    {
      accessorKey: 'ref',
      meta: { label: 'Reference' },
      header: 'Reference',
      cell: ({ row }) => (
        <Link href={`/panel/account/billing/invoices/${row.getValue('ref')}`} className='text-primary hover:underline'>
          {row.getValue('ref')}
        </Link>
      )
    },
    {
      accessorKey: 'created_at',
      meta: { label: 'Date' },
      header: 'Date',
      cell: ({ row }) => {
        return new Date(row.getValue('created_at')).toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        });
      }
    },
    {
      accessorKey: 'discount',
      meta: { label: 'Discount' },
      header: 'Discount',
      cell: ({ row }) => {
        const discount = row.getValue('discount') as number | null;
        const discountCode = row.original.discount_code;

        if (!discount) {
          return <span className='text-gray-400'>N/A</span>;
        }

        return (
          <>
            $
            {discount.toFixed(2)}
            {discountCode && (
              <span className='px-2 py-0.5 bg-gray-100 rounded-full mx-0.5 text-gray-500 text-xs'>{discountCode}</span>
            )}
          </>
        );
      }
    },
    {
      accessorKey: 'amount',
      meta: { label: 'Amount' },
      header: 'Amount',
      cell: ({ row }) => `$${row.getValue<number>('amount').toFixed(2)}`
    },
    {
      accessorKey: 'tax',
      meta: { label: 'Tax' },
      header: 'Tax',
      cell: ({ row }) => `$${row.getValue<number>('tax').toFixed(2)}`
    },
    {
      accessorKey: 'total',
      meta: { label: 'Total' },
      header: 'Total',
      cell: ({ row }) => `$${row.getValue<number>('total').toFixed(2)}`
    },
    {
      accessorKey: 'status',
      meta: { label: 'Status' },
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as InvoiceStatus;
        const statusConfig = {
          [InvoiceStatus.PENDING]: {
            label: 'Pending',
            className: 'bg-yellow-100 text-yellow-800',
            icon: Clock
          },
          [InvoiceStatus.PAID]: {
            label: 'Paid',
            className: 'bg-green-100 text-green-800',
            icon: CheckCircle2
          },
          [InvoiceStatus.IN_PROCESS]: {
            label: 'In Process',
            className: 'bg-blue-100 text-blue-800',
            icon: RefreshCcw
          },
          [InvoiceStatus.CANCELED]: {
            label: 'Canceled',
            className: 'bg-red-100 text-red-800',
            icon: XCircle
          },
          [InvoiceStatus.BLOCKED]: {
            label: 'Blocked',
            className: 'bg-gray-100 text-gray-800',
            icon: AlertCircle
          }
        };

        const StatusIcon = statusConfig[status].icon;

        return (
          <span
            className={`w-full px-2 py-1 rounded-sm text-xs ${statusConfig[status].className} flex items-center justify-center gap-1`}
          >
            <StatusIcon className='size-3' />
            {statusConfig[status].label}
          </span>
        );
      }
    },
    {
      id: 'actions',
      meta: { label: 'Actions' },
      enableHiding: false,
      cell: ({ row }) => {
        const invoice = row.original;
        return (
          <div className='text-right flex items-center justify-end gap-2'>
            {invoice.status === InvoiceStatus.PENDING && (
              <Button variant='outline' size='sm'>
                <CreditCard className='w-4 h-4 mr-2' />
                Payment
              </Button>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' className='h-8 w-8 p-0'>
                  <span className='sr-only'>Open menu</span>
                  <MoreHorizontal className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => window.open(`/panel/account/billing/invoices/${invoice.ref}`, '_blank')}
                >
                  <Eye className='mr-2 h-4 w-4' />
                  View Invoice
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => window.open(`/api/invoices/${invoice.ref}/download`, '_blank')}>
                  <Download className='mr-2 h-4 w-4' />
                  Download PDF
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => window.open(`/api/invoices/${invoice.ref}/email`, '_blank')}>
                  <Mail className='mr-2 h-4 w-4' />
                  Send via Email
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      }
    }
  ];

  if (!invoicesData?.data?.length && !isLoading) {
    return (
      <div className='text-center py-8'>
        <p className='text-gray-500'>There is no invoice/transaction here for you.</p>
        <div className='flex items-center justify-center gap-3 mt-4'>
          <Link href='/panel/account/billing/charge'>
            <Button variant='outline'>Charge my account</Button>
          </Link>
          <Link href='/panel/account/billing/charge'>
            <Button className='gap-1.5 bg-primary/90 hover:bg-primary'>
              <Plus className='size-4' />
              Top Up Balance
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  function setPage(_value: SetStateAction<number>): void {
    throw new Error('Function not implemented.');
  }

  return (
    <div>
      <div className='flex justify-between items-center mb-4'>
        <div className='flex items-center gap-2'>{component}</div>
      </div>
      <DataTable
        columns={columns}
        dataTable={{
          data: invoicesData?.data || [],
          pagination: invoicesData?.pagination || {
            current_page: 1,
            first_page_url: '',
            from: 0,
            last_page: 1,
            last_page_url: '',
            next_page_url: null,
            per_page: perpage,
            prev_page_url: '',
            total: 0
          }
        }}
        isLoading={isLoading}
        pageNumber={pageNumber}
        perpage={perpage}
        setPerpage={(value: SetStateAction<number>) =>
          setPerpageAction(typeof value === 'function' ? value(perpage) : value)}
        setPageNumber={setPage}
        title=''
      />
    </div>
  );
}
