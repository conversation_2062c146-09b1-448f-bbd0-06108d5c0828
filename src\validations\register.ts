import { z } from 'zod';

export const registerValidationSchema = z
  .object({
    name: z.string().min(1, 'name is required'),

    email: z.string({ required_error: 'Email is required' }).email('Invalid email format'),

    password: z
      .string({ required_error: 'Password is required' })
      .regex(
        /^(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/,
        'Password must be at least 8 characters long and include at least one uppercase letter and one number'
      ),

    confirmPassword: z.string({ required_error: 'Please confirm your password' })
  })
  .refine(data => data.password === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords do not match'
  });

export type RegisterSchemaType = z.infer<typeof registerValidationSchema>;
