export enum CloudProvider {
  AZURE = 0,
  CTRL_CLOUD = 1,
  LINODE = 2,
  AWS = 3,
  GCP = 4,
  AZURE_ALT = 5,
  ALIBABA = 6,
  HETZNER = 7,
  BARE_METAL = 8
}

export const cloudProviderMap: Record<
  number,
  {
    label: string;
    description: string;
    icon?: string;
  }
> = {
  [CloudProvider.AZURE]: {
    label: 'Microsoft Azure',
    description: 'Microsoft Azure cloud provider',
    icon: '/img/companies/microsoft.svg'
  },
  [CloudProvider.CTRL_CLOUD]: {
    label: 'CtrlCloud',
    description: 'Ctrl Cloud provider',
    icon: '/img/companies/ctrlcloud.svg'
  },
  [CloudProvider.LINODE]: {
    label: 'Linode',
    description: 'Linode cloud provider'
  },
  [CloudProvider.AWS]: {
    label: 'AWS',
    description: 'Amazon Web Services (AWS) cloud provider',
    icon: '/img/companies/aws.svg'
  },
  [CloudProvider.GCP]: {
    label: 'GCP',
    description: 'Google Cloud Platform provider',
    icon: '/img/companies/google.svg'
  },
  [CloudProvider.AZURE_ALT]: {
    label: 'Microsoft Azure Alt',
    description: 'Microsoft Azure cloud provider (alternative)',
    icon: '/img/companies/microsoft.svg'
  },
  [CloudProvider.ALIBABA]: {
    label: 'Alibaba Cloud',
    description: 'Alibaba Cloud provider'
  },
  [CloudProvider.HETZNER]: {
    label: 'Hetzner',
    description: 'Hetzner cloud provider',
    icon: '/img/companies/hetzner.svg'
  },
  [CloudProvider.BARE_METAL]: {
    label: 'Bare Metal',
    description: 'Baremetal server provider'
  }
};

export enum Region {
  NEW_YORK = 0,
  AMSTERDAM = 1,
  FRANKFURT = 2,
  LONDON = 3
}

export const regionMap: Record<
  number,
  {
    label: string;
    description: string;
  }
> = {
  [Region.NEW_YORK]: { label: 'New York', description: 'New York, United States' },
  [Region.AMSTERDAM]: { label: 'Amsterdam', description: 'Amsterdam, Netherlands' },
  [Region.FRANKFURT]: { label: 'Frankfurt', description: 'Frankfurt, Germany' },
  [Region.LONDON]: { label: 'London', description: 'London, United Kingdom' }
};

export enum WindowsVersion {
  SERVER_2016 = 3,
  SERVER_2019 = 4,
  SERVER_2022 = 5,
  SERVER_2025 = 10,
  WINDOWS_11_PRO = 6,
  WINDOWS_11_PRO_N = 7,
  WINDOWS_10_PRO = 8,
  WINDOWS_10_PRO_N = 9
}

export const windowsVersionMap: Record<
  number,
  {
    label: string;
    description: string;
  }
> = {
  [WindowsVersion.SERVER_2016]: { label: 'Windows Server 2016', description: 'Windows Server 2016' },
  [WindowsVersion.SERVER_2019]: { label: 'Windows Server 2019', description: 'Windows Server 2019' },
  [WindowsVersion.SERVER_2022]: { label: 'Windows Server 2022', description: 'Windows Server 2022' },
  [WindowsVersion.SERVER_2025]: { label: 'Windows Server 2025', description: 'Windows Server 2025' },
  [WindowsVersion.WINDOWS_11_PRO]: { label: 'Windows 11 Pro', description: 'Windows 11 Pro' },
  [WindowsVersion.WINDOWS_11_PRO_N]: { label: 'Windows 11 Pro N', description: 'Windows 11 Pro N' },
  [WindowsVersion.WINDOWS_10_PRO]: { label: 'Windows 10 Pro', description: 'Windows 10 Pro' },
  [WindowsVersion.WINDOWS_10_PRO_N]: { label: 'Windows 10 Pro N', description: 'Windows 10 Pro N' }
};

export enum ServerStatus {
  PENDING = 0,
  CREATING = 1,
  DEPLOYING = 2,
  DELETING = 3,
  DELETED = 4,
  ERROR = 5,
  RUNNING = 6,
  STOPPED = 7,
  STARTING = 8,
  STOPPING = 9,
  RESTARTING = 10,
  UPDATING = 11,
  SCALING = 12
}

export const serverStatusMap: Record<
  number,
  {
    label: string;
    description: string;
    variant: 'primary_light' | 'warning_light' | 'success_light' | 'danger_light';
  }
> = {
  [ServerStatus.PENDING]: {
    label: 'Pending',
    description: 'Server creation has been requested but not yet started',
    variant: 'primary_light'
  },
  [ServerStatus.CREATING]: { label: 'Creating', description: 'Server is being created', variant: 'warning_light' },
  [ServerStatus.DEPLOYING]: {
    label: 'Deploying',
    description: 'Server is being deployed',
    variant: 'primary_light'
  },
  [ServerStatus.DELETING]: { label: 'Deleting', description: 'Server is being deleted', variant: 'warning_light' },
  [ServerStatus.DELETED]: { label: 'Deleted', description: 'Server has been deleted', variant: 'danger_light' },
  [ServerStatus.ERROR]: { label: 'Error', description: 'An error occurred with the server', variant: 'danger_light' },
  [ServerStatus.RUNNING]: {
    label: 'Running',
    description: 'Server is running and operational',
    variant: 'success_light'
  },
  [ServerStatus.STOPPED]: { label: 'Stopped', description: 'Server is stopped', variant: 'warning_light' },
  [ServerStatus.STARTING]: { label: 'Starting', description: 'Server is starting up', variant: 'warning_light' },
  [ServerStatus.STOPPING]: { label: 'Stopping', description: 'Server is shutting down', variant: 'warning_light' },
  [ServerStatus.RESTARTING]: { label: 'Restarting', description: 'Server is restarting', variant: 'warning_light' },
  [ServerStatus.UPDATING]: { label: 'Updating', description: 'Server is being updated', variant: 'warning_light' },
  [ServerStatus.SCALING]: { label: 'Scaling', description: 'Server is being scaled', variant: 'warning_light' }
};

export const statusItems = [
  {
    key: '0',
    value: 'Pending',
    icon: <p className='size-2.5 rounded-full bg-blue-500'></p>
  },
  { key: '1', value: 'Creating', icon: <p className='size-2.5 rounded-full bg-yellow-400'></p> },
  {
    key: '2',
    value: 'Deploying',
    icon: <p className='size-2.5 rounded-full bg-green-400 '></p>
  },
  {
    key: '3',
    value: 'Deleting',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '4',
    value: 'Deleted',
    icon: <p className='size-2.5 rounded-full bg-red-400'></p>
  },
  {
    key: '5',
    value: 'Error',
    icon: <p className='size-2.5 text-xs rounded-full bg-red-400'></p>
  },
  {
    key: '6',
    value: 'Running',
    icon: <p className='size-2.5 rounded-full bg-green-400 '></p>
  },
  {
    key: '7',
    value: 'Stoped',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '8',
    value: 'Starting',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '9',
    value: 'Stopping',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '10',
    value: 'Restarting',
    icon: <p className='size-2.5 rounded-full bg-yellow-400'></p>
  },
  {
    key: '11',
    value: 'Updating',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '12',
    value: 'Scaling',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  }
];
export enum DiskType {
  SSD = 0,
  NVME = 1
}

export const diskTypeMap: Record<
  number,
  {
    label: string;
    description: string;
  }
> = {
  [DiskType.SSD]: { label: 'SSD', description: 'Solid State Drive storage' },
  [DiskType.NVME]: { label: 'NVME', description: 'NVMe (Non-Volatile Memory Express) storage' }
};
