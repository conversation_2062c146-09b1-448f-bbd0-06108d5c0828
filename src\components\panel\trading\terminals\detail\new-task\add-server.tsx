import { useParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

const AddServer = () => {
  const { id } = useParams();

  const [serverName, setServerName] = useState('');
  const { mutate, isPending } = useNewTaskMutation({
    onSuccess: () => {
      toast.success('Server added successfully');
      setServerName('');
    },
    onError: (error: { message: any }) => {
      toast.error(error.message || 'Failed to add server');
    }
  });

  const handleSubmit = () => {
    if (!serverName.trim()) {
      toast.error('Please enter a server name');
      return;
    }

    mutate({
      params: { id: id as string },
      queryParams: {
        type: '8'
      },
      body: {
        payload: {
          server: serverName.trim()
        }
      }
    });
  };

  return (
    <div>
      <div className='w-full flex flex-col gap-1 py-12'>
        <div className='flex items-baseline flex-wrap lg:flex-nowrap gap-2.5'>
          <label htmlFor='serverName' className='form-label text-sm flex basis-1/4 items-center gap-1 max-w-32'>
            Server name
            {' '}
            <span className='text-xs text-red-500'>*</span>
          </label>
          <div className='w-full flex flex-col basis-3/4'>
            <Input
              placeholder='e.g. ICMarketsSC-Demo'
              id='serverName'
              value={serverName}
              onChange={e => setServerName(e.target.value)}
              disabled={isPending}
            />
            <Button onClick={handleSubmit} disabled={isPending} className='ml-auto mt-3'>
              {isPending ? 'Submitting...' : 'Submit'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddServer;
