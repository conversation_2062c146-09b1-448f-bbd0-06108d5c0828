import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IUserInfo } from 'types/user';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useUserUpdateMutate = (
  options?: Pick<IUseMutationFactoryProps<IUserInfo>, 'refetchQueries' | 'onSuccess'>
) => {
  return useMutationFactory<IUserInfo>({
    url: API_ROUTES.USER_PROFILE,
    method: 'PATCH',
    ...options
  });
};
