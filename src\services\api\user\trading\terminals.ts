import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { ITableQuery } from '@/types/table-query';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from 'configs/api-query-key';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';
import { SERVER } from 'libs/Axios';
import { usePaginationFactory } from '@/hooks/api/use-pagination-factory';

type IRegion = {
  id: string;
  name: string;
};
type ITerminalDetail = {
  id: string;
  name: string;
  region: string;
  server_name: string;
  login: string;
  status: number | null;
  created_at: string;
  updated_at: string;
};

export type ITerminal = {
  id: string;
  name: string;
  platform: string;
  region: string;
  login: string;
  status: 'active' | 'inactive' | 'pending';
  description: string;
};

type ApiResponse<T> = {
  ok: boolean;
  msg: string;
  data: T extends any[] ? T : T[];
};

// type ITerminalsResponse = {
//   data: ITerminal[];
//   pagination: {
//     current_page: number;
//     first_page_url: string;
//     from: number;
//     last_page: number;
//     last_page_url: string;
//     next_page_url: string | null;
//     per_page: number;
//     prev_page_url: string;
//     total: number;
//   };
// };

export const useRegionsFetch = () => {
  return useQuery({
    queryKey: [API_QUERY_KEY.TERMINALS_REGIONS],
    queryFn: async () => {
      const response = await SERVER.get<ApiResponse<IRegion[]>>(API_ROUTES.TERMINALS_REGIONS);
      return response.data;
    }
  });
};
export const useTerminalDetailFetch = (id: string) => {
  return useQuery({
    queryKey: [API_QUERY_KEY.TERMINALS_DETAIL, id],
    enabled: !!id,
    queryFn: async () => {
      const response = await SERVER.get<ITerminalDetail>(`${API_ROUTES.TERMINAL_DETAILS}/${id}`);
      return response.data;
    }
  });
};

// export const useListFetch = (query: ITableQuery, options?: { enabled: boolean }) => {
//   return useQuery({
//     queryKey: ['TERMINALS_LIST', query],
//     queryFn: async () => {
//       const response = await SERVER.get<ITerminalsResponse>(API_ROUTES.TERMINALS_LIST, {
//         params: query
//       });

//       return response;
//     },
//     // کش غیرفعال می‌شود
//     // مدت زمان نگهداری داده در کش (صفر یعنی هیچ کشی نداشته باش)

//     ...options
//   });
// };
export const useListFetch = (query: ITableQuery, options?: { enabled: boolean }) => {
  return usePaginationFactory({
    url: API_ROUTES.TERMINALS_LIST,
    page: query.page,
    perPage: query.per_page,
    queryKey: [API_QUERY_KEY.TERMINALS_LIST, JSON.stringify(query)],
    query,
    ...options
  });
};

export const useDeployMutation = (options?: Pick<IUseMutationFactoryProps<ITerminal>, 'onSuccess' | 'onError'>) => {
  return useMutationFactory<ITerminal>({
    url: API_ROUTES.DEPLOY_TERMINAL,
    method: 'POST',
    ...options
  });
};

export const useDestroyMutation = (options?: Pick<IUseMutationFactoryProps<ITerminal>, 'onSuccess' | 'onError'>) => {
  return useMutationFactory<ITerminal>({
    url: API_ROUTES.DESTROY_TERMINAL,
    method: 'DELETE',
    ...options
  });
};
