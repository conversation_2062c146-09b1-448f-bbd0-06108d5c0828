import Counter from '@/components/marketing/counter';

type StatProps = {
  number: number;
  suffix: string;
  text: string;
};

export default function Stats() {
  const stats: StatProps[] = [
    {
      number: 360,
      suffix: 'X',
      text: 'Faster delivery time on average for trading servers.'
    },
    {
      number: 63.7,
      suffix: '%',
      text: 'Cheaper than traditional servers on the market price.'
    },
    {
      number: 2.3,
      suffix: 'K+',
      text: 'Servers up and still running on the cloud infrastracture.'
    },
    {
      number: 17,
      suffix: 'K+',
      text: 'Tasks done on the trading servers, thanks to MetaPylot.'
    }
  ];

  return (
    <div className='max-w-6xl mx-auto px-4 sm:px-6'>
      <div className='max-w-sm mx-auto grid gap-12 sm:grid-cols-2 md:grid-cols-4 md:-mx-5 md:gap-0 items-start md:max-w-none'>
        {stats.map(stat => (
          <div key={stat.number} className='relative text-center md:px-5'>
            <h4 className='font-inter-tight text-2xl md:text-3xl font-bold tabular-nums mb-2'>
              <Counter number={stat.number} />
              {stat.suffix}
            </h4>
            <p className='text-sm text-zinc-500'>{stat.text}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
