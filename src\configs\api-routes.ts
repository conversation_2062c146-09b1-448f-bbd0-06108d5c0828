export const API_ROUTES = {
  AUTH_LOGIN: 'auth/login',
  AUTH_FORGET_PASSWORD: 'auth/forgot-password',
  AUTH_REGISTER: 'auth/register',
  AUTH_LOGOUT: 'auth/logout',
  AUTH_RESET_PASSWORD: 'auth/reset-password',
  USER_PROFILE: 'my/account/profile',
  ACCOUNT_RESET_PASSWORD: 'my/account/security/change-password',
  USER_PROFILE_SET_PASSWORD: 'user/profile/set-password',
  ADD_USER_TICKET: 'my/support/tickets',
  TICKET_DEPARTMENTS: 'my/support/tickets/departments',
  TICKET_STATUSES: 'my/support/tickets/statuses',
  SHOW_USER_TICKET: '/user/support/messages/{ticketId}',
  SHOW_USER_MESSAGES: '/my/support/tickets/messages/{ticketId}',
  CLOSE_TICKET: '/my/support/tickets/close/{id}',
  DELETE_TICKET: '/my/support/tickets/{id}',
  GET_TRADING_ACCOUNTS: 'user/trading/accounts',
  GET_TRADING_PLATFORMS: 'user/trading/accounts/platforms',
  ADD_TRADING_ACOUNT: 'user/trading/accounts',
  DELETETRADING_ACCOUNT: 'user/trading/accounts/{id}',
  TERMINALS_REGIONS: 'my/trading/terminals/regions',
  DEPLOY_TERMINAL: '/my/trading/terminals',
  DEPLOY_VPS: '/my/trading/vps',
  CHANG_VPS_PASSWORD: '/my/trading/vps/change-password/{vps_id}',
  MY_PROJECTS: 'my/projects',
  TERMINALS_LIST: '/my/trading/terminals',
  DESTROY_TERMINAL: '/my/trading/terminals/{id}',
  PROJECTS_CREATE: '/my/projects',
  USER_INVOICES: 'my/account/billing/invoices',
  USER_INVOICE_DETAIL: 'my/account/billing/invoices/{id}',
  TERMINAL_DETAILS: 'my/trading/terminals/show',
  TASK_LIST: 'my/trading/terminals/{id}/tasks',
  NEW_TASK: 'my/trading/terminals/{id}/tasks',
  BLOG_POSTS: 'https://blog.tradevps.net/wp-json/wp/v2/posts',
  USER_NOTIFICATIONS: 'my/account/notifications',
  MARK_NOTIFICATION_AS_READ: 'my/account/notifications/{id}/read',
  MARK_ALL_NOTIFICATION_AS_READ: 'my/account/notifications/read-all',
  ACCOUNT_OFFERS: 'my/account/offers',
  API_MANAGEMENT: '/my/account/tokens',
  API_REVOKE_TOKEN: '/my/account/tokens',
  CONTACT_FORM: '/contact-form',
  VPS_LIST: '/my/trading/vps',
  VPS_DETAILS: 'my/trading/vps/show',
  WINDOWS_SERVERS: 'my/trading/windows-servers',
  WINDOWS_SERVER_DETAIL: 'my/trading/windows-servers/show/{server_id}',
  WINDOWS_SERVER_DELETE: 'my/trading/windows-servers/{server_id}',
  WINDOWS_SERVER_CHANGE_PASSWORD: 'my/trading/windows-servers/change-password/{server_id}',
  WINDOWS_REGION: 'my/trading/windows-servers/regions',
  WINDOWS_SERVER_ACTION: 'my/trading/windows-servers/{server_id}/action'
} as const;
