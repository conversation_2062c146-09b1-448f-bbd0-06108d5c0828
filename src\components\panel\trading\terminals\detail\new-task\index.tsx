import { AlignJustify, Earth, FileUp, Images, KeyRound, Play, SatelliteDish } from 'lucide-react';
import * as React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AddServer from './add-server';
import AllowWebRequest from './allow-web-request';
import AutoTrade from './auto-trade';
import { CaptureScreenshot } from './capture-screenshot';
import InstallExpertTask from './install-expert';
import LoginAccount from './login-account';

const NewTask = () => {
  const [taskType, setTaskType] = React.useState<number>(1);
  const taskItems = [
    { id: 1, title: 'Install Expert', description: 'Upload and enable expert advisor (EA)', icon: <FileUp /> },
    { id: 2, title: 'Capture Screenshot', description: 'See whats happening on your terminal', icon: <Images /> },
    { id: 3, title: 'Add Server', description: 'Configure terminal servers list', icon: <SatelliteDish /> },
    { id: 4, title: 'Login Account', description: 'Securely log in to a trading account', icon: <KeyRound /> },
    { id: 5, title: 'AutoTrade', description: 'On/Off Auto Trading (Algo Trading)', icon: <Play /> },
    { id: 6, title: 'Allow Web Request', description: 'Add URL to MetaTrader whitelist', icon: <Earth /> },
    { id: 7, title: 'MarketWatch Show All', description: 'Show all symbols on MarketWatch', icon: <AlignJustify /> }
  ];
  const returnTaskTypeComponent = () => {
    switch (taskType) {
      case 1:
        return <InstallExpertTask />;
      case 2:
        return <CaptureScreenshot />;
      case 3:
        return <AddServer />;
      case 4:
        return <LoginAccount />;
      case 5:
        return <AutoTrade />;
      case 6:
        return <AllowWebRequest />;
      default:
        return null;
    }
  };

  const handleTaskSelection = (id: number) => {
    setTaskType(id);
  };

  const handleKeyPress = (event: React.KeyboardEvent, id: number) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTaskSelection(id);
    }
  };

  const activeStyle = 'text text-blue-400';
  return (
    <div className='flex flex-col lg:flex-row gap-5'>
      <div className='w-full lg:w-1/3 mb-4 lg:mb-0'>
        <Card className='w-full'>
          <CardHeader>
            <CardTitle className='text-lg'>Task Type</CardTitle>
          </CardHeader>
          <hr />
          <CardContent>
            {taskItems?.map(item => (
              <div key={item?.id}>
                <div
                  role='button'
                  tabIndex={0}
                  onClick={() => handleTaskSelection(item?.id)}
                  onKeyDown={e => handleKeyPress(e, item?.id)}
                  className='flex items-center my-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary'
                >
                  <div className='rounded-lg bg-primary/10 p-2 mr-3'>{item.icon}</div>
                  <div className={item?.id === taskType ? activeStyle : ''}>
                    <h1 className='text'>{item?.title}</h1>
                    <p className='text text-sm'>{item?.description}</p>
                  </div>
                </div>
                <Separator />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className='w-full lg:w-2/3'>
        <Card className='w-full'>
          <CardHeader>
            <CardTitle className='text-lg'>Options</CardTitle>
          </CardHeader>
          <hr />
          <CardContent>{returnTaskTypeComponent()}</CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NewTask;
