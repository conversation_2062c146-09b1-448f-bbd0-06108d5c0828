import { getTranslations } from 'next-intl/server';
import Link from 'next/link';

type ICookiesProps = {
  params: Promise<{ slug: string; locale: string }>;
};

export async function generateMetadata(props: ICookiesProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Cookies'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}
const CookiesPolicy = () => {
  return (
    <main className='text-sm/6 font-semibold text-gray-600 before:pt-24 relative before:absolute before:inset-0 pt-24 before:h-80 before:pointer-events-none before:bg-gradient-to-b before:from-zinc-100 before:-z-10'>
      <div className='py-12 md:py-20 px-4 sm:px-24  '>
        <section className='mt-4' aria-labelledby='cookies-policy-heading'>
          <h1 className='text-2xl font-bold'>Cookies Policy</h1>
          <p className='text-sm text-gray-500'>Effective Date: 02/12/2025</p>

          <p className='mt-4'>
            This Cookies Policy explains how TradeVPS ("we," "us," or "our") uses cookies and similar technologies on
            our website
            {' '}
            <Link href='https://tradevps.net/' className='text-blue-600 underline'>
              tradevps.net
            </Link>
            {' '}
            (the "Site"). This policy should be read in conjunction with our
            {' '}
            <Link href='/legal/privacy-policy' className='text-blue-600 underline'>
              Privacy Policy
            </Link>
            .
          </p>
        </section>

        <h2 className='mt-6 text-xl font-bold'>What are Cookies?</h2>
        <p className='mt-2'>
          Cookies are small text files that are placed on your device when you visit a website. They are widely used to
          make websites work more efficiently and provide information to website owners.
        </p>

        <h2 className='mt-6 text-xl font-bold'>Types of Cookies We Use</h2>
        <ul className='list-disc pl-6 mt-2'>
          <li>
            <span className='font-bold'>Strictly Necessary Cookies:</span>
            {' '}
            Essential for site navigation and secure
            areas. These cookies do not require consent.
          </li>
          <li>
            <span className='font-bold'>Performance Cookies:</span>
            {' '}
            Collect data on site usage to improve performance
            and user experience.
          </li>
          <li>
            <span className='font-bold'>Functionality Cookies:</span>
            {' '}
            Remember user choices and enhance features like
            language preferences.
          </li>
          <li>
            <span className='font-bold'>Targeting/Advertising Cookies:</span>
            {' '}
            Used to display relevant ads and limit ad
            frequency.
          </li>
        </ul>

        <h2 className='mt-6 text-xl font-bold'>Third-Party Cookies</h2>
        <p className='mt-2'>
          We may allow third-party service providers to place cookies on our Site for analytics and advertising. We do
          not control these cookies, so please review their privacy policies for more details.
        </p>

        <h2 className='mt-6 text-xl font-bold'>Managing Cookies</h2>
        <p className='mt-2'>
          You can manage cookies via browser settings. Note that disabling cookies may affect website functionality.
        </p>

        <h2 className='mt-6 text-xl font-bold'>Changes to this Cookies Policy</h2>
        <p className='mt-2'>We may update this policy from time to time. Any changes will be posted here.</p>

        <h2 className='mt-6 text-xl font-bold'>Contact Us</h2>
        <p className='mt-2'>For questions, contact us at legal[at]tradevps[dot]net.</p>
      </div>
    </main>
  );
};

export default CookiesPolicy;
