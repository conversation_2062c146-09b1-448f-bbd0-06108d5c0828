'use client';
import type { ColumnDef } from '@tanstack/react-table';
import type { DateRange } from 'react-day-picker';
import type { ITerminal } from '@/services/api/user/trading/terminals';
import { ChevronsUpDown, EllipsisVertical, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/badge';
import { DateTime } from '@/components/date-time';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog/delete-dialog';
import { terminalStatusMap } from '@/components/panel/trading/terminals/table-statuses';
import DataTable from '@/components/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useSearchQuery } from '@/hooks/search-query';
import useTableSearchQueries from '@/hooks/table-query';
import { useProjectsPaginationList } from '@/services/api/user/projects';
import { useDestroyMutation } from '@/services/api/user/trading/terminals';
import ProjectsTableFiltersComponent from './table-component';
import { projectTypeMap } from './type-list';

export default function ProjectsList() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [filterValue, setFilterValue] = useState('');
  const [per_page, setPerpage] = useState(15);
  const [type, setType] = useState<string[]>([]);
  const [columnSort, setColumnSort] = useState<{ sort_by: string; sort_direction: 'asc' | 'desc' } | null>(null);
  const search = useSearchQuery(filterValue, 500);
  const [date_from, setdateFrom] = useState<string>();
  const [date_to, setDateTo] = useState<string>();
  const [status, setSelectedStatuses] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>(undefined);
  const [subject, setSubject] = useState<string>('');
  // const [deleteId, setDeleteId] = useState<string>('');
  const [Opendialog, setOpenDialog] = useState(false);
  const queries = useTableSearchQueries({
    page,
    per_page,
    search,
    date_from,
    date_to,
    status: status.join(','),
    type: type.join(','),
    sort_by: columnSort?.sort_by,
    sort_direction: columnSort?.sort_direction
  });
  const {
    data: projects,
    isLoading,
    refetch
  } = useProjectsPaginationList(
    {
      ...queries
    },
    {
      enabled: true
    }
  );

  const handleSortAction = (sortType: string) => {
    if (columnSort?.sort_by === sortType) {
      const direction = columnSort.sort_direction === 'asc' ? 'desc' : 'asc';
      setColumnSort({ sort_by: sortType, sort_direction: direction });
    } else {
      setColumnSort({ sort_by: sortType, sort_direction: 'desc' });
    }
  };

  const destroyMutation = useDestroyMutation({
    onSuccess: () => {
      toast.success('Terminal deleted successfully');
      setOpenDialog(false);
      setSubject('');
      // setDeleteId('');
      refetch();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to delete terminal');
    }
  });

  const handleDeleteClick = (terminal: ITerminal) => {
    setSubject(terminal?.name);
    // setDeleteId(terminal?.id);
    setOpenDialog(true);
  };

  const handleDeleteConfirm = () => {
    setOpenDialog(false);
  };

  const columns: ColumnDef<ITerminal>[] = [
    {
      accessorKey: 'name',
      meta: {
        label: 'Name'
      },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex cursor-pointer items-center w-fit'
          onClick={() => handleSortAction('name')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSortAction('name');
            }
          }}
        >
          Name & Description
          <ChevronsUpDown className='ml-2 h-3 w-3' />
        </div>
      ),
      cell: ({ row }) => {
        const description = row.original.description || '';
        const truncatedDescription = description.length > 90 ? `${description.substring(0, 87)}...` : description;

        return (
          <div className='flex flex-col space-y-1'>
            <div className='font-medium'>{row.original.name}</div>
            {description && <div className='text-sm text-muted-foreground line-clamp-3'>{truncatedDescription}</div>}
          </div>
        );
      }
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('type') as number;
        const { label, variant } = projectTypeMap[type] ?? { label: 'Unknown', variant: 'default' };
        return (
          <div>
            <Badge value={label} variant={variant} />
          </div>
        );
      }
    },
    {
      accessorKey: 'status',
      meta: { label: 'status' },
      header: () => (
        <div
          role='button'
          tabIndex={0}
          className='flex cursor-pointer w-fit items-center'
          onClick={() => handleSortAction('status')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSortAction('status');
            }
          }}
        >
          Status
          <ChevronsUpDown className='ml-2 h-3 w-3' />
        </div>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as number;
        const { label, variant } = terminalStatusMap[status] ?? { label: 'Unknown', variant: 'default' };
        return (
          <div>
            <Badge value={label} variant={variant} />
          </div>
        );
      }
    },
    {
      accessorKey: 'url',
      header: 'Url'
    },
    {
      accessorKey: 'soft_limit',

      header: 'Soft limit',
      cell: ({ row }) => {
        const softLimit = row.getValue('soft_limit') as string;
        return (
          <div className='min-w-[50px]'>
            {' '}
            <span className='text-muted-foreground'>$</span>
            {softLimit !== undefined ? Number(softLimit).toFixed(2) : '__'}
          </div>
        );
      }
      // cell: ({ row }) => {
      //   const url = row.getValue('url') as string;
      //   return <div className='text-[13px]'>{url}</div>;
      // }
    },
    {
      accessorKey: 'hard_limit',
      header: 'Hard limit',
      cell: ({ row }) => {
        const hard_limit = row.getValue('hard_limit') as string;
        return (
          <div className='min-w-[50px]'>
            {' '}
            <span className='text-muted-foreground'>$</span>
            {hard_limit !== undefined ? Number(hard_limit).toFixed(2) : '__'}
          </div>
        );
      }
      // cell: ({ row }) => {
      //   const url = row.getValue('url') as string;
      //   return <div className='text-[13px]'>{url}</div>;
      // }
    },
    {
      accessorKey: 'created_at',
      header: 'Create At',
      cell: ({ row }) => {
        const date = new Date(row?.getValue('created_at'));
        return <DateTime className='text-[13px] t' value={date.toISOString()} />;
      }
    },

    {
      accessorKey: 'updated_at',
      header: 'Update At',
      cell: ({ row }) => {
        const date = new Date(row?.getValue('updated_at'));
        return <DateTime className='text-[13px] ' value={date.toISOString()} />;
      }
    },

    {
      accessorKey: 'Actions',
      enableHiding: false,

      cell: ({ row }) => {
        const project = row?.original;

        return (
          <div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size='sm'
                  variant='outline'
                  className='border-blue-300 flex items-center text-[13px] dark:border-input'
                >
                  <EllipsisVertical />
                  Actions
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(project.name)}>
                  Copy Name
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(project.description)}>
                  Copy Description
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className='text-red-600'
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleDeleteClick(project);
                  }}
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete Project
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      }
    }
  ];

  useEffect(() => {
    if (selectedDateRange === undefined) {
      setdateFrom('');
      setDateTo('');
    }
    if (selectedDateRange?.from && selectedDateRange?.to) {
      const fromDate = selectedDateRange.from.toISOString().split('T')[0];
      const toDate = selectedDateRange.to.toISOString().split('T')[0];
      setdateFrom(fromDate);
      setDateTo(toDate);
    }
  }, [selectedDateRange]);

  return (
    <div className='mt-5'>
      <div className='flex gap-2 mb-5 items-end justify-between'>
        <div>
          <h4 className='text-primary'>Projects</h4>
          <p className='text-muted-foreground'>Central Hub for Personal Customization</p>
        </div>

        <div className='flex gap-2'>
          <Link href={PAGE_ROUTES.PANEL_TRADING_TERMINALS_DEPLOY}>
            <Button variant='outline' size='sm' className='w-[80px]'>
              Export
            </Button>
          </Link>
          <Button
            onClick={() => {
              router.push('/panel/projects/create');
            }}
            variant='blue_primary'
            className='cursor-pointer hover:shadow-lg transition-shadow duration-200 text-sm'
            size='sm'
          >
            <Plus className='size-3.5' />
            Create
          </Button>
        </div>
      </div>
      <DataTable
        pageNumber={page}
        setPageNumber={setPage}
        perpage={per_page}
        setPerpage={setPerpage}
        columns={columns}
        dataTable={{
          data: Array.isArray(projects) ? projects : projects?.data || [],
          pagination: projects?.pagination || {
            current_page: 1,
            first_page_url: '',
            from: 0,
            last_page: 1,
            last_page_url: '',
            next_page_url: null,
            per_page,
            prev_page_url: '',
            total: 0
          }
        }}
        isLoading={isLoading}
        title='Terminals'
        component={(
          <ProjectsTableFiltersComponent
            setFilterValue={setFilterValue}
            filterValue={filterValue}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            selectedStatus={status}
            setSelectedStatus={setSelectedStatuses}
            type={type}
            setType={setType}
          />
        )}
      />
      <DeleteConfirmDialog
        open={Opendialog}
        setOpen={setOpenDialog}
        confirmSubject={subject}
        onDeleteFunction={handleDeleteConfirm}
        isLoading={destroyMutation?.isPending}
      />
    </div>
  );
}
