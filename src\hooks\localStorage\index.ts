import { useState } from 'react';

function useLocalStorage<T>(key: string, initialValue: T) {
  // State برای نگهداری مقدار
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // بررسی مقدار موجود در localStorage
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch {
      return initialValue;
    }
  });

  // تابع برای تنظیم مقدار جدید
  const setValue = (value: T | ((val: T) => T)) => {
    const valueToStore = typeof value === 'function' ? (value as (val: T) => T)(storedValue) : value;
    setStoredValue(valueToStore);
    // ذخیره مقدار جدید در localStorage
    window.localStorage.setItem(key, JSON.stringify(valueToStore));
  };
  const removeValue = () => {
    window.localStorage.removeItem(key);
    setStoredValue(initialValue); // بازنشانی به مقدار اولیه
  };
  return [storedValue, setValue, removeValue] as const;
}

export default useLocalStorage;
