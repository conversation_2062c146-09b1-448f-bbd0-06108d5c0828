'use client';
import type { APIError } from '@tradevpsnet/client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Label } from '@/components/ui/label';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { useForgetpasswordClientMutate } from '@/services/api/auth/use-forget-password-mutate';
import { useResetpasswordClientMutate } from '@/services/api/auth/use-reset-password-mutation';

const emailSchema = z.string().email('Please enter a valid email address');
const passwordResetSchema = z
  .object({
    email: z.string().email('Invalid email address'),
    verificationCode: z.string().length(6, 'Verification code must be 6 digits'),
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/\d/, 'Password must contain at least one number'),
    confirmPassword: z.string()
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Passwords don\'t match',
    path: ['confirmPassword']
  });

export default function ForgetPasswordPage() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const router = useRouter();
  const [otp, setOtp] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    verificationCode: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const forgetPasswordMutation = useForgetpasswordClientMutate({
    onSuccess: () => {
      setSubmitted(true);
      setFormData(prev => ({ ...prev, email }));
      toast.success('Verification code sent to your email');
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Sending Email failed. Please try again.');
    }
  });

  const resetPasswordMutation = useResetpasswordClientMutate({
    onSuccess: () => {
      toast.success('Password reset successfully!');
      router.push(PAGE_ROUTES.LOGIN);
      setEmail('');
      setOtp('');
      setFormData({
        email: '',
        verificationCode: '',
        newPassword: '',
        confirmPassword: ''
      });
      setSubmitted(false);
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Reset Password failed. Please try again.');
    }
  });

  const handleEmailSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    const result = emailSchema.safeParse(email);
    if (!result.success) {
      const issue = result.error.errors[0];
      setError(issue?.message || 'Invalid input');
      return;
    }
    setError('');
    forgetPasswordMutation.mutate({ email });
  };

  const handleOtpChange = (value: string) => {
    setOtp(value);
    setFormData(prev => ({ ...prev, verificationCode: value }));
  };

  const handleResendCode = () => {
    setSubmitted(false);
    setOtp('');
    toast.info('Enter your email again to receive a new code');
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = passwordResetSchema.safeParse({
        ...formData,
        verificationCode: otp
      });

      if (!result.success) {
        const issue = result.error.errors[0];
        setPasswordError(issue?.message || 'Invalid input');
        return;
      }

      setPasswordError('');

      await resetPasswordMutation.mutateAsync({
        email: formData.email,
        code: otp,
        password: formData.newPassword,
        password_confirmation: formData.confirmPassword
      });
    } catch (error) {
      console.error('Password reset error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className='flex flex-col items-center justify-center'>
      <Card className='w-[470] max-w-md p-10'>
        {!submitted ? (
          <div className='space-y-6'>
            <CardHeader>
              <CardTitle className='text-lg'>Your Email</CardTitle>
              <CardDescription>Enter your email to reset your password</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleEmailSubmit}>
                <div className='space-y-10'>
                  <div className='space-y-2'>
                    <Label htmlFor='email'>Email</Label>
                    <Input
                      id='email'
                      type='email'
                      placeholder='<EMAIL>'
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                    />
                    {error && <p className='text-sm text-red-600 mt-1'>{error}</p>}
                  </div>
                  <div className='flex justify-end'>
                    <Button type='submit' disabled={forgetPasswordMutation.isPending}>
                      {forgetPasswordMutation.isPending ? 'Sending...' : 'Continue'}
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </div>
        ) : (
          <div className='space-y-6'>
            <CardHeader>
              <CardTitle className='text-lg'>Reset Password</CardTitle>
              <CardDescription>Enter your new password below</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordSubmit} className='space-y-4'>
                <input type='hidden' name='email' value={formData.email} />
                <div className='space-y-2 w-full'>
                  <Label>Verification Code</Label>
                  <InputOTP maxLength={6} value={otp} onChange={handleOtpChange}>
                    <InputOTPGroup>
                      {[...Array.from({ length: 6 })].map((_, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <InputOTPSlot key={index} index={index} />
                      ))}
                    </InputOTPGroup>
                  </InputOTP>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='newPassword'>New Password</Label>
                  <Input
                    id='newPassword'
                    name='newPassword'
                    type='password'
                    placeholder='Enter new password'
                    value={formData.newPassword}
                    onChange={handlePasswordChange}
                    disabled={isSubmitting}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='confirmPassword'>Confirm Password</Label>
                  <Input
                    id='confirmPassword'
                    name='confirmPassword'
                    type='password'
                    placeholder='Confirm new password'
                    value={formData.confirmPassword}
                    onChange={handlePasswordChange}
                    disabled={isSubmitting}
                  />
                </div>
                {passwordError && <p className='text-sm text-red-600 mt-1'>{passwordError}</p>}
                <div className='flex items-center justify-between'>
                  <Button variant='link' type='button' onClick={handleResendCode} className=' text-sm'>
                    Resend code
                  </Button>
                  <Button type='submit' disabled={isSubmitting}>
                    {isSubmitting ? 'Resetting...' : 'Reset Password'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </div>
        )}
      </Card>
    </div>
  );
}
