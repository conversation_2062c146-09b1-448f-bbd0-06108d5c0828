import * as yup from 'yup';

export const AddTransactionValidationSchema = yup.object().shape({
  name: yup.string(),
  login: yup.string().required('لطفا شماره حساب/لاگین را وارد نمایید'),
  trading_password: yup.string().required('لطفا رمز معاملاتی را وارد نمایید'),
  server: yup.string().required('لطفا سرور معاماتی را وارد نمایید'),
  readonly_password: yup.string()
});

export type AddTicketSchemaType = yup.InferType<typeof AddTransactionValidationSchema>;

export const AddTerminalValidationSchema = yup.object().shape({
  name: yup.string(),

  trading_account_id: yup.string()
});
