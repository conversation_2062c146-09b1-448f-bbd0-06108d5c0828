import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export type IContactFormData = {
  name: string;
  email: string;
  phone?: string;
  message: string;
};

export type IContactFormResponse = {
  ok: boolean;
  msg: string;
};

export const useContactFormMutation = (
  options?: Pick<IUseMutationFactoryProps<IContactFormResponse>, 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IContactFormResponse>({
    url: API_ROUTES.CONTACT_FORM,
    method: 'POST',
    successToast: true,
    ...options
  });
};
