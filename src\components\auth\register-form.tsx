'use client';

import type { APIError, IRegisterParams, IRegisterResponse } from '@tradevpsnet/client';
import type { RegisterSchemaType } from 'validations/register';
import { zodResolver } from '@hookform/resolvers/zod';
import { useUser } from 'hooks/user/user';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { FcGoogle } from 'react-icons/fc';
import { useRegisterClientMutate } from 'services/api/auth/use-register-mutate';
import { toast } from 'sonner';
import { registerValidationSchema } from 'validations/register';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { cn } from '@/lib/utils';

export default function RegisterForm({ className, ...props }: React.ComponentPropsWithoutRef<'form'>) {
  const router = useRouter();
  const user = useUser();

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<RegisterSchemaType>({
    resolver: zodResolver(registerValidationSchema)
  });

  const registerApi = useRegisterClientMutate({
    onSuccess: (data: IRegisterResponse) => {
      user.login(data?.token);
      toast('Welcome to TradeVPS, redirect to panel...', {
        action: {
          label: 'Go to Panel',
          onClick: () => router.replace(PAGE_ROUTES.PANEL)
        }
      });
      router.replace(PAGE_ROUTES.PANEL);
    },
    onError: (error: APIError) => {
      toast.error(error?.message || 'Register failed. Please try again.');
    }
  });

  const onSubmit = (data: IRegisterParams) => {
    registerApi.mutate(data);
  };

  return (
    <form className={cn('flex flex-col gap-6', className)} {...props} onSubmit={handleSubmit(onSubmit)}>
      <div className='flex flex-col items-start gap-2 text-start'>
        <h1 className='text-2xl font-bold'>Welcome to TradeVPS</h1>
        <p className='text-sm text-muted-foreground'>Enter your details below to create your account.</p>
      </div>
      <div className='grid gap-6'>
        <div className='grid gap-2'>
          <Label htmlFor='firstName'>Name</Label>
          <Input id='firstName' type='text' placeholder='John' {...register('name')} />
          {errors.name && <p className='text-red-500 text-sm'>{errors.name.message}</p>}
        </div>
        <div className='grid gap-2'>
          <Label htmlFor='email'>Email</Label>
          <Input id='email' type='email' placeholder='<EMAIL>' {...register('email')} />
          {errors.email && <p className='text-red-500 text-sm'>{errors.email.message}</p>}
        </div>
        <div className='grid gap-2'>
          <Label htmlFor='password'>Password</Label>
          <Input id='password' type='password' {...register('password')} />
          {errors.password && <p className='text-red-500 text-sm'>{errors.password.message}</p>}
        </div>
        <div className='grid gap-2'>
          <Label htmlFor='confirmPassword'>Confirm Password</Label>
          <Input id='confirmPassword' type='password' {...register('confirmPassword')} />
          {errors.confirmPassword && <p className='text-red-500 text-sm'>{errors.confirmPassword.message}</p>}
        </div>
        <Button type='submit' className='w-full' isLoading={registerApi?.isPending}>
          Sign up
        </Button>
        <div className='relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border'>
          <span className='relative z-10 bg-background px-2 text-muted-foreground'>Or Continue With</span>
        </div>
        <Link
          href='https://api.tradevps.net/v1/auth/social/google'
          className='inline-flex items-center cursor-pointer justify-center gap-2 py-2 whitespace-nowrap rounded-md text-sm font-medium  outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] w-full border border-input bg-background shadow-xs hover:bg-accent'
        >
          <FcGoogle className='size-5' />
          Login with Google
        </Link>
      </div>
      <div className='text-center text-sm'>
        Have an account?
        {' '}
        <Link href={PAGE_ROUTES.LOGIN} className='underline underline-offset-4'>
          Sign in
        </Link>
      </div>
    </form>
  );
}
