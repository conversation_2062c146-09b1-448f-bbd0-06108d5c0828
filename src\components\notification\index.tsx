'use client';
import { formatDistanceToNow } from 'date-fns';
import { Bell, BellOff, EyeIcon, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useMarkAllNotificationsAsReadMutation,
  useMarkAsReadMutation,
  useNotificationsList
} from '@/services/api/user/notifications';

type NotificationPopoverProps = {
  onClose: () => void;
};
type Notification = {
  id: string;
  message: string;
  read: boolean;
};

const NotificationPopover = ({ onClose }: NotificationPopoverProps) => {
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all');
  const [page, setPage] = useState(1);
  const [per_page] = useState(5);
  const [accessNextPage, setAccessNextPage] = useState(true);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [readMarkId, setReadMarkId] = useState<string>('');
  const [allNotifications, setAllNotifications] = useState<Notification[]>([]);
  const [unreadMessage] = useState(false);

  const {
    data: notificationsData,
    isLoading,
    refetch,
    isFetching
  } = useNotificationsList(page, per_page, activeTab === 'unread');

  useEffect(() => {
    if (notificationsData?.data) {
      if (page === 1) {
        setAllNotifications(notificationsData.data);
      } else {
        setAllNotifications(prev => [...prev, ...notificationsData.data]);
      }
    }
  }, [notificationsData?.data, page]);

  useEffect(() => {
    const handleScroll = () => {
      if (!scrollRef.current) {
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isAtBottom && !isFetching && accessNextPage) {
        setPage(prev => prev + 1);
      }
    };

    const el = scrollRef.current;
    if (el) {
      el.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (el) {
        el.removeEventListener('scroll', handleScroll);
      }
    };
  }, [isFetching, accessNextPage]);

  useEffect(() => {
    setPage(1);
  }, [unreadMessage]);

  useEffect(() => {
    if (notificationsData?.pagination) {
      const { current_page, total, per_page } = notificationsData.pagination;
      setAccessNextPage(current_page * per_page < total);
    }
  }, [notificationsData]);

  const markAllAsRead = useMarkAllNotificationsAsReadMutation({
    onSuccess: () => {
      refetch();
      toast.success('All notifications marked as read');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to mark notifications as read');
    }
  });
  const markAsReadMutation = useMarkAsReadMutation({
    onSuccess: () => {
      refetch();
      toast.success('Notification marked as read');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to mark notification as read');
    }
  });
  const handleMarkAsRead = (id: string) => {
    setReadMarkId(id);
    markAsReadMutation.mutate({ params: { id } });
  };
  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate({ params: { id: 1 } });
  };
  return (
    <div className='w-[400px] rounded-lg shadow-lg bg-background border'>
      <div className='flex items-center justify-between p-4 border-b'>
        <h3 className='text-primary'>Notifications</h3>
        <Button
          onClick={onClose}
          className='rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700'
          variant='ghost'
          size='icon'
        >
          <X className='h-5 w-5' />
        </Button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          setActiveTab(value as 'all' | 'unread');
          setPage(1);
        }}
      >
        <TabsList className='w-full rounded-none border-b'>
          <TabsTrigger value='all' className='w-full'>
            All
          </TabsTrigger>
          <TabsTrigger value='unread' className='w-full'>
            Unread
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <div ref={scrollRef} className='h-[400px] overflow-y-auto'>
        {isLoading && page === 1 ? (
          <div className='p-4 space-y-4'>
            {[...Array.from({ length: 5 })].map((_, i) => (
              <div key={i} className='flex items-start gap-3'>
                <Skeleton className='h-10 w-10 rounded-full' />
                <div className='space-y-2 flex-1'>
                  <Skeleton className='h-4 w-[80%]' />
                  <Skeleton className='h-3 w-[60%]' />
                </div>
              </div>
            ))}
          </div>
        ) : allNotifications?.length === 0 ? (
          <div className='p-8 text-center'>
            <Bell className='h-10 w-10 mx-auto text-muted-foreground mb-3' />
            <p className='text-muted-foreground'>No notifications found</p>
          </div>
        ) : (
          <div className='divide-y'>
            {allNotifications?.map((notification: any) => (
              <div key={notification.id} className='p-4 hover:bg-muted/50'>
                <div className='flex items-start gap-3'>
                  <div
                    className={`p-2 rounded-full ${notification.read_at ? 'bg-muted' : 'bg-primary/10 text-primary'}`}
                  >
                    {notification.read_at ? <BellOff size={16} /> : <Bell size={16} />}
                  </div>
                  <div className='flex-1'>
                    <p className='font-medium'>{notification.data?.name}</p>
                    <p className='text-sm text-muted-foreground'>
                      {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                    </p>
                    <p className='text-sm mt-1'>{notification.data?.message}</p>
                    <div className='flex items-center'>
                      <button
                        type='button'
                        className='text-blue-600 dark:text-blue-300 me-5 text-[15px] cursor-pointer hover:text-blue-600 bg-transparent p-0 border-none focus:outline-none focus:ring-0 underline underline-offset-4 decoration-dotted decoration-blue-500'
                      >
                        view
                      </button>
                      {/* <Button size='sm' variant='outline' className='me-3'>view</Button> */}
                      {!notification?.read_at && (
                        <Button
                          onClick={() => handleMarkAsRead(notification.id)}
                          disabled={markAsReadMutation.isPending}
                          variant='table'
                          className='w-fit text-sm'
                          size='sm'
                        >
                          {readMarkId === notification?.id && markAsReadMutation.isPending ? (
                            <CyrcleSvg />
                          ) : (
                            <EyeIcon size='sm' />
                          )}
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isFetching && page > 1 && (
              <div className='p-4 flex justify-center'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className='p-3 border-t'>
        <Button variant='ghost' className='w-full' onClick={handleMarkAllAsRead} disabled={markAllAsRead.isPending}>
          <EyeIcon className='mr-2 h-4 w-4' />
          {markAllAsRead.isPending ? 'Processing...' : 'Mark all as read'}
        </Button>
      </div>
    </div>
  );
};

export default NotificationPopover;
