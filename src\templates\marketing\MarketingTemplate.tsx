import Footer from '@/components/marketing/layout/footer';
import Header from '@/components/marketing/layout/header';
import { Toaster } from '@/components/ui/sonner';

export const MarketingTemplate = (props: { children: React.ReactNode; breadcrumb?: React.ReactNode }) => {
  return (
    <div className='min-h-screen flex flex-col relative'>
      <Header />
      {/* Add padding-top to account for fixed header */}
      <div className='pt-[var(--header-height)] flex flex-col w-full'>
        {props.breadcrumb && (
          <div className='breadcrumb-wrapper w-full'>
            <div className='breadcrumb-container w-full'>
              <div className='container mx-auto px-4 sm:px-6 lg:px-8 py-2'>{props.breadcrumb}</div>
            </div>
          </div>
        )}
        <main className='flex-grow'>{props.children}</main>
      </div>
      <Toaster />
      <Footer />
    </div>
  );
};
