import Icon from './Icon';

export default function Logo(props: { className?: string; showText?: boolean; inverted?: boolean }) {
  const { className, showText = true, inverted = false } = props;
  const iconClass = className || 'size-6 fill-white';

  return (
    <div className='flex items-center justify-start gap-x-2'>
      <div className='flex size-8 items-center justify-center rounded-md bg-primary dark:bg-secondary text-primary-foreground'>
        <Icon className={iconClass} />
      </div>
      {showText && (
        <div className='flex-col items-center justify-center'>
          <h5 className={`text-md font-bold ${inverted ? 'text-white' : 'text-primary'}`}>TradeVPS</h5>
          <p className={`font-light text-xs ${inverted ? 'text-white/80' : 'text-zinc-600 dark:text-zinc-200'}`}>
            Creative Cloud for Trading
          </p>
        </div>
      )}
    </div>
  );
}
