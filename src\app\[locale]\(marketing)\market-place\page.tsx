'use client';
import React from 'react';
import openAi from '@/public/img/platforms/openai-chatgpt-logo-icon-free-png.webp';
import SquareCard from './cards/square-card';

import FeaturedTabs from './featured-tabs';

const MaketPlace = () => {
  return (
    <>
      <h1 className='mb-2  text-2xl'>Models for your every use case</h1>
      <p className='text-xs'>Try, test, and deploy from a wide range of model types, sizes, and specializations.</p>
      <div className='grid md:grid-cols-3 gap-3 mt-4'>
        <SquareCard
          producer='by Azure OpenAI Service'
          type='Model'
          title='OpenAI GPT-4o'
          image={openAi}
          description='OpenAI s most advanced multimodal model in the gpt-4o family. Can handle both text and image inputs.'
        />
        <SquareCard
          producer='by Azure OpenAI Service'
          type='Model'
          title='OpenAI GPT-4o'
          image={openAi}
          description='OpenAI s most advanced multimodal model in the gpt-4o family. Can handle both text and image inputs.'
        />
        <SquareCard
          producer='by Azure OpenAI Service'
          type='Model'
          title='OpenAI GPT-4o'
          image={openAi}
          description='OpenAI s most advanced multimodal model in the gpt-4o family. Can handle both text and image inputs.'
        />
      </div>
      <FeaturedTabs />
    </>
  );
};

export default MaketPlace;
