'use client';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import RiseLoader from 'react-spinners/RiseLoader';
import { getFromStorage } from '@/utils/storage';

export const LoginAuthenticationLayout = ({ children }: React.PropsWithChildren) => {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    const tokenStore = getFromStorage('token');
    if (pathname) {
      if (tokenStore?.state?.token) {
        if (pathname.startsWith('/auth')) {
          router.replace('/panel');
          setIsLoading(false);
          setIsLoading(false);
        } else {
          setIsMounted(true);
          setIsLoading(false);
        }
      } else {
        setIsMounted(true);
        setIsLoading(false);
      }
    }
  }, [pathname, router]);
  // وقتی کامپوننت mount نشده است، چیزی رندر نمی‌شود
  // if (!isMounted) return null;

  if (isLoading || !isMounted) {
    return (
      <div className='flex items-center bg-gray-200 justify-center h-screen'>
        <RiseLoader
          color='blue'
          loading={isLoading || !isMounted}
          // cssOverride={override}
          size={15}
          aria-label='Loading Spinner'
          data-testid='loader'
        />
      </div>
    );
  }

  return <>{children}</>;
};
