import { getTranslations } from 'next-intl/server';

type IProfilePageProps = {
  params: Promise<{ locale: string }>;
  children: React.ReactNode;
};

export async function generateMetadata(props: IProfilePageProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Profile'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default function Layout({ children }: IProfilePageProps) {
  return <>{children}</>;
}
