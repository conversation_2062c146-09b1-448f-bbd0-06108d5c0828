import type { APIError, IResetPasswordParams, IResetPasswordResponse } from '@tradevpsnet/client';
import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useResetPasswordMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.AUTH_RESET_PASSWORD,
    method: 'POST',
    ...options
  });
};
export const useResetPasswordAccountMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.ACCOUNT_RESET_PASSWORD,
    method: 'POST',
    ...options
  });
};

export const useResetpasswordClientMutate = (options?: {
  onSuccess?: (data: IResetPasswordResponse) => void;
  onError?: (error: APIError) => void;
}) => {
  const client = new Client('', process.env.NEXT_PUBLIC_SERVER_URL);

  return useMutation<IResetPasswordResponse, APIError, IResetPasswordParams>({
    mutationFn: (params: IResetPasswordParams) => client.auth.reset_password(params),
    ...options
  });
};
