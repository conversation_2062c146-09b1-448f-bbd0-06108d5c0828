{"RootLayout": {"home_link": "Home", "about_link": "About", "counter_link": "Counter", "portfolio_link": "Portfolio", "sign_in_link": "Sign in", "sign_up_link": "Sign up"}, "BaseTemplate": {"description": "Starter code for your Nextjs Boilerplate with Tailwind CSS", "made_with": "Made with <author></author>."}, "Index": {"meta_title": "TradeVPS - Home", "meta_description": "Next js Boilerplate is the perfect starter code for your project. Build your React application with the Next.js framework.", "sponsors_title": "Sponsors"}, "Counter": {"meta_title": "Counter", "meta_description": "An example of DB operation", "loading_counter": "Loading counter...", "security_powered_by": "Security, bot detection and rate limiting powered by"}, "CounterForm": {"presentation": "The counter is stored in the database and incremented by the value you provide.", "label_increment": "Increment by", "button_increment": "Increment"}, "CurrentCount": {"count": "Count: {count}"}, "About": {"meta_title": "About", "meta_description": "About page description", "about_paragraph": "Welcome to our About page! We are a team of passionate individuals dedicated to creating amazing software."}, "Terms": {"meta_title": "Terms & Conditions", "meta_description": "Terms page description"}, "Pricing": {"meta_title": "Pricing", "meta_description": "Gimme money asap"}, "Portfolio": {"meta_title": "Portfolio", "meta_description": "Welcome to my portfolio page!", "presentation": "Welcome to my portfolio page! Here you will find a carefully curated collection of my work and accomplishments. Through this portfolio, I'm to showcase my expertise, creativity, and the value I can bring to your projects.", "portfolio_name": "Portfolio {name}", "error_reporting_powered_by": "Error reporting powered by", "coverage_powered_by": "Code coverage powered by"}, "PortfolioSlug": {"meta_title": "Portfolio {slug}", "meta_description": "Portfolio {slug} description", "header": "Portfolio {slug}", "content": "Created a set of promotional materials and branding elements for a corporate event. Crafted a visually unified theme, encompassing a logo, posters, banners, and digital assets. Integrated the client's brand identity while infusing it with a contemporary and innovative approach. Garnered favorable responses from event attendees, resulting in a successful event with heightened participant engagement and increased brand visibility.", "log_management_powered_by": "Log management powered by"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "Dashboard": {"meta_title": "Dashboard", "hello_message": "Hello {email}!", "alternative_message": "Want to build your SaaS faster using the same stack? Try <url></url>."}, "UserProfile": {"meta_title": "User Profile"}, "DashboardLayout": {"dashboard_link": "Dashboard", "user_profile_link": "Manage your account", "sign_out": "Sign out"}, "Projects": {"create": {"meta_title": "Create Project", "meta_description": "Create a new project"}}, "Account": {"meta_title": "My Account", "meta_description": "Access and manage your TradeVPS account settings, subscriptions, and preferences with ease.", "meta_keywords": "TradeVPS, My Account, Account <PERSON><PERSON><PERSON>, Manage Account, Trading Preferences"}, "Billing": {"meta_title": "Billing & Payments", "meta_description": "Manage your billing details, payment methods, and view invoices", "meta_keywords": "billing, payments, invoices, payment methods, subscription"}, "Privacy": {"meta_title": "Privacy", "meta_description": "Privacy page description"}, "DMCA": {"meta_title": "DMCA", "meta_description": "DMCA page description"}, "Cookies": {"meta_title": "Cookies", "meta_description": "Cookies page description"}, "Blog": {"meta_title": "Blogs", "meta_description": "Blog page descriptions"}, "NotFound": {"meta_title": "Page Not Found - 404", "meta_description": "The page you're looking for doesn't exist or has been moved.", "title": "Page not found", "description": "Sorry, we couldn't find the page you're looking for.", "back_home": "Go back home", "contact_support": "Contact support"}, "Forget-Password": {"meta_description": "Forget-Password page", "meta_title": "Forget-Password"}, "Careers": {"meta_description": "An example of DB operation", "meta_keywords": "TradeVPS, My Account, Account <PERSON><PERSON><PERSON>, Manage Account, Trading Preferences", "meta_title": "Careers"}, "BrandBook": {"meta_title": "Brand Book - Design System & Guidelines", "meta_description": "Explore our comprehensive brand guidelines, design system, and visual language documentation.", "title": "Brand Book", "description": "A comprehensive guide to our brand identity, design system, and visual language"}, "Profile": {"meta_title": "Profile - TradeVPS", "meta_description": "Manage your TradeVPS account profile and settings"}, "WindowsServer": {"meta_title": "Windows Server"}, "DeployServer": {"meta_title": "DeployServer", "meta_description": "Deploy Server page"}, "WindowsServerDetail": {"meta_title": "WindowsServerDetail", "meta_description": "Windows Server Detail"}}