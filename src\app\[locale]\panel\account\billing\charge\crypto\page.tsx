'use client';

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const formSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  currency: z.string().min(1, 'Please select a currency')
});

export default function CryptoChargePage() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: '',
      currency: ''
    }
  });

  function onSubmit(_values: z.infer<typeof formSchema>) {
    // console.log(values);
  }

  return (
    <div className='max-w-2xl mx-auto py-8'>
      <div className='mb-6'>
        <Link
          href='/panel/account/billing'
          className='text-sm text-muted-foreground hover:text-primary flex items-center gap-1'
        >
          <ArrowLeft className='size-4' />
          Back to Billing
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Add Funds with Cryptocurrency</CardTitle>
          <CardDescription>Securely add funds to your account using cryptocurrency</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount (USD)</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter amount' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='currency'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Cryptocurrency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select a cryptocurrency' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='btc'>Bitcoin (BTC)</SelectItem>
                        <SelectItem value='eth'>Ethereum (ETH)</SelectItem>
                        <SelectItem value='usdt'>Tether (USDT)</SelectItem>
                        <SelectItem value='usdc'>USD Coin (USDC)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type='submit' className='w-full'>
                Generate Payment Address
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
