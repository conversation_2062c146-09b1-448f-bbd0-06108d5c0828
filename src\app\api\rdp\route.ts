import type { NextRequest } from 'next/server';
import fs from 'node:fs/promises';
import { NextResponse } from 'next/server';
import { buildRdpFile } from '@/lib/buildrdp-file';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Add the sanitizeFilename function
function sanitizeFilename(filename: string) {
  // eslint-disable-next-line no-control-regex
  return filename.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_').replace(/^\.|\.$|^\s+|\s+$/g, '_');
}

export async function POST(req: NextRequest) {
  try {
    const { address, username } = await req.json();

    if (!address || !username) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const filePath = await buildRdpFile({ address, username });
    const fileBuffer = await fs.readFile(filePath);

    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/x-rdp',
        'Content-Disposition': `attachment; filename="${sanitizeFilename(address)}.rdp"`
      }
    });
  } catch (err) {
    console.error('❌ Failed to build RDP file:', err);
    return NextResponse.json({ error: `Failed to build RDP file: ${err}` }, { status: 500 });
  }
}
