import React from 'react';
import { Pagination, PaginationContent, PaginationItem } from '@/components/ui/pagination';

type CustomPaginationProps = {
  pageCount: number;
  pageIndex: number;
  setPageIndex: (page: number) => void;
};

export default function PaginationAction({ pageCount, pageIndex, setPageIndex }: CustomPaginationProps) {
  const paginationArray: React.ReactNode[] = [];
  const pageCountSet = Array.from({ length: pageCount }, (_, i) => i + 1);
  let finalIndexes: (number | string)[] = [];

  switch (pageCount > 5) {
    case true:
      switch (pageIndex) {
        case 1:
          if (pageIndex === 1) {
            finalIndexes = [...pageCountSet.slice(pageIndex - 1, pageIndex + 2), '...', pageCount];
          }
          break;

        case pageCountSet[pageCountSet.length - 1]:
          if (pageIndex === pageCountSet[pageCountSet.length - 1]) {
            finalIndexes = [1, '...', ...pageCountSet.slice(pageIndex - 4, pageIndex)];
          }
          break;

        case pageCountSet[1]:
          if (pageIndex === pageCountSet[1]) {
            finalIndexes = [...pageCountSet.slice(0, pageIndex + 1), '...', pageCount];
          }
          break;

        case pageCountSet[pageCountSet.length - 2]:
          if (pageIndex === pageCountSet[pageCountSet.length - 2]) {
            finalIndexes = [1, '...', ...pageCountSet.slice(pageIndex - 3, pageIndex + 1)];
          }
          break;

        default:
          finalIndexes = [1, '...', ...pageCountSet.slice(pageIndex - 1, pageIndex + 1), '...', pageCount];
          break;
      }
      break;

    default:
      for (let i = 1; i <= pageCount; i++) {
        paginationArray.push(
          <PaginationItem key={`page-${i}`}>
            <button
              type='button'
              onClick={() => setPageIndex(i)}
              className={`bg-${pageIndex === i ? 'gray-200' : ''} text-${pageIndex === i ? 'primary' : ''} inline-flex cursor-pointer items-center justify-center gap-2 whitespace-nowrap font-medium ring-0 focus:ring-0 ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md size-8 p-0 text-[14px] text-muted-foreground`}
            >
              {i}
            </button>
          </PaginationItem>
        );
      }
      break;
  }

  finalIndexes.forEach((item, index) => {
    paginationArray.push(
      <div key={`pagination-${item}-${index}`}>
        <PaginationItem>
          {typeof item === 'number' ? (
            <button
              type='button'
              className={`${pageIndex === item ? 'bg-gray-300 dark:bg-gray-50 dark:text-gray-600 dark:hover:bg-accent dark:hover:text-accent-foreground ' : ''}} text-${pageIndex === item ? 'primary' : ''} inline-flex cursor-pointer items-center justify-center gap-2 whitespace-nowrap font-medium ring-0 focus:ring-0 ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md size-8 p-0 text-[14px] text-muted-foreground`}
              onClick={() => setPageIndex(item)}
            >
              {item}
            </button>
          ) : (
            <button type='button'>...</button>
          )}
        </PaginationItem>
      </div>
    );
  });

  return (
    <Pagination>
      <PaginationContent>
        <button
          type='button'
          disabled={pageIndex === 1}
          className='mr-1 cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-0 focus:ring-0 ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md size-8 p-0 text-[14px] rtl:transform rtl:rotate-180'
          onClick={() => setPageIndex(pageIndex - 1)}
        >
          <p className='text-primary/200 text-center'>{'<'}</p>
        </button>
      </PaginationContent>
      <PaginationContent>{paginationArray}</PaginationContent>
      <PaginationContent>
        <button
          type='button'
          disabled={pageIndex === pageCount}
          className='mr-1 cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-0 focus:ring-0 ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md size-8 p-0 text-[14px] rtl:transform rtl:rotate-180'
          onClick={() => setPageIndex(pageIndex + 1)}
        >
          <p className='text-primary/200 text-center'>{'>'}</p>
        </button>
      </PaginationContent>
    </Pagination>
  );
}
