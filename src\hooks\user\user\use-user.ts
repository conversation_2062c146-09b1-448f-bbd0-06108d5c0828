import type { IUseUser } from './use-user.type';
import { useQueryClient } from '@tanstack/react-query';
import { useUserInfoFetch } from 'services/api/user/use-user-info-fetch';
import { useUserStore } from 'stores/user';

export function useUser(): IUseUser {
  const queryClient = useQueryClient();
  const token = useUserStore(state => state.token);
  const setToken = useUserStore(state => state.setToken);
  const removeToken = useUserStore(state => state.removeToken);
  const fetchUserInfo = useUserInfoFetch();

  const isLogin = !!token;

  const isMySelf = (id?: number | string) => {
    if (!id) {
      return false;
    }
    return fetchUserInfo?.data?.id === +id;
  };

  const login = (token: string, refreshToken?: string) => {
    setToken(token, refreshToken);
    // Refetch user info after login
    queryClient.invalidateQueries({ queryKey: ['userInfo'] });
  };

  const logout = () => {
    removeToken();
    queryClient.removeQueries();
  };

  return {
    isLogin,
    login,
    logout,
    isMySelf,
    token,
    ...fetchUserInfo,
    // Override the data property from fetchUserInfo with a default value if it's null/undefined
    data: fetchUserInfo.data || {
      id: '',
      avatar: '',
      email: '',
      name: '',
      plan: null,
      plan_expires_at: null
    }
  };
}
