import Image from 'next/image';
import * as React from 'react';
import { Card } from '@/components/ui/card';

type CardPropsType = {
  title: string;
  image: any;
  producer?: string;
  description: string;
  type: string;
};
const SquareCard = ({ title, image, producer, description, type }: CardPropsType) => {
  return (
    <Card className='p-3 text-center relative '>
      <span className='absolute  border px-3 rounded-lg w-fit text-gray-500  text-sm top-3 right-2'>{type}</span>
      <Image className='mx-auto mb-0' src={image} alt='' width={60} height={40} />
      <h1 className='font-bold'>{title}</h1>
      <p className='text-xs text-gray-600'>{producer}</p>
      <p className='text-xs text-gray-600'>{description}</p>
    </Card>
  );
};

export default SquareCard;
