'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { CyrcleSvg } from '@/components/cyrcle-svg';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useCahgeVpsPasswordMutation } from '@/services/api/user/trading/vps';
// 👇 Add the fields to the schema
const formSchema = z
  .object({
    account_password: z.string().min(1, 'Account password is required'),
    new_password: z
      .string()
      .min(8, 'Password must be at least 8 characters long')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/\d/, 'Password must contain at least one number')
      .regex(/[@#$!%*?&]/, 'Password must contain at least one special character (@, #, $, !, %, *, ?, &)'),
    confirm_password: z.string().min(1, 'Please confirm your password')
  })
  .refine(data => data.new_password === data.confirm_password, {
    message: 'Passwords do not match',
    path: ['confirm_password'] // attach error to the confirm_password field
  });

type FormValues = z.infer<typeof formSchema>;

export default function CahngeVpsPassword() {
  // const router = useRouter();

  // const queryClient = useQueryClient();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      account_password: '',
      new_password: '',
      confirm_password: ''
    }
  });
  const params = useParams();
  const id = params.id as string;
  const { mutate, isPending } = useCahgeVpsPasswordMutation({
    onSuccess: () => {
      toast.success('Project created successfully');
    },
    onError: () => {
      toast.error('Failed to create project');
    }
  });

  const onSubmit = (values: FormValues) => {
    const submissionData = {
      // ...values,
      // url: values.url || undefined,
      // description: values.description || undefined
      account_password: values.account_password,
      new_password: values.new_password
    };

    mutate({ body: submissionData, params: { vps_id: id } });
  };

  return (
    <div className='flex w-full md:w-1/2 flex-col mx-auto'>
      <Card>
        <CardHeader className='pb-2'>
          <CardTitle className='text-xl font-semibold'>Change VPS Password</CardTitle>
        </CardHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className='grid gap-6'>
              <div className='grid'>
                {/* Project Name */}
                <FormField
                  control={form.control}
                  name='account_password'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex'>
                        <FormLabel className='mb-0'>Account </FormLabel>
                        <span className='text-destructive -mt-1 ml-1'>*</span>
                      </div>
                      <FormControl className='-mt-1'>
                        <Input
                          placeholder='Enter account password'
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='grid'>
                {/* Project Name */}
                <FormField
                  control={form.control}
                  name='new_password'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex'>
                        <FormLabel>New Password</FormLabel>
                        <span className='text-destructive -mt-1 ml-1'>*</span>
                      </div>
                      <FormControl className='-mt-1'>
                        <Input
                          placeholder='Enter new password'
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='grid'>
                {/* Project Name */}
                <FormField
                  control={form.control}
                  name='confirm_password'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex'>
                        <FormLabel>Confirm Password</FormLabel>
                        <span className='text-destructive -mt-1 ml-1'>*</span>
                      </div>
                      <FormControl className='-mt-1'>
                        <Input
                          placeholder='Enter confirm password'
                          disabled={isPending}
                          className='rounded-lg'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='flex w-auto justify-end'>
                <Button variant='blue_primary' type='submit' disabled={isPending}>
                  {isPending ? (
                    <div className='flex items-center gap-2'>
                      <CyrcleSvg />
                      Cahnging...
                    </div>
                  ) : (
                    'Cahnge Password'
                  )}
                </Button>
              </div>
            </CardContent>
          </form>
        </Form>
      </Card>
    </div>
  );
}
