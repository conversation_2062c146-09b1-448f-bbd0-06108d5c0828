import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { toast } from 'sonner';
import FileUploader from '@/components/drop-zone';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useNewTaskMutation } from '@/services/api/user/trading/tasks';

const InstallExpertTask = () => {
  const { id } = useParams();
  const { mutate, isPending } = useNewTaskMutation();
  const [eaFile, setEaFile] = useState<File | null>(null);
  const [settingsFile, setSettingsFile] = useState<File | null>(null);
  const [timeFrame, setTimeFrame] = useState<string | null>(null);
  const [chart, setChart] = useState('');
  const [autoTrading, setAutoTrading] = useState(false);
  const [allowDllImports, setAllowDllImports] = useState(false);

  const TimeframeOptions = [
    { id: 1, value: 'M1' },
    { id: 2, value: 'M5' },
    { id: 3, value: 'M15' },
    { id: 4, value: 'M30' },
    { id: 5, value: 'H1' },
    { id: 6, value: 'H4' },
    { id: 7, value: 'D' }
  ] as const;

  const handleSubmit = async () => {
    if (!eaFile) {
      toast.error('Please upload an Expert Advisor file');
      return;
    }

    if (!timeFrame) {
      toast.error('Please select a timeframe');
      return;
    }
    const formData = new FormData();
    formData.append('payload[eafile]', eaFile as File);
    if (settingsFile) {
      formData.append('payload[setfile]', settingsFile as File);
    }

    formData.append('payload[chart]', chart);
    formData.append('payload[timeframe]', timeFrame);
    formData.append('payload[auto_trading]', String(autoTrading));
    formData.append('payload[allow_dll_imports]', String(allowDllImports));

    mutate(
      {
        params: { id: id as string },
        queryParams: { type: '2' },
        body: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      },
      {
        onSuccess: () => toast.success('Expert Advisor installed successfully'),
        onError: error => toast.error(error.message || 'Failed to install Expert Advisor')
      }
    );
  };

  return (
    <>
      <div className='flex flex-col md:flex-row mb-10 justify-between gap-5'>
        <div className='w-full'>
          <h1 className='font-bold mb-1'>Expert Advisor (EA)</h1>
          <div className='w-full'>
            <FileUploader
              file={eaFile}
              setFile={(file: File | null) => {
                setEaFile(file);
              }}
              title='Upload a file or drag and drop'
              describe='MQL4, MQL5, EX4, EX5 up to 10MB'
              fileType='ea'
              variant='file'
            />
          </div>
        </div>
        <div className='w-full'>
          <h1 className='font-bold mb-1'>Settings file</h1>
          <FileUploader
            file={settingsFile}
            setFile={(file: File | null) => {
              setSettingsFile(file);
            }}
            title='Upload a file or drag and drop'
            describe='.set, .ini up to 1MB'
            fileType='settings'
          />
        </div>
      </div>
      {/* Rest of your component remains the same */}
      <div className='flex flex-col md:flex-row mt-5 items-center'>
        <div className='w-full md:w-1/3'>
          <span className='text text-sm'>Chart</span>
          {' '}
          <span className='text-xs text-red-500'>*</span>
        </div>
        <div className='w-full md:w-2/3 lg:w-1/3'>
          <Input placeholder='e.g. EURUSD' value={chart} onChange={e => setChart(e.target.value)} />
        </div>
      </div>
      <div className='flex flex-col md:flex-row items-center mt-4'>
        <div className='w-full md:w-1/3'>
          <span className='text text-sm'>Timeframe</span>
          {' '}
          <span className='text-xs text-red-500'>*</span>
        </div>
        <div className='flex gap-1'>
          {TimeframeOptions.map(({ id, value }) => {
            return (
              <TooltipProvider key={id}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => setTimeFrame(value)}
                      className='cursor-pointer mr-2'
                      variant={value === timeFrame ? 'primary' : 'primary_outline'}
                    >
                      {value}
                    </Button>
                  </TooltipTrigger>
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </div>
      </div>
      <div className='flex mt-5 justify-between md:justify-start'>
        <div className='w-1/2 md:w-1/3 md:mr-0'>
          <span className='text text-sm'>Allow Auto Trading</span>
          {' '}
          <Badge variant='primay_light'>
            <span className='text text-xs'>Algo Trading</span>
          </Badge>
        </div>
        <Switch checked={autoTrading} onCheckedChange={setAutoTrading} />
      </div>
      <div className='flex mt-5 justify-between md:justify-start'>
        <div className='w-1/2 md:w-1/3 md:mr-0'>
          <span className='text text-sm'>Allow DLL Imports</span>
          {' '}
          <Badge variant='danger_light'>
            <span className='text text-xs'>CAREFUL</span>
          </Badge>
        </div>
        <Switch checked={allowDllImports} onCheckedChange={setAllowDllImports} />
      </div>
      <div className='full-width'>
        <Button onClick={handleSubmit} disabled={isPending} className='ml-auto mt-3 justify-end flex'>
          {isPending ? 'Submitting...' : 'Submit'}
        </Button>
      </div>
    </>
  );
};

export default InstallExpertTask;
