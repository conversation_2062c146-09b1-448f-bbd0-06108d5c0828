import { setRequestLocale } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import Logo from '@/components/logo';
import { IMAGE_URL } from '@/configs/image-url';
import { LoginAuthenticationLayout } from '@/layouts/login-authentication';
import { AuthTemplate } from '@/templates/auth/AuthTemplate';

export default async function CenteredLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <LoginAuthenticationLayout>
      <AuthTemplate>
        <div className='grid min-h-svh lg:grid-cols-2'>
          <div className='flex flex-col gap-4 p-6 md:p-10'>
            <div className='flex justify-center gap-2 md:justify-start'>
              <Link href='/' className='flex items-center gap-2'>
                <Logo showText={true} />
              </Link>
            </div>
            <div className='flex flex-1 items-center justify-center'>
              <div className='w-full max-w-xs'>{props.children}</div>
            </div>
          </div>
          <div className='relative hidden bg-muted lg:block'>
            <Image
              src={IMAGE_URL.WINDOWS_SERVER_POSTER}
              width={630}
              height={865}
              alt='Image'
              className='absolute inset-0 h-full w-full object-cover object-top dark:brightness-[0.2] dark:grayscale'
            />
          </div>
        </div>
      </AuthTemplate>
    </LoginAuthenticationLayout>
  );
}
