import type { BadgeVariants } from '@/components/badge/badge.type';
import { Badge } from '../../../../ui/badge';

export enum enumStatus {
  CREATED = 0,
  PAYMENT = 1,
  QUEUE = 2,
  DEPLOYING = 3,
  INSTALLING = 4,
  RUNNING = 5,
  FINISHED = 6,
  CANCELED = 7,
  BLOCKED = 8,
  FAILED = 9,
  DELETING = 10,
  DESTROYED = 11
}
export const terminalStatusMap: Record<number, { label: string; variant: BadgeVariants }> = {
  [enumStatus.CREATED]: { label: 'Created', variant: 'stone_light' },
  [enumStatus.PAYMENT]: { label: 'Payment', variant: 'slate_light' },
  [enumStatus.QUEUE]: { label: 'Queue', variant: 'fuchsia_light' },
  [enumStatus.DEPLOYING]: { label: 'Deploying', variant: 'sky_light' },
  [enumStatus.INSTALLING]: { label: 'Installing', variant: 'primary_light' },
  [enumStatus.RUNNING]: { label: 'Running', variant: 'emerald_light' },
  [enumStatus.FINISHED]: { label: 'Finished', variant: 'neutral_light' },
  [enumStatus.CANCELED]: { label: 'Canceled', variant: 'warning_light' },
  [enumStatus.BLOCKED]: { label: 'Blocked', variant: 'rose_light' },
  [enumStatus.FAILED]: { label: 'Failed', variant: 'danger_light' },
  [enumStatus.DELETING]: { label: 'Deleting', variant: 'amber_light' },
  [enumStatus.DESTROYED]: { label: 'Destroyed', variant: 'gray_light' }
};

export const statusBadge = (status: number) => {
  switch (status) {
    case 1:
      return (
        <Badge variant='slate_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-slate-500 me-1.5'></span>
          Payment
        </Badge>
      );
    case 7:
      return (
        <Badge variant='warning_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-yellow-500 me-1.5'></span>
          Canceled
        </Badge>
      );
    case 0:
      return (
        <Badge variant='stone_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-stone-500 me-1.5'></span>
          Created
        </Badge>
      );
    case 2:
      return (
        <Badge variant='fuchsia_light' className='h-[23px] w-[100px]'>
          <span className='size-2 rounded-full bg-fuchsia-500 me-1.5'></span>
          Queue
        </Badge>
      );
    case 3:
      return (
        <Badge variant='sky_light' className='w-[100px] h-[23px] '>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-sky-400 opacity-75' />
            <span className='relative size-1 h-full w-full  inline-flex size-2 rounded-full bg-sky-500' />
          </span>
          Deploying
        </Badge>
      );
    case 4:
      return (
        <Badge variant='primay_light' className='w-[100px] h-[23px]'>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-blue-400 opacity-75' />
            <span className='relative size-1 h-full w-full  inline-flex size-2 rounded-full bg-blue-500' />
          </span>
          Installing
        </Badge>
      );
    case 5:
      return (
        <Badge variant='emerald_light' className='w-[100px] h-[23px] flex items-center gap-2'>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-emerald-400 opacity-75' />
            <span className='relative size-1 h-full w-full  inline-flex size-2 rounded-full bg-emerald-500' />
          </span>
          <span>Running</span>
        </Badge>
      );
    case 6:
      return (
        <Badge variant='neutral_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-neutral-500 me-1.5'></span>
          Finished
        </Badge>
      );

    case 8:
      return (
        <Badge variant='rose_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-rose-500 me-1.5'></span>
          Blocked
        </Badge>
      );
    case 9:
      return (
        <Badge variant='danger_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-red-500 me-1.5 '></span>
          Failed
        </Badge>
      );
    case 10:
      return (
        <Badge variant='amber_light' className='w-[100px] h-[23px] w-[100px] '>
          <span className='relative flex justify-center items-center size-2'>
            <span className=' absolut size-2 -mt-1  -ms-1 h-full w-full animate-ping rounded-full bg-amber-400 opacity-75' />
            <span className='relative size-1 h-full w-full  inline-flex size-2 rounded-full bg-amber-500' />
          </span>
          Deleting
        </Badge>
      );
    case 11:
      return (
        <Badge variant='gray_light' className='h-[23px] w-[100px] '>
          <span className='size-2 rounded-full bg-gray-500 me-1.5'></span>
          Destroyed
        </Badge>
      );
    default:
      return (
        <Badge variant='default' className='h-[23px] w-[100px] '>
          <span className='size-1.5 rounded-full bg-gray-500 me-1.5'></span>
          Unknown
        </Badge>
      );
  }
};

export const statusItems = [
  {
    key: '0',
    value: 'Created',
    icon: <p className='size-2.5 rounded-full bg-stone-500'></p>
  },
  { key: '1', value: 'Payment', icon: <p className='size-2.5 rounded-full bg-slate-400'></p> },

  {
    key: '2',
    value: 'Queue',
    icon: <p className='size-2.5 rounded-full bg-fuchsia-400 '></p>
  },
  {
    key: '3',
    value: 'Deploying',
    icon: <p className='size-2.5 rounded-full bg-sky-400 '></p>
  },
  {
    key: '4',
    value: 'Installing',
    icon: <p className='size-2.5 rounded-full bg-blue-400'></p>
  },
  {
    key: '5',
    value: 'Running',
    icon: <p className='size-2.5 text-xs rounded-full bg-emerald-400'></p>
  },
  {
    key: '6',
    value: 'Finished',
    icon: <p className='size-2.5 rounded-full bg-neutral-400 '></p>
  },
  {
    key: '7',
    value: 'Canceled',
    icon: <p className='size-2.5 rounded-full bg-yellow-400 '></p>
  },
  {
    key: '8',
    value: 'Blocked',
    icon: <p className='size-2.5 rounded-full bg-rose-400 '></p>
  },
  {
    key: '9',
    value: 'Failed',
    icon: <p className='size-2.5 rounded-full bg-red-400 '></p>
  },
  {
    key: '10',
    value: 'Deleting',
    icon: <p className='size-2.5 rounded-full bg-amber-400'></p>
  },
  {
    key: '11',
    value: 'Destroyed',
    icon: <p className='size-2.5 rounded-full bg-gray-400 '></p>
  }
];
