import type { IUseQueryFactoryProps } from 'hooks/api/use-query-factory/use-query-factory.type';
import type { IUserInfo } from 'types/user';
import { API_ROUTES } from 'configs/api-routes';
import { useQueryFactory } from 'hooks/api/use-query-factory';

export const useShowUserMessagess = (
  ticketId: string,
  options?: Pick<IUseQueryFactoryProps<IUserInfo>, 'enabled' | 'params' | 'dependence'>
) => {
  return useQueryFactory<IUserInfo>({
    queryKey: ['SHOWMESSAGESQUERY', ticketId, `${options?.dependence}`],
    url: API_ROUTES.SHOW_USER_MESSAGES.replace('{ticketId}', ticketId),
    ...options
  });
};
