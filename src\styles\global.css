/* @tailwind base;
@tailwind components; */
@tailwind utilities;

@layer base {
  :root {
    /* Interface Settings */
    --header-height: 4rem;
    --content-padding: 1rem;
    --transition-duration: 0.2s;

    /* Base Colors */
    --background: oklch(1 0 0);
    --foreground: oklch(0.141 0.005 285.823);

    /* Card Colors */
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.141 0.005 285.823);

    /* Popover Colors */
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.141 0.005 285.823);

    /* Primary Colors */
    --primary: oklch(30.37% 0.1233 259.91); /* #00296B */
    --primary-foreground: oklch(90.49% 0.118 89.16); /* #ffdc80 */

    /* Secondary Colors */
    --secondary: oklch(90.49% 0.118 89.16); /* #ffdc80 */
    --secondary-foreground: oklch(30.37% 0.1233 259.91); /* #00296B */

    /* Muted Colors */
    --muted: oklch(0.967 0.001 286.375);
    --muted-foreground: oklch(0.552 0.016 285.938);

    /* Accent Colors */
    --accent: oklch(0.967 0.001 286.375);
    --accent-foreground: oklch(0.21 0.006 285.885);

    /* Destructive Colors */
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);

    /* Border & Input Colors */
    --border: oklch(0.92 0.004 286.32);
    --input: oklch(0.92 0.004 286.32);
    --ring: oklch(0.871 0.006 286.286);

    /* Chart Colors */
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* Sidebar Colors */
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.141 0.005 285.823);
    --sidebar-primary: oklch(0.21 0.006 285.885);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.967 0.001 286.375);
    --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
    --sidebar-border: oklch(0.92 0.004 286.32);
    --sidebar-ring: oklch(0.871 0.006 286.286);

    /* Radius */
    --radius: 0.625rem;
  }

  .dark {
    --background: oklch(0.141 0.005 285.823);
    --foreground: oklch(0.985 0 0);

    --card: oklch(0.141 0.005 285.823);
    --card-foreground: oklch(0.985 0 0);

    --popover: oklch(0.141 0.005 285.823);
    --popover-foreground: oklch(0.985 0 0);

    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.21 0.006 285.885);

    --secondary: oklch(0.274 0.006 286.033);
    --secondary-foreground: oklch(0.985 0 0);

    --muted: oklch(0.274 0.006 286.033);
    --muted-foreground: oklch(0.705 0.015 286.067);

    --accent: oklch(0.274 0.006 286.033);
    --accent-foreground: oklch(0.985 0 0);

    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);

    --border: oklch(0.274 0.006 286.033);
    --input: oklch(0.274 0.006 286.033);
    --ring: oklch(0.442 0.017 285.786);

    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    --sidebar: oklch(0.21 0.006 285.885);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.274 0.006 286.033);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.274 0.006 286.033);
    --sidebar-ring: oklch(0.442 0.017 285.786);
  }
}

@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(30.37% 0.1233 259.91); /* #00296B */
  --primary-foreground: oklch(90.49% 0.118 89.16); /* #ffdc80 */
  --secondary: oklch(90.49% 0.118 89.16); /* #ffdc80 */
  --secondary-foreground: oklch(30.37% 0.1233 259.91); /* #00296B */
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.871 0.006 286.286);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
  --primary-rgb: 0, 41, 107; /* This should match your primary color in RGB format */
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.141 0.005 285.823);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.274 0.006 286.033);
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
@layer base {
  html {
    overflow-y: scroll;
  }
}

@layer base {
  :root {
    --removed-body-scroll-bar-size: 0px;
  }

  /* Prevent layout shift when modal/dropdown opens */
  body {
    padding-right: var(--removed-body-scroll-bar-size);
  }

  /* Force scrollbar to always show to prevent layout shift */
  html {
    overflow-y: scroll;
    /* Prevent layout shift in Windows browsers */
    scrollbar-gutter: stable;
  }
}

@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .btn,
  .btn-sm {
    @apply font-medium inline-flex text-base items-center border border-transparent rounded-sm leading-snug transition duration-150 ease-in-out;
  }

  .btn {
    @apply px-4 py-2;
  }

  .btn-sm {
    @apply px-2 py-1 text-sm;
  }

  .btn-primary {
    @apply bg-primary text-white font-medium;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: oklch(0.25 0.1 250); /* #00296B */
    --primary-foreground: oklch(0.91 0.15 80); /* #ffdc80 */
    --secondary: oklch(0.91 0.15 80); /* #ffdc80 */
    --secondary-foreground: oklch(0.25 0.1 250); /* #00296B */
    --alternative: 46 100% 50%; /* #FDC500 */
    --alternative-foreground: 240 10% 3.9%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: oklch(0.25 0.1 250); /* #00296B */
    --primary-foreground: oklch(0.91 0.15 80); /* #ffdc80 */
    --secondary: oklch(0.91 0.15 80); /* #ffdc80 */
    --secondary-foreground: oklch(0.25 0.1 250); /* #00296B */
    --alternative: 46 100% 50%; /* #FDC500 */
    --alternative-foreground: 240 10% 3.9%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

.channel-stats-bg {
  background-image: url('/assets/media/images/2600x1600/bg-3.png');
}

[data-theme='dark'] .channel-stats-bg {
  background-image: url('/assets/media/images/2600x1600/bg-3-dark.png');
}

@layer components {
  .bento-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .bento-card {
    @apply relative overflow-hidden rounded-3xl transition-all duration-300;
  }

  .bento-card:hover {
    @apply transform scale-[1.02] shadow-xl;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary to-secondary;
  }

  .bg-grid-primary {
    background-image:
      linear-gradient(var(--primary) 1px, transparent 1px), linear-gradient(90deg, var(--primary) 1px, transparent 1px);
  }

  @keyframes subtle-bounce {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .animate-subtle-bounce {
    animation: subtle-bounce 3s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .circle-feature {
    @apply absolute size-12 rounded-full bg-white shadow-lg
    flex items-center justify-center transition-transform
    hover:scale-110 cursor-pointer;
  }

  .circle-feature::after {
    content: '';
    @apply absolute inset-0 rounded-full border-2 border-primary/20;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.5;
    }
  }

  @keyframes particlePulse {
    0% {
      transform: scale(1);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      opacity: 0.8;
    }
  }

  .signal-particle {
    animation: particlePulse 1s ease-in-out infinite;
  }

  /* Add glass effect utilities */
  .glass-effect {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 dark:bg-zinc-900/30 dark:border-white/10;
  }

  /* Add smooth dot movement */
  .dot-transition {
    transition: left 0.2s ease-out;
  }

  /* Add these new animations */
  @keyframes glass-shine {
    0% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: -500px 0;
      background-size: 200% 100%;
    }
    100% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: 500px 0;
      background-size: 200% 100%;
    }
  }

  @keyframes glass-shine-text {
    0% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: -200% 0;
      background-size: 200% 100%;
    }
    100% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: 200% 0;
      background-size: 200% 100%;
    }
  }

  .animate-glass-shine {
    animation: glass-shine 8s linear infinite;
  }

  .animate-glass-shine-text {
    animation: glass-shine-text 4s linear infinite;
    mix-blend-mode: overlay;
  }

  /* Dark mode adjustments */
  .dark .animate-glass-shine,
  .dark .animate-glass-shine-text {
    opacity: 0.5;
  }

  /* Add this new animation for the gradient text */
  @keyframes gradient-text {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-gradient-text {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(
      90deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 25%,
      hsl(var(--primary)) 50%,
      hsl(var(--secondary)) 75%,
      hsl(var(--primary)) 100%
    );
    background-size: 200% auto;
    animation: gradient-text 8s linear infinite;
  }

  /* Enhance glass shine for text */
  @keyframes glass-shine-text {
    0% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: -200% 0;
      background-size: 200% 100%;
    }
    100% {
      background: linear-gradient(
        60deg,
        transparent 0%,
        transparent 20%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 40%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 60%,
        transparent 100%
      );
      background-position: 200% 0;
      background-size: 200% 100%;
    }
  }

  .animate-glass-shine-text {
    animation: glass-shine-text 4s linear infinite;
    mix-blend-mode: overlay;
  }

  /* Dark mode adjustments */
  .dark .animate-glass-shine-text {
    opacity: 0.5;
  }

  /* Navigation menu glass effect styles */
  .NavigationMenuContent {
    @apply backdrop-blur-md
      bg-background/50 dark:bg-background/50
      border border-border/10 dark:border-border/10
      rounded-lg shadow-lg;
  }

  /* Button styles */
  .btn-sm {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  /* Glass effect for dropdowns */
  .NavigationMenuContent {
    @apply backdrop-blur-md bg-white/70 dark:bg-gray-950/70 border border-white/20 dark:border-white/10 rounded-lg shadow-lg;
  }

  .dashboard-card {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .dashboard-card:hover {
    @apply transform shadow-lg;
  }

  .dashboard-icon {
    @apply transition-transform duration-300;
  }

  .dashboard-icon:hover {
    @apply transform scale-110;
  }

  .dashboard-button {
    @apply transition-all duration-300;
  }

  .dashboard-button:hover {
    @apply transform scale-105;
  }

  .account-card-hover {
    @apply relative overflow-hidden;
  }

  .account-card-hover::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(800px circle at var(--mouse-x) var(--mouse-y), var(--primary) / 10%, transparent 40%);
    opacity: 0;
    transition: opacity 0.5s;
    pointer-events: none;
    z-index: 1;
  }

  .account-card-hover:hover::after {
    opacity: 1;
  }
}

@layer components {
  .faq-gradient-bg {
    background: radial-gradient(circle at top center, var(--primary) / 0.15, transparent 70%);
  }

  .faq-tab-trigger {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .faq-tab-trigger::after {
    content: '';
    @apply absolute bottom-0 left-0 h-0.5 w-full transform scale-x-0
           bg-primary transition-transform duration-300;
  }

  .faq-tab-trigger[data-state='active']::after {
    @apply scale-x-100;
  }

  .faq-accordion-item {
    @apply transition-all duration-300;
  }

  .faq-accordion-item[data-state='open'] {
    @apply shadow-lg;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary)) 50%, hsl(var(--secondary)));
  }
  /* Service Card Icon Colors */
  .bg-indigo-100\/50 {
    background-color: rgb(224 231 255 / 0.5);
  }
  .text-indigo-500 {
    color: rgb(99 102 241);
  }

  .bg-emerald-100\/50 {
    background-color: rgb(209 250 229 / 0.5);
  }
  .text-emerald-500 {
    color: rgb(16 185 129);
  }

  .bg-purple-100\/50 {
    background-color: rgb(237 233 254 / 0.5);
  }
  .text-purple-500 {
    color: rgb(168 85 247);
  }

  .bg-amber-100\/50 {
    background-color: rgb(254 243 199 / 0.5);
  }
  .text-amber-500 {
    color: rgb(245 158 11);
  }
}

.customer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customer-avatar div {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

@keyframes dash {
  to {
    stroke-dashoffset: -8;
  }
}

.animate-dash {
  animation: dash 1s linear infinite;
}
@keyframes grow-ping {
  0%,
  100% {
    width: 0.3rem; /* size-2.5 */
    height: 0.3rem;
  }
  50% {
    width: 0.4rem; /* size-3 */
    height: 0.4rem;
  }
}

.animate-grow-ping {
  animation: grow-ping 1s infinite;
}
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.purchase-point {
  position: relative;
  transform-origin: center;
  transition: transform 0.3s ease-out;
}

.purchase-point:hover {
  transform: scale(1.1);
}

.purchase-pulse {
  @apply absolute rounded-full border-2 border-primary;
  animation: purchase-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-600 {
  animation-delay: 600ms;
}

@keyframes purchase-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.purchase-notification {
  transform-origin: bottom right;
  animation: slide-in 0.3s ease-out forwards;
}

.purchase-notification.fade-out {
  animation: slide-out 0.3s ease-in forwards;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@keyframes ping {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(3);
    opacity: 0;
  }
}

.animate-ping {
  position: absolute;
  left: 50%;
  top: 50%;
  animation: ping 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes flagWave {
  0%,
  100% {
    transform: rotate(-2deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

.flag-marker {
  animation: flagWave 3s ease-in-out infinite;
  transform-origin: bottom center;
}

/* Add these styles to your global.css */
.Map-container {
  @apply relative w-full h-full overflow-hidden;
  background: linear-gradient(to bottom, rgba(var(--background-start-rgb), 0.9), rgba(var(--background-end-rgb), 0.9));
}

.Map-container img {
  filter: saturate(0) brightness(0.5);
  mix-blend-mode: overlay;
}

.Map-container::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0.75) 100%);
}

.dark .Map-container::after {
  background: linear-gradient(145deg, rgba(24, 24, 27, 0.85) 0%, rgba(24, 24, 27, 0.75) 100%);
}

@keyframes pinPulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(3);
    opacity: 0.2;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.Pin-back {
  transform-origin: center;
  animation: pinPulse 1.5s ease-out infinite;
}

.Pin-front {
  transform-origin: center;
  animation: subtle-bounce 3s ease-in-out infinite;
}

@keyframes subtle-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.Map-container svg {
  animation: subtle-float 20s ease-in-out infinite;
}

/* Ensure content remains visible */
.Map-container * {
  position: relative;
  z-index: 1;
}

/* Map pin pulse animation */
@keyframes pinPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(50);
    opacity: 0;
  }
}

.Pin-back {
  transform-origin: center;
  animation: pinPulse 3s ease-out infinite;
}

/* Optional: Add hover effect for the map */
.Map-container:hover {
  filter: none; /* Changed from: filter: saturate(0) brightness(1.1); */
  transition: none; /* Remove transition since we don't have hover effect anymore */
}

/* Dark mode adjustments */
.dark .Map-container {
  opacity: 0.9;
}

.Location-nav {
  margin-top: 30px;
  color: theme('colors.zinc.600');
  @apply dark:text-zinc-400;
}

.Location-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.Location-nav li {
  margin: 15px;
  cursor: default;
  user-select: none;
  position: relative;
}

.Location-nav li::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  @apply bg-primary;
  opacity: 0;
  transform: translateY(100%);
  transition:
    opacity 0.25s linear,
    transform 0.25s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.Location-nav li:hover::after {
  opacity: 1;
  transform: translateY(0);
}

@layer utilities {
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
  }
}

@keyframes gradient-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes border-flow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.8;
  }
}

.animate-gradient-move {
  animation: gradient-move 15s ease infinite;
  background-size: 200% 200%;
}

.animate-border-flow {
  animation: border-flow 8s linear infinite;
  background-size: 200% 200%;
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

/* Base styles with compact spacing */
.layout-transition {
  --spacing-base: 0.5rem;
  --content-padding: 0.75rem;
  transition: all var(--transition-duration) ease-in-out;
}

/* Expanded mode (when compact mode is OFF) */
.expanded-mode {
  --spacing-base: 1rem;
  --content-padding: 1.5rem;
}

/* Apply spacing to common elements */
.card,
.section,
.container {
  padding: var(--content-padding);
  gap: var(--spacing-base);
}

/* Apply spacing to form elements */
.form-group {
  margin-bottom: var(--spacing-base);
}

/* Apply spacing to list items */
.list-item {
  padding: calc(var(--spacing-base) * 0.5) var(--spacing-base);
}

/* Apply spacing to grid layouts */
.grid {
  gap: var(--spacing-base);
}

/* Transition for smooth spacing changes */
.layout-transition * {
  transition:
    padding var(--transition-duration) ease-in-out,
    margin var(--transition-duration) ease-in-out,
    gap var(--transition-duration) ease-in-out;
}

/* Font size base styles */
:root {
  --font-size-small: 0.875rem; /* 14px - new small size */
  --font-size-normal: 1rem; /* 16px - current small becomes normal */
  --font-size-large: 1.125rem; /* 18px - current normal becomes large */
}

/* Font size variations */
.text-size-small {
  --base-font-size: var(--font-size-small);
  font-size: var(--base-font-size);
}

.text-size-normal {
  --base-font-size: var(--font-size-normal);
  font-size: var(--base-font-size);
}

.text-size-large {
  --base-font-size: var(--font-size-large);
  font-size: var(--base-font-size);
}

/* Scale other text elements relative to the base font size */
.text-size-small h1 {
  font-size: calc(var(--base-font-size) * 2);
}
.text-size-small h2 {
  font-size: calc(var(--base-font-size) * 1.75);
}
.text-size-small h3 {
  font-size: calc(var(--base-font-size) * 1.5);
}
.text-size-small h4 {
  font-size: calc(var(--base-font-size) * 1.25);
}

.text-size-normal h1 {
  font-size: calc(var(--base-font-size) * 2.25);
}
.text-size-normal h2 {
  font-size: calc(var(--base-font-size) * 2);
}
.text-size-normal h3 {
  font-size: calc(var(--base-font-size) * 1.75);
}
.text-size-normal h4 {
  font-size: calc(var(--base-font-size) * 1.5);
}

.text-size-large h1 {
  font-size: calc(var(--base-font-size) * 2.5);
}
.text-size-large h2 {
  font-size: calc(var(--base-font-size) * 2.25);
}
.text-size-large h3 {
  font-size: calc(var(--base-font-size) * 2);
}
.text-size-large h4 {
  font-size: calc(var(--base-font-size) * 1.75);
}

/* Add smooth transitions for font size changes */
body {
  transition: font-size 0.2s ease-in-out;
}

/* Adjust line heights for better readability */
.text-size-small {
  line-height: 1.4;
}
.text-size-normal {
  line-height: 1.5;
}
.text-size-large {
  line-height: 1.6;
}

/* Sticky header styles */
:root {
  --header-height: 4rem; /* 64px */
  --breadcrumb-height: 2.5rem; /* 40px */
}

/* Header styles */
header {
  height: var(--header-height);
  width: 100%;
  background-color: hsl(var(--background));
  display: flex;
  align-items: center;
}

/* Main layout structure */
.layout-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header and breadcrumb container */
.header-section {
  width: 100%;
}

/* Breadcrumb styles */
.breadcrumb-wrapper {
  width: 100%;
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border) / 0.2);
  height: var(--breadcrumb-height);
}

/* Main content */
main {
  flex: 1;
}

@layer utilities {
  .bg-dot-pattern {
    background-size: 20px 20px;
    background-image: radial-gradient(circle at 1px 1px, rgb(var(--foreground) / 0.1) 1px, transparent 0);
  }
}

/* Add these keyframes for additional animations if needed */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

@keyframes spin-slow {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping-slow {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shine {
  from {
    background-position: 200% center;
  }
  to {
    background-position: -200% center;
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 1;
    filter: brightness(1);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2);
  }
}

@keyframes border-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add these utility classes */
@layer utilities {
  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-ping-slow {
    animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shine {
    animation: shine 3s linear infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-border-flow {
    animation: border-flow 8s linear infinite;
    background-size: 200% 200%;
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 dark:bg-black/10;
  }

  .gradient-border {
    @apply bg-gradient-to-r from-primary via-secondary to-primary;
    background-size: 200% 100%;
  }
}

@layer utilities {
  /* Enhanced gradient text utilities */
  .text-gradient {
    @apply bg-clip-text text-transparent;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary via-secondary to-primary;
  }

  .gradient-subtle {
    @apply bg-gradient-to-r from-foreground/80 to-foreground/60;
  }
}

/* Enhanced animations */
@keyframes subtle-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@layer utilities {
  .animate-subtle-bounce {
    animation: subtle-bounce 3s ease-in-out infinite;
  }

  .animate-gradient-shift {
    animation: gradient-shift 8s ease infinite;
    background-size: 200% 200%;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s ease-in-out infinite;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
}

/* Add these to your existing Tailwind config if not already present */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
  }
}

@layer components {
  .bg-grid-primary {
    background-size: 100px 100px;
    background-image:
      linear-gradient(to right, var(--primary) 1px, transparent 1px),
      linear-gradient(to bottom, var(--primary) 1px, transparent 1px);
  }

  .card-shine {
    --shine-deg: 45deg;
    position: relative;
    overflow: hidden;
  }

  .card-shine::after {
    content: '';
    position: absolute;
    inset: 0;
    transform: translateX(-100%);
    background: linear-gradient(var(--shine-deg), transparent 20%, rgba(255, 255, 255, 0.1) 25%, transparent 30%);
    animation: shine 3s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    50%,
    100% {
      transform: translateX(100%);
    }
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px var(--primary);
  }
}

@layer components {
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .bg-grid-pattern {
    background-size: 50px 50px;
    background-image:
      linear-gradient(to right, var(--primary) / 5% 1px, transparent 1px),
      linear-gradient(to bottom, var(--primary) / 5% 1px, transparent 1px);
    mask-image: linear-gradient(to bottom, transparent, black, transparent);
  }

  .bg-gradient-radial {
    background-image: radial-gradient(circle at 50% 0%, var(--primary) / 10%, transparent 70%);
  }

  .card-hover-effect {
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-style: preserve-3d;
  }

  .card-hover-effect:hover {
    transform: translateZ(20px) rotateX(2deg) rotateY(2deg);
  }

  .shine-effect {
    position: relative;
    overflow: hidden;
  }

  .shine-effect::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, var(--primary) / 10%, transparent 60%);
    transform: rotate(45deg);
    animation: shine 6s linear infinite;
  }

  @keyframes shine {
    from {
      transform: rotate(45deg) translateY(-100%);
    }
    to {
      transform: rotate(45deg) translateY(100%);
    }
  }

  .floating-animation {
    animation: floating 6s ease-in-out infinite;
  }

  @keyframes floating {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

/* Navigation menu positioning */
.NavigationMenuContent {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  min-width: max-content;
  @apply backdrop-blur-md
    bg-background/95 dark:bg-background/95
    border border-border/10 dark:border-border/10
    rounded-lg shadow-lg
    mt-1;
}

/* Ensure the viewport doesn't interfere with positioning */
[data-slot='navigation-menu'] {
  position: relative;
}

@layer base {
  :root {
    --navigation-height: 4.5rem;
  }
}

@layer components {
  .NavigationMenuContent {
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%);
    width: var(--radix-navigation-menu-viewport-width);
    @apply bg-background/95 dark:bg-background/95 backdrop-blur-md;
  }

  .NavigationMenuViewport {
    width: var(--radix-navigation-menu-viewport-width);
    height: var(--radix-navigation-menu-viewport-height);
    @apply bg-background/95 dark:bg-background/95 backdrop-blur-md;
  }
}

@layer components {
  .btn-animated {
    background: radial-gradient(circle, var(--primary) 0%, var(--secondary) 100%);
    line-height: 42px;
    padding: 0;
    border: none;
    position: relative;
  }

  .btn-animated span {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
  }

  .btn-animated:before,
  .btn-animated:after {
    position: absolute;
    content: '';
    height: 0%;
    width: 1px;
    box-shadow:
      -1px -1px 20px 0px rgba(255, 255, 255, 1),
      -4px -4px 5px 0px rgba(255, 255, 255, 1),
      7px 7px 20px 0px rgba(0, 0, 0, 0.4),
      4px 4px 5px 0px rgba(0, 0, 0, 0.3);
  }

  .btn-animated:before {
    right: 0;
    top: 0;
    transition: all 500ms ease;
  }

  .btn-animated:after {
    left: 0;
    bottom: 0;
    transition: all 500ms ease;
  }

  .btn-animated:hover {
    background: transparent;
    color: var(--primary);
    box-shadow: none;
  }

  .btn-animated:hover:before,
  .btn-animated:hover:after {
    height: 100%;
  }

  .btn-animated span:before,
  .btn-animated span:after {
    position: absolute;
    content: '';
    box-shadow:
      -1px -1px 20px 0px rgba(255, 255, 255, 1),
      -4px -4px 5px 0px rgba(255, 255, 255, 1),
      7px 7px 20px 0px rgba(0, 0, 0, 0.4),
      4px 4px 5px 0px rgba(0, 0, 0, 0.3);
  }

  .btn-animated span:before {
    left: 0;
    top: 0;
    width: 0%;
    height: 0.5px;
    transition: all 500ms ease;
  }

  .btn-animated span:after {
    right: 0;
    bottom: 0;
    width: 0%;
    height: 0.5px;
    transition: all 500ms ease;
  }

  .btn-animated:hover span:before,
  .btn-animated:hover span:after {
    width: 100%;
  }
}

@layer components {
  @keyframes text-shimmer {
    0% {
      background-position: 200% 50%;
    }
    100% {
      background-position: -200% 50%;
    }
  }

  .animate-text-shimmer {
    animation: text-shimmer 6s linear infinite;
  }
}

@layer components {
  .animate-gradient-heading {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(
      90deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 25%,
      hsl(var(--primary)) 50%,
      hsl(var(--secondary)) 75%,
      hsl(var(--primary)) 100%
    );
    background-size: 200% auto;
    animation: gradient-heading 8s linear infinite;
  }

  .animate-gradient-shine {
    position: relative;
    display: inline-block;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 200%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shine-wave 3s infinite;
    }
  }

  .animate-gradient-move {
    animation: gradient-move 15s ease infinite;
    background-size: 200% 200%;
  }

  @keyframes gradient-heading {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes shine-wave {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes gradient-move {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Dark mode enhancements */
  .dark .animate-gradient-heading {
    background-image: linear-gradient(
      90deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 25%,
      hsl(var(--primary)) 50%,
      hsl(var(--secondary)) 75%,
      hsl(var(--primary)) 100%
    );
    text-shadow: 0 0 30px rgba(var(--primary), 0.5);
  }

  .dark .animate-gradient-shine::after {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }
}

@layer utilities {
  .animate-gradient {
    animation: gradient 8s linear infinite;
    background-size: 200% auto;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
}

@layer components {
  .animate-gradient-x {
    animation: gradient-x 15s ease infinite;
    background-size: 200% 200%;
  }

  @keyframes gradient-x {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .bg-grid-primary {
    background-size: 100px 100px;
    background-image:
      linear-gradient(to right, var(--primary) 1px, transparent 1px),
      linear-gradient(to bottom, var(--primary) 1px, transparent 1px);
  }

  .glass-card {
    @apply bg-background/80 dark:bg-background/20
           backdrop-blur-lg
           border border-border/50 dark:border-border/20
           shadow-sm hover:shadow-lg
           transition-all duration-300;
  }

  /* Dark mode specific styles */
  .dark .faq-gradient-text {
    @apply bg-gradient-to-r
           from-primary-foreground
           via-primary-foreground/80
           to-primary-foreground;
    animation: gradient 15s ease infinite;
    background-size: 200% 200%;
  }

  .dark .glass-effect {
    @apply bg-background/20
           backdrop-blur-md
           border-border/20
           shadow-primary/10;
  }

  .dark .accordion-content {
    @apply text-muted-foreground/90;
  }

  .dark .tab-trigger {
    @apply text-foreground/60
           hover:text-foreground
           bg-background/20;
  }

  .dark .tab-trigger[data-state='active'] {
    @apply bg-primary-foreground
           text-primary;
  }

  .dark .gradient-text {
    @apply bg-gradient-to-r from-primary-foreground via-primary-foreground/80 to-primary-foreground;
  }
}

@layer utilities {
  /* Animation keyframes */
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-gradient-shift {
    animation: gradient-shift 15s ease infinite;
    background-size: 200% 200%;
  }

  .bg-dot-pattern {
    background-size: 16px 16px;
    background-image: radial-gradient(circle at 1px 1px, rgb(var(--foreground) / 0.2) 1px, transparent 0);
  }

  /* Dark mode specific utilities */
  .dark .glass-effect {
    @apply bg-background/20 backdrop-blur-md border-border/20;
  }

  .dark .gradient-text {
    @apply bg-gradient-to-r from-primary-foreground via-primary-foreground/80 to-primary-foreground;
  }
}

@layer base {
  :root {
    --transition-duration: 0.3s;
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --border-glow: 0 0 10px rgba(255, 255, 255, 0.1);
  }
}

@layer components {
  .glass-card {
    @apply bg-background/80
           backdrop-blur-lg
           border
           shadow-sm
           transition-all duration-300 ease-in-out;

    /* Light mode - darker border */
    @apply border-border/70
           hover:shadow-lg
           hover:border-border/90
           hover:bg-background/90;

    /* Dark mode - unchanged */
    @apply dark:bg-background/10
           dark:border-white/10
           dark:hover:border-white/20
           dark:hover:bg-background/20
           dark:hover:shadow-[0_0_15px_rgba(255,255,255,0.1)];
  }

  .bg-grid-primary {
    background-size: 100px 100px;
    /* Light mode grid */
    background-image:
      linear-gradient(to right, var(--primary) / 0.1 1px, transparent 1px),
      linear-gradient(to bottom, var(--primary) / 0.1 1px, transparent 1px);

    /* Dark mode grid */
    @apply dark:bg-background/5;
  }

  /* Dark mode specific styles with improved visibility */
  .dark .faq-gradient-text {
    @apply bg-gradient-to-r
           from-white
           via-white/80
           to-white/90
           text-transparent
           bg-clip-text;
    animation: gradient 15s ease infinite;
    background-size: 200% 200%;
  }

  .dark .glass-effect {
    @apply bg-white/5
           backdrop-blur-md
           border-white/10
           shadow-[0_0_15px_rgba(255,255,255,0.05)]
           transition-all duration-300 ease-in-out
           hover:bg-white/10
           hover:border-white/20
           hover:shadow-[0_0_20px_rgba(255,255,255,0.1)];
  }

  .dark .accordion-content {
    @apply text-white/80
           transition-colors
           duration-200;
  }

  .dark .tab-trigger {
    @apply text-white/60
           hover:text-white
           bg-white/5
           border border-white/10
           transition-all
           duration-200
           ease-in-out
           hover:bg-white/10
           hover:border-white/20;
  }

  .dark .tab-trigger[data-state='active'] {
    @apply bg-white/15
           text-white
           border-white/20
           shadow-[0_0_10px_rgba(255,255,255,0.1)];
  }

  .dark .gradient-text {
    @apply bg-gradient-to-r
           from-white
           via-white/90
           to-white
           text-transparent
           bg-clip-text
           transition-all
           duration-300;
  }
}

/* Smooth animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced hover transitions */
@layer utilities {
  .hover-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-in-out
           hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]
           dark:hover:shadow-[0_0_15px_rgba(255,255,255,0.1)];
  }

  .border-glow {
    @apply border border-white/10
           dark:border-white/20
           transition-all duration-300 ease-in-out
           hover:border-white/30
           dark:hover:border-white/40
           hover:shadow-[0_0_15px_rgba(255,255,255,0.1)]
           dark:hover:shadow-[0_0_15px_rgba(255,255,255,0.2)];
  }
}

/* Additional dark mode enhancements */
.dark {
  @apply selection:bg-white/20 selection:text-white;
}

.dark *::selection {
  @apply bg-white/20 text-white;
}

/* Improved focus states for dark mode */
.dark *:focus-visible {
  @apply outline-white/30 outline-offset-2;
}

/* Smooth scrolling for the entire application */
html {
  @apply scroll-smooth;
}

/* Improved dark mode scrollbar */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  @apply bg-black/20;
}

.dark ::-webkit-scrollbar-thumb {
  @apply bg-white/20
         hover:bg-white/30
         transition-colors
         duration-200
         rounded-full;
}

@layer components {
  /* Question title styles */
  .question-title {
    @apply text-foreground
           transition-colors
           duration-200
           ease-in-out
           hover:text-primary;

    /* Dark mode specific hover */
    @apply dark:text-foreground
           dark:hover:text-primary-foreground
           dark:hover:opacity-90;
  }

  /* If you're using a link component, you can also add these styles */
  .question-link {
    @apply relative
           inline-block
           transition-all
           duration-200
           ease-in-out;

    /* Hover effect for light mode */
    &:hover {
      @apply text-primary;
    }

    /* Hover effect for dark mode */
    .dark &:hover {
      @apply text-primary-foreground
             opacity-90;
    }
  }
}
