import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import * as React from 'react';
import { z } from 'zod';
import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] transition-[color,box-shadow] overflow-hidden',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/90 ',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/90 dark:bg-secondary-dark dark:text-secondary-dark-foreground dark:hover:bg-secondary-dark/90',
        destructive:
          'border-transparent bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive-dark dark:text-white dark:hover:bg-destructive-dark/90',
        outline:
          'text-foreground hover:bg-accent hover:text-accent-foreground dark:bg-accent-dark dark:text-accent-dark-foreground',
        primary_light: 'text-blue-600/80 dark:text-blue-300 bg-blue-200/20 dark:bg-blue-900/60',
        success_light: 'text-green-600/80 dark:text-green-300 bg-green-200/20 dark:bg-green-900/60',
        warning_light: 'text-yellow-600/80 dark:text-yellow-300 bg-yellow-200/20 dark:bg-yellow-900/60',
        danger_light: 'text-red-600/80 dark:text-red-300 bg-red-200/20 dark:bg-red-900/60',
        stone_light: 'text-stone-600/80 dark:text-stone-300 bg-stone-200/20 dark:bg-stone-900/60',
        fuchsia_light: 'text-fuchsia-600/80 dark:text-fuchsia-300 bg-fuchsia-200/20 dark:bg-fuchsia-900/60',
        sky_light: 'text-sky-600/80 dark:text-sky-300 bg-sky-200/20 dark:bg-sky-900/60',
        emerald_light: 'text-emerald-600/80 dark:text-emerald-300 bg-emerald-200/20 dark:bg-emerald-900/60',
        neutral_light: 'text-neutral-600/80 dark:text-neutral-300 bg-neutral-200/20 dark:bg-neutral-900/60',
        rose_light: 'text-rose-600/80 dark:text-rose-300 bg-rose-200/20 dark:bg-rose-900/60',
        amber_light: 'text-amber-600/80 dark:text-amber-300 bg-amber-200/20 dark:bg-amber-900/60',
        gray_light: 'text-gray-600/80 dark:text-gray-300 bg-gray-200/20 dark:bg-gray-900/60',
        slate_light: 'text-slate-600/80 dark:text-slate-300 bg-slate-200/20 dark:bg-slate-900/60'
      }
    },
    defaultVariants: {
      variant: 'default'
    }
  }
);

type BadgeVariants = VariantProps<typeof badgeVariants>['variant'];

const variantToColorMap: Record<string, string> = {
  primary_light: 'blue',
  success_light: 'green',
  warning_light: 'yellow',
  danger_light: 'red',
  stone_light: 'stone',
  fuchsia_light: 'fuchsia',
  sky_light: 'sky',
  emerald_light: 'emerald',
  neutral_light: 'neutral',
  rose_light: 'rose',
  amber_light: 'amber',
  gray_light: 'gray',
  slate_light: 'slate'
};
const variantSchema = z
  .union([
    z.string().refine(val => !!val && val[0] === val[0]?.toLowerCase(), {
      message: 'Variant must start with a lowercase letter.'
    }),
    z.record(z.string(), z.string()),
    z.array(z.string())
  ])
  .optional();

const prettifyVariantName = (val: string) => {
  return val
    .toLowerCase()
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
};

const defaultPulseCheck = (value: string) => value.toLowerCase().endsWith('ing');

type BadgeProps<T extends string> = {
  value: T | T[] | undefined;
  variant?: BadgeVariants | Record<T, BadgeVariants> | BadgeVariants[];
  maxShows?: number;
  showPulse?: boolean;
  pulseFor?: (value: T) => boolean;
  className?: string;
};

const getPulseColor = (variant: BadgeVariants): string | null => {
  if (typeof variant === 'string') {
    return variantToColorMap[variant] ?? null;
  }
  return null;
};

const Badge = function Badge<T extends string>({
  value,
  variant = 'default',
  maxShows = 2,
  showPulse = true,
  pulseFor = defaultPulseCheck,
  className
}: BadgeProps<T>) {
  const validation = variantSchema.safeParse(variant);
  if (!validation.success) {
    console.warn('Badge variant validation failed:', validation.error.errors);
  }

  const values = Array.isArray(value) ? value : value ? [value] : [];
  const visible = values.slice(0, maxShows);
  const overflowCount = values.length - maxShows;

  const getVariantFor = (val: T, idx: number): BadgeVariants => {
    if (typeof variant === 'string') {
      return variant;
    }
    if (Array.isArray(variant)) {
      return variant[idx] ?? 'default';
    }
    return variant?.[val] ?? 'default';
  };

  return (
    <div className='inline-flex items-center gap-1 '>
      {visible.map((val, idx) => {
        const currentVariant = getVariantFor(val, idx);
        const isPulsing = showPulse && pulseFor(val);
        const pulseColor = getPulseColor(currentVariant);

        return (
          <span key={val} className={cn(badgeVariants({ variant: currentVariant }), className)}>
            {isPulsing && pulseColor && (
              <span className='relative flex justify-center items-center size-2'>
                <span
                  className={cn(
                    'absolute size-2 -mt-1 -ms-1 animate-ping rounded-full opacity-75',
                    `bg-${pulseColor}-400`
                  )}
                />
                <span className={cn('relative size-2 rounded-full', `bg-${pulseColor}-500`)} />
              </span>
            )}
            {prettifyVariantName(val)}
          </span>
        );
      })}
      {overflowCount > 0 && (
        <span className={cn(badgeVariants({ variant: 'sky_light' }), className)}>
          +
          {overflowCount}
        </span>
      )}
    </div>
  );
} as <T extends string>(props: BadgeProps<T>) => React.ReactElement;

export { Badge, badgeVariants, prettifyVariantName };
export type { BadgeVariants };
