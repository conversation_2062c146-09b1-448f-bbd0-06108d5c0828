import { MessageSquarePlus } from 'lucide-react';
import Image from 'next/image';
import MessageBoxImage from 'public/img/illustrations/18.svg';
import * as React from 'react';
// components/EmptyState.tsx
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export default function EmptyState({
  setCreateNewTicket
}: {
  setCreateNewTicket: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  return (
    <Card className='flex w-full flex-col items-center justify-center min-h-[60vh] text-center px-4'>
      <div className='max-w-md flex flex-col justify-center items-center'>
        <Image src={MessageBoxImage} height={150} width={150} alt='' />
        <h2 className='text-2xl text-gray-700 font-bold mt-2 mb-4'>Ticket Management</h2>
        <p className='text-muted-foreground mb-6 leading-relaxed'>
          To create a new ticket, click the
          {' '}
          <span className='font-semibold'>New Ticket</span>
          {' '}
          button.
          <br />
          To continue the conversation, select a ticket from the list.
        </p>
        <div className='flex gap-4 justify-center'>
          <Button onClick={() => setCreateNewTicket(true)} variant='blue_primary'>
            <MessageSquarePlus className='w-4 h-4 ml-2' />
            New Ticket
          </Button>
          {/* <Button variant="outline">
            <List className="w-4 h-4 ml-2" />
            View Tickets
          </Button> */}
        </div>
      </div>
    </Card>
  );
}
