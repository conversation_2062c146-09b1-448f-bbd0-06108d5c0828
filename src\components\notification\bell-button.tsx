'use client';
import { Bell } from 'lucide-react';
import { useState } from 'react';
import NotificationPopover from '@/components/notification';
import { Button } from '@/components/ui/button';
import { useNotificationsList } from '@/services/api/user/notifications';

export const NotificationButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: notificationsData } = useNotificationsList(1, 1, true);
  const hasUnreadNotifications = notificationsData?.pagination?.total > 0;

  return (
    <div className='relative'>
      <Button variant='ghost' className='text-primary' size='icon' onClick={() => setIsOpen(!isOpen)}>
        <Bell className='h-5 w-5 ' />
        {hasUnreadNotifications && <span className='absolute top-1 left-2 block h-1 w-1 rounded-full bg-destructive' />}
      </Button>

      {isOpen && (
        <div className='absolute right-0 top-12 z-50'>
          <NotificationPopover onClose={() => setIsOpen(false)} />
        </div>
      )}
    </div>
  );
};
