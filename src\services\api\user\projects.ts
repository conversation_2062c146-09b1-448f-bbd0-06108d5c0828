import type { IUseMutationFactoryProps } from '@/hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IProject } from '@/types/project';
import type { ITableQuery } from '@/types/table-query';
import { useQuery } from '@tanstack/react-query';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { API_ROUTES } from '@/configs/api-routes';
import { useMutationFactory } from '@/hooks/api/use-mutation-factory';
import { usePaginationFactory } from '@/hooks/api/use-pagination-factory';
import { SERVER } from '@/libs/Axios';

export type IPagination = {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  first_page_url: string;
  from: number;
  last_page_url: string;
  links: Record<string, any>;
  next_page_url: string | null;
  prev_page_url: string | null;
};

export const useProjectsPaginationList = (query: ITableQuery, options?: { enabled: boolean }) => {
  return usePaginationFactory<IProject[]>({
    url: API_ROUTES.MY_PROJECTS,
    page: query.page,
    perPage: query.per_page,
    queryKey: [API_QUERY_KEY.PROJECTS_LIST, JSON.stringify(query)], // ✅ فقط برای cache
    query, // ✅ برای ساختن پارامتر واقعی
    ...options
  });
};
export const useProjectsList = (query?: ITableQuery, options?: { enabled: boolean }) => {
  return useQuery<IProject[]>({
    queryKey: [API_QUERY_KEY.PROJECTS_LIST, query],
    queryFn: async () => {
      const response = await SERVER.get<IProject[]>(API_ROUTES.MY_PROJECTS, {
        params: query || null
      });
      return response.data;
    },
    ...options
  });
};
export const useCreateProject = (
  options?: Pick<IUseMutationFactoryProps<IProject>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IProject>({
    url: API_ROUTES.PROJECTS_CREATE,
    method: 'POST',
    ...options
  });
};
