'use client';
import type { IRegion } from '@tradevpsnet/client';
import type { IProject } from '@/types/project';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { ArrowLeft, Check, ChevronsUpDown, HelpCircle, Loader2 } from 'lucide-react';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import Link from 'next/link';

import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { API_QUERY_KEY } from '@/configs/api-query-key';
import { IMAGE_URL } from '@/configs/image-url';
import { PAGE_ROUTES } from '@/configs/page-routes';
import { cn } from '@/lib/utils';
import { useProjectsList } from '@/services/api/user/projects';
import { useAvailableRegions } from '@/services/api/windows/use-region-list-fetch';
import { useWindowsServerDeployMutation } from '@/services/api/windows/use-server-mutate';
import { getConsistentColor } from '@/utils/avatar';
import { CloudProvider, WindowsVersion, windowsVersionMap } from '../list-map';

const formSchema = z.object({
  name: z.string().min(3, 'Server name is required'),
  admin_username: z.string().min(3, 'Server name is required'),
  admin_password: z
    .string()
    .min(12, 'The admin password field must be at least 12 characters.')
    .max(123, 'The admin password field must be at most 123 characters.')
    .regex(/[A-Z]/, 'The admin password must contain at least one uppercase letter.')
    .regex(/\d/, 'The admin password must contain at least one number.'),
  region: z.number().min(0, 'Region is required'),
  project_id: z.string().min(1, 'Project is required'),
  provider: z.number().optional(),
  version: z.number().optional(),
  cpu: z.number().min(1).optional(),
  memory: z.number().min(1).optional(),
  disk: z.number().min(1).optional(),
  disk_type: z.number().optional(),
  auto_scale_cpu: z.boolean().optional(),
  auto_scale_memory: z.boolean().optional(),
  has_static_ip: z.boolean().optional()
});

const getBrokersByRegion = (regionId: number): string => {
  switch (regionId) {
    case 0:
      return 'FXCM, Interactive Brokers';
    case 1:
      return 'Saxo Bank, DEGIRO';
    case 2:
      return 'Flatex, XTB, IG';
    case 3:
      return 'IG Markets, Pepperstone';
    default:
      return 'Multiple brokers available';
  }
};

export function DeployServer() {
  const router = useRouter();
  const { data: projectsResponse, isLoading: isProjectsLoading } = useProjectsList();
  const { data: regionsResponse } = useAvailableRegions();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      admin_username: '',
      admin_password: '',
      region: 0,
      provider: CloudProvider.AZURE,
      cpu: 2,
      memory: 4,
      disk: 80,
      disk_type: 0,
      version: WindowsVersion.WINDOWS_11_PRO,
      auto_scale_cpu: false,
      auto_scale_memory: false,
      has_static_ip: false
    }
  });

  const [regions, setRegions] = React.useState<any>([]);
  const [projects, setProjects] = React.useState<any>([]);
  const [openProject, setOpenProject] = React.useState(false);

  const projectColors = React.useMemo(() => {
    return (
      projects?.reduce(
        (acc: Record<string | number, string>, project: IProject) => ({
          ...acc,
          [project.id]: getConsistentColor(project.name)
        }),
        {} as Record<string | number, string>
      ) || {}
    );
  }, [projects]);
  React.useEffect(() => {
    if (regionsResponse && Array.isArray(regionsResponse?.data)) {
      const formattedRegions = regionsResponse.data.map((region, index) => ({
        id: index,
        label: region.label,
        icon: region.icon
      }));
      setRegions(formattedRegions);

      if (formattedRegions.length > 0 && formattedRegions[0]?.id !== undefined) {
        form.setValue('region', formattedRegions[0].id);
      }
    }
  }, [regionsResponse, form]);

  React.useEffect(() => {
    if (projectsResponse) {
      setProjects(projectsResponse);
      if (projectsResponse[0]?.id) {
        form.setValue('project_id', projectsResponse[0].id);
      }
    }
  }, [projectsResponse, form]);
  const { theme } = useTheme();
  const queryClient = useQueryClient();

  const refetchWindowsServers = () => {
    queryClient.refetchQueries({
      queryKey: [API_QUERY_KEY.WINDOWS_SERVERS_LIST],
      exact: false
    });
  };
  const imgQuestionSrc = theme === 'dark' ? IMAGE_URL.QUESTION_ASKED_DARK : IMAGE_URL.QUESTION_ASKED;
  const imgSupportSrc = theme === 'dark' ? IMAGE_URL.CONTACT_SUPPORT_DARK : IMAGE_URL.CONTACT_SUPPORT;
  const deployMutation = useWindowsServerDeployMutation({
    onSuccess: () => {
      toast.success('Server deployed successfully');
      form.reset();
      router.push(PAGE_ROUTES.PANEL_TRADING_WINDOWS);
      refetchWindowsServers();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to deploy server');
      form.reset();
    }
  });
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    deployMutation.mutate({
      ...values,
      disk_type: values.disk_type !== undefined ? String(values.disk_type) : undefined
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100
      }
    }
  };

  return (
    <div className='flex flex-col gap-5 lg:gap-7.5'>
      <motion.div
        className='flex items-center justify-between'
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h1 className='text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent'>
            Deploy Windows Server
          </h1>
          <p className='text-muted-foreground mt-2'>Configure and deploy your cloud Windows server in minutes</p>
        </div>
        <div className='flex gap-2'>
          <Link href='/panel/trading/terminals'>
            <Button variant='outline' size='sm' className='group transition-all duration-300 hover:border-primary'>
              <ArrowLeft className='w-4 h-4 mr-2 group-hover:translate-x-[-2px] transition-transform' />
              Back
            </Button>
          </Link>
          <Link href='https://docs.tradevps.net/apis/trading/windows' target='_blank'>
            <Button variant='outline' size='sm' className='group transition-all duration-300 hover:border-primary'>
              <HelpCircle className='w-4 h-4 mr-2 group-hover:rotate-12 transition-transform' />
              How Works?
            </Button>
          </Link>
        </div>
      </motion.div>

      <div className='grid lg:grid-cols-12 gap-5'>
        <motion.div className='lg:col-span-12' variants={containerVariants} initial='hidden' animate='visible'>
          <Card className='border shadow-xs bg-background/50 backdrop-filter backdrop-blur-sm'>
            <CardContent className='p-6'>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-10'>
                  {/* Basic Information Section */}
                  <motion.div variants={containerVariants} className='space-y-6'>
                    <motion.div
                      variants={itemVariants}
                      className='flex items-center space-x-2 border-b border-border/20 pb-2'
                    >
                      <div className='bg-primary/10 p-1.5 rounded-md'>
                        <svg
                          className='h-4 w-4 text-primary'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path
                            d='M21 10H3C2.44772 10 2 10.4477 2 11V17C2 17.5523 2.44772 18 3 18H21C21.5523 18 22 17.5523 22 17V11C22 10.4477 21.5523 10 21 10Z'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                          />
                          <path
                            d='M7 10V6C7 4.89543 7.89543 4 9 4H15C16.1046 4 17 4.89543 17 6V10'
                            stroke='currentColor'
                            strokeWidth='2'
                            strokeLinecap='round'
                          />
                          <path d='M12 14V14.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                        </svg>
                      </div>
                      <h5 className='text-lg font-medium tracking-tight'>Basic Information</h5>
                    </motion.div>
                    <div className='grid md:grid-cols-4 gap-6'>
                      <motion.div variants={itemVariants}>
                        <FormField
                          control={form.control}
                          name='name'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className='text-foreground/80 font-medium'>Server Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='Enter server name'
                                  {...field}
                                  className='border-border/20 focus-visible:ring-1 focus-visible:ring-primary/30 focus-visible:border-primary/50 transition-all'
                                />
                              </FormControl>
                              <FormDescription className='text-xs'>
                                Choose a unique name for your Windows server
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </motion.div>

                      <motion.div variants={itemVariants}>
                        <FormField
                          control={form.control}
                          name='project_id'
                          render={({ field }) => {
                            return (
                              <FormItem className=''>
                                <FormLabel className='text-foreground/80 font-medium'>Project</FormLabel>
                                <Popover open={openProject} onOpenChange={setOpenProject}>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant='outline'
                                        role='combobox'
                                        aria-expanded={openProject}
                                        className='w-full justify-between text-primary bg-background/50 border-border/50 hover:bg-background/80 transition-all'
                                      >
                                        {field.value && projects
                                          ? projects.find((project: IProject) => project.id === field.value)?.name
                                          : 'Select project...'}
                                        <ChevronsUpDown className='h-4 w-4 shrink-0 opacity-50' />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className='w-[--radix-popover-trigger-width] p-0 z-[60]'
                                    align='start'
                                    side='bottom'
                                    sideOffset={4}
                                  >
                                    <Command className='bg-background/95 backdrop-blur-sm'>
                                      <CommandInput placeholder='Search project...' className='h-9' />
                                      <CommandEmpty>No project found.</CommandEmpty>
                                      <CommandGroup>
                                        {projects && Array.isArray(projects)
                                          ? Object.values(
                                              projects.reduce((acc: Record<string, IProject>, project: any) => {
                                                if (!acc[project.id]) {
                                                  acc[project.id] = project;
                                                }
                                                return acc;
                                              }, {})
                                            ).map((project: any) => (
                                              <CommandItem
                                                key={project.id}
                                                value={project.id}
                                                onSelect={() => {
                                                  form.setValue('project_id', project.id);
                                                  setOpenProject(false);
                                                }}
                                              >
                                                <div
                                                  className='mr-2 h-4 w-4 rounded-full'
                                                  style={{ backgroundColor: projectColors[project.id] }}
                                                />
                                                <span>{project.name}</span>
                                                <Check
                                                  className={cn(
                                                    'ml-auto h-4 w-4',
                                                    project.id === field.value ? 'opacity-100' : 'opacity-0'
                                                  )}
                                                />
                                              </CommandItem>
                                            ))
                                          : null}
                                      </CommandGroup>
                                    </Command>
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                                {!isProjectsLoading && (!projects || projects.length === 0) && (
                                  <p className='text-xs text-muted-foreground mt-2'>
                                    No projects found.
                                    <Link href='/panel/projects/create' className='text-primary hover:underline ml-1'>
                                      Create a new project
                                    </Link>
                                  </p>
                                )}
                              </FormItem>
                            );
                          }}
                        />
                      </motion.div>
                      <motion.div variants={itemVariants}>
                        <FormField
                          control={form.control}
                          name='admin_username'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className='text-foreground/80 font-medium'>Admin Username</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='Enter admin username'
                                  {...field}
                                  className='border-border/20 focus-visible:ring-1 focus-visible:ring-primary/30 focus-visible:border-primary/50 transition-all'
                                />
                              </FormControl>
                              <FormDescription className='text-xs'>Username for administrator access</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </motion.div>

                      <motion.div variants={itemVariants}>
                        <FormField
                          control={form.control}
                          name='admin_password'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className='text-foreground/80 font-medium'>Admin Password</FormLabel>
                              <FormControl>
                                <Input
                                  type='password'
                                  placeholder='Enter admin password'
                                  {...field}
                                  className='border-border/20 focus-visible:ring-1 focus-visible:ring-primary/30 focus-visible:border-primary/50 transition-all'
                                />
                              </FormControl>
                              <FormDescription className='text-xs'>
                                Strong password required (min. 12 characters)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </motion.div>
                    </div>

                    <motion.div variants={itemVariants} className='mt-12'>
                      <motion.div
                        variants={itemVariants}
                        className='flex items-center space-x-2 border-b border-border/20 pb-2 mb-4'
                      >
                        <div className='bg-primary/10 p-1.5 rounded-md'>
                          <svg
                            className='h-4 w-4 text-primary'
                            viewBox='0 0 24 24'
                            fill='none'
                            xmlns='http://www.w3.org/2000/svg'
                          >
                            <path
                              d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                            />
                            <path d='M12 2V6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                            <path d='M12 18V22' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                            <path
                              d='M4.93 4.93L7.76 7.76'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                            />
                            <path
                              d='M16.24 16.24L19.07 19.07'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                            />
                            <path d='M2 12H6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                            <path d='M18 12H22' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                            <path
                              d='M4.93 19.07L7.76 16.24'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                            />
                            <path
                              d='M16.24 7.76L19.07 4.93'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                            />
                          </svg>
                        </div>
                        <h5 className='text-lg font-medium tracking-tight'>Region Selection</h5>
                      </motion.div>
                      <FormField
                        control={form.control}
                        name='region'
                        render={({ field }) => (
                          <FormItem>
                            <div className='grid grid-cols-3 gap-3'>
                              {regions.map((region: IRegion) => (
                                <div
                                  key={region.id}
                                  onClick={() => form.setValue('region', region.id)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                      e.preventDefault();
                                      form.setValue('region', region.id);
                                    }
                                  }}
                                  tabIndex={0}
                                  role='button'
                                  aria-pressed={field.value === region.id}
                                  className={cn(
                                    'rounded-md p-3 cursor-pointer transition-all duration-300 flex items-center gap-3 border',
                                    field.value === region.id
                                      ? 'border-primary/40 bg-primary/[0.03] shadow-xs'
                                      : 'border-border/30 hover:border-primary/30'
                                  )}
                                >
                                  <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/50'>
                                    <Image
                                      src={region.icon}
                                      alt={region.label}
                                      width={32}
                                      height={32}
                                      className='h-8 w-8 object-cover rounded-full'
                                    />
                                  </div>
                                  <div className='flex flex-col'>
                                    <span className='font-medium text-sm'>{region.label}</span>
                                    <span className='text-xs text-muted-foreground'>
                                      {getBrokersByRegion(region.id)}
                                    </span>
                                  </div>
                                  {field.value === region.id && (
                                    <div className='ml-auto bg-primary text-primary-foreground rounded-full p-1 shadow-xs'>
                                      <Check className='h-3.5 w-3.5' />
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Windows Version Selection with improved title */}
                  <motion.div variants={containerVariants} className='space-y-6'>
                    <motion.div
                      variants={itemVariants}
                      className='flex items-center space-x-2 border-b border-border/20 pb-2'
                    >
                      <div className='bg-primary/5 p-1 rounded-md'>
                        <svg
                          className='h-4 w-4 text-primary'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <path d='M3 5.1L10.5 4.2V11H3V5.1Z' fill='currentColor' />
                          <path d='M11.5 4.1L21 3V11H11.5V4.1Z' fill='currentColor' />
                          <path d='M21 13L11.5 13V19.9L21 21V13Z' fill='currentColor' />
                          <path d='M10.5 13.1L3 13V18.9L10.5 19.8V13.1Z' fill='currentColor' />
                        </svg>
                      </div>
                      <h5 className='text-lg font-medium tracking-tight'>Windows Version</h5>
                    </motion.div>

                    <FormField
                      control={form.control}
                      name='version'
                      render={({ field }) => (
                        <FormItem>
                          <div className='space-y-4'>
                            <div className='grid grid-cols-1 md:grid-cols-3 gap-3'>
                              {Object.entries(windowsVersionMap)
                                .filter(([key]) =>
                                  [
                                    WindowsVersion.SERVER_2016.toString(),
                                    WindowsVersion.SERVER_2019.toString(),
                                    WindowsVersion.SERVER_2022.toString(),
                                    WindowsVersion.SERVER_2025.toString(),
                                    WindowsVersion.WINDOWS_10_PRO.toString(),
                                    WindowsVersion.WINDOWS_11_PRO.toString()
                                  ].includes(key)
                                )
                                .map(([key, value]) => (
                                  <div
                                    key={key}
                                    onClick={() => form.setValue('version', Number(key))}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        form.setValue('version', Number(key));
                                      }
                                    }}
                                    tabIndex={0}
                                    role='button'
                                    aria-pressed={Number(key) === field.value}
                                    className={cn(
                                      'glass-card rounded-md p-3 cursor-pointer transition-all duration-300 flex items-center gap-3 border',
                                      Number(key) === field.value
                                        ? 'border-primary/40 bg-primary/[0.03] shadow-xs'
                                        : 'border-border/30 hover:border-primary/30 hover:bg-background/60'
                                    )}
                                  >
                                    <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/30'>
                                      <svg
                                        className='h-7 w-7'
                                        viewBox='0 0 24 24'
                                        fill='none'
                                        xmlns='http://www.w3.org/2000/svg'
                                      >
                                        <path d='M3 5.1L10.5 4.2V11H3V5.1Z' fill='currentColor' />
                                        <path d='M11.5 4.1L21 3V11H11.5V4.1Z' fill='currentColor' />
                                        <path d='M21 13L11.5 13V19.9L21 21V13Z' fill='currentColor' />
                                        <path d='M10.5 13.1L3 13V18.9L10.5 19.8V13.1Z' fill='currentColor' />
                                      </svg>
                                    </div>
                                    <div className='flex flex-col'>
                                      <span className='font-medium text-sm'>{value.label}</span>
                                      <span className='text-xs text-muted-foreground'>
                                        {key === WindowsVersion.SERVER_2016.toString() && 'Released 2016 • Stable'}
                                        {key === WindowsVersion.SERVER_2019.toString() && 'Released 2019 • Recommended'}
                                        {key === WindowsVersion.SERVER_2022.toString() && 'Released 2022 • Latest'}
                                        {key === WindowsVersion.SERVER_2025.toString() && 'Preview • Coming Soon'}
                                        {key === WindowsVersion.WINDOWS_11_PRO.toString() && 'Latest Desktop OS'}
                                        {key === WindowsVersion.WINDOWS_10_PRO.toString() && 'Stable Desktop OS'}
                                      </span>
                                    </div>
                                    {Number(key) === field.value && (
                                      <div className='ml-auto bg-primary text-primary-foreground rounded-full p-1 shadow-xs'>
                                        <Check className='h-3.5 w-3.5' />
                                      </div>
                                    )}
                                  </div>
                                ))}
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* Hardware Resources Section with enhanced power indicators and transitions */}
                  <motion.div variants={containerVariants} className='space-y-6'>
                    <motion.div
                      variants={itemVariants}
                      className='flex items-center space-x-2 border-b border-border/20 pb-2'
                    >
                      <div className='bg-primary/10 p-1.5 rounded-md'>
                        <svg
                          className='h-4 w-4 text-primary'
                          viewBox='0 0 24 24'
                          fill='none'
                          xmlns='http://www.w3.org/2000/svg'
                        >
                          <rect x='4' y='4' width='16' height='16' rx='2' stroke='currentColor' strokeWidth='2' />
                          <path d='M9 9H9.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                          <path d='M15 9H15.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                          <path d='M9 15H9.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                          <path d='M15 15H15.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                        </svg>
                      </div>
                      <h5 className='text-lg font-medium tracking-tight'>Hardware Resources</h5>
                    </motion.div>

                    {/* CPU Selection with power indicators */}
                    <FormField
                      control={form.control}
                      name='cpu'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='text-foreground/80 font-medium block'>CPU Cores</FormLabel>
                          <div className='flex flex-nowrap gap-3 overflow-x-auto hide-scrollbar'>
                            {[1, 2, 4, 8, 16, 24, 32].map((cores, index) => {
                              // Calculate opacity based on power (higher cores = higher opacity)
                              const powerOpacity = 0.3 + (index / 6) * 0.7; // 0.3 to 1.0
                              return (
                                <div
                                  key={cores}
                                  onClick={() => form.setValue('cpu', cores)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                      e.preventDefault();
                                      form.setValue('cpu', cores);
                                    }
                                  }}
                                  tabIndex={0}
                                  role='button'
                                  aria-pressed={field.value === cores}
                                  className={cn(
                                    'rounded-md p-3 cursor-pointer transition-all duration-500 flex flex-col items-center gap-2 border text-center flex-shrink-0 w-[100px] relative',
                                    field.value === cores
                                      ? 'border-primary/40 bg-primary/[0.03]'
                                      : 'border-border/30 hover:border-primary/30 hover:bg-background/60'
                                  )}
                                  style={
                                    {
                                      '--power-level': powerOpacity
                                    } as React.CSSProperties
                                  }
                                >
                                  <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/30 bg-background/80 transition-all duration-300'>
                                    <svg
                                      className={cn('h-5 w-5 text-primary transition-all duration-300')}
                                      style={{
                                        opacity: powerOpacity
                                      }}
                                      viewBox='0 0 24 24'
                                      fill='none'
                                      xmlns='http://www.w3.org/2000/svg'
                                    >
                                      <rect
                                        x='4'
                                        y='4'
                                        width='16'
                                        height='16'
                                        rx='2'
                                        stroke='currentColor'
                                        strokeWidth='2'
                                      />
                                      <path d='M9 9H9.01' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path
                                        d='M15 9H15.01'
                                        stroke='currentColor'
                                        strokeWidth='2'
                                        strokeLinecap='round'
                                      />
                                      <path
                                        d='M9 15H9.01'
                                        stroke='currentColor'
                                        strokeWidth='2'
                                        strokeLinecap='round'
                                      />
                                      <path
                                        d='M15 15H15.01'
                                        stroke='currentColor'
                                        strokeWidth='2'
                                        strokeLinecap='round'
                                      />
                                    </svg>
                                  </div>
                                  <div className='flex flex-col'>
                                    <span className='font-medium text-sm'>
                                      {cores}
                                      {' '}
                                      {cores === 1 ? 'Core' : 'Cores'}
                                    </span>
                                    <span className='text-xs text-muted-foreground'>
                                      {cores <= 2 && 'Basic'}
                                      {cores > 2 && cores <= 8 && 'Standard'}
                                      {cores > 8 && 'Performance'}
                                    </span>
                                  </div>
                                  {field.value === cores && (
                                    <div className='absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-xs animate-pulse-minimal'>
                                      <Check className='h-3 w-3' />
                                    </div>
                                  )}
                                  <div
                                    className='absolute bottom-0 left-0 right-0 h-1 rounded-b-md transition-all duration-300'
                                    style={{
                                      background: `linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)))`,
                                      opacity: field.value === cores ? 1 : powerOpacity / 2,
                                      transform: field.value === cores ? 'scaleX(1)' : 'scaleX(0.5)',
                                      transformOrigin: 'left'
                                    }}
                                  />
                                </div>
                              );
                            })}
                          </div>
                          <FormDescription className='text-xs text-muted-foreground opacity-60'>
                            Recommended: 2-4 cores for most trading applications
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Memory Selection with power indicators */}
                    <FormField
                      control={form.control}
                      name='memory'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='text-foreground/80 font-medium block'>Memory</FormLabel>
                          <div className='flex flex-nowrap gap-3 overflow-x-auto hide-scrollbar'>
                            {[2, 4, 8, 16, 32, 64, 128, 256].map((memory, index) => {
                              // Calculate opacity based on power (higher memory = higher opacity)
                              const powerOpacity = 0.3 + (index / 7) * 0.7; // 0.3 to 1.0
                              return (
                                <div
                                  key={memory}
                                  onClick={() => form.setValue('memory', memory)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                      e.preventDefault();
                                      form.setValue('memory', memory);
                                    }
                                  }}
                                  tabIndex={0}
                                  role='button'
                                  aria-pressed={field.value === memory}
                                  className={cn(
                                    'relative rounded-md p-3 cursor-pointer transition-all duration-500 flex flex-col items-center gap-2 border text-center flex-shrink-0 w-[100px]',
                                    field.value === memory
                                      ? 'border-primary/40 bg-primary/[0.03]'
                                      : 'border-border/30 hover:border-primary/30 hover:bg-background/60'
                                  )}
                                  style={
                                    {
                                      '--power-level': powerOpacity
                                    } as React.CSSProperties
                                  }
                                >
                                  <div className='flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-border/30 bg-background/80 transition-all duration-300'>
                                    <svg
                                      className={cn('h-5 w-5 text-primary transition-all duration-300')}
                                      style={{
                                        opacity: powerOpacity
                                      }}
                                      viewBox='0 0 24 24'
                                      fill='none'
                                      xmlns='http://www.w3.org/2000/svg'
                                    >
                                      <rect
                                        x='2'
                                        y='8'
                                        width='20'
                                        height='8'
                                        rx='1'
                                        stroke='currentColor'
                                        strokeWidth='2'
                                      />
                                      <path d='M6 8V6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M10 8V6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M14 8V6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M18 8V6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M6 16V18' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M10 16V18' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M14 16V18' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                      <path d='M18 16V18' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                                    </svg>
                                  </div>
                                  <div className='flex flex-col'>
                                    <span className='font-medium text-sm'>
                                      {memory}
                                      {' '}
                                      GB
                                    </span>
                                    <span className='text-xs text-muted-foreground'>
                                      {memory <= 4 && 'Basic'}
                                      {memory > 4 && memory <= 16 && 'Standard'}
                                      {memory > 16 && 'Performance'}
                                    </span>
                                  </div>
                                  {field.value === memory && (
                                    <div className='absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-xs animate-pulse-minimal'>
                                      <Check className='h-3 w-3' />
                                    </div>
                                  )}
                                  <div
                                    className='absolute bottom-0 left-0 right-0 h-1 rounded-b-md transition-all duration-300'
                                    style={{
                                      background: `linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)))`,
                                      opacity: field.value === memory ? 1 : powerOpacity / 2,
                                      transform: field.value === memory ? 'scaleX(1)' : 'scaleX(0.5)',
                                      transformOrigin: 'left'
                                    }}
                                  />
                                </div>
                              );
                            })}
                          </div>
                          <FormDescription className='text-xs text-muted-foreground opacity-60'>
                            Recommended: 4-8 GB for most trading platforms
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                  <motion.div variants={itemVariants} className='pt-4 flex items-center justify-end'>
                    <Button
                      type='submit'
                      className='w-full md:w-auto px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground'
                      disabled={deployMutation.isPending}
                    >
                      {deployMutation.isPending ? (
                        <>
                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          Deploying...
                        </>
                      ) : (
                        <>Submit Deployment</>
                      )}
                    </Button>
                  </motion.div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Support Cards */}
      <div className='grid lg:grid-cols-2 gap-5 '>
        <SupportCard
          title='Questions?'
          description='Visit our Help Center for detailed assistance on billing, payments, and subscriptions.'
          linkText='Go to Help Center'
          linkHref='https://docs.tradevps.net'
          imageSrc={imgQuestionSrc}
        />

        <SupportCard
          title='Contact Support'
          description='Need assistance? Contact our support team for prompt, personalized help your queries & concerns.'
          linkText='Contact Support'
          linkHref='/panel/support'
          imageSrc={imgSupportSrc}
        />
      </div>
    </div>
  );
}

// Support Card Component remains the same
function SupportCard({
  title,
  description,
  linkText,
  linkHref,
  imageSrc
}: {
  title: string;
  description: string;
  linkText: string;
  linkHref: string;
  imageSrc: string;
}) {
  return (
    <Card className='pb-0 gap-1'>
      <CardContent className='px-5 h-[180px] mb-0 pb-0'>
        <div className='flex flex-wrap md:flex-nowrap items-center gap-3 md:gap-10'>
          <div className='flex flex-col items-start gap-3'>
            <h2 className='text-primary '>{title}</h2>
            <p className='text-muted-foreground '>{description}</p>
          </div>
          <Image src={imageSrc} alt={description} width={120} height={140} />
        </div>
      </CardContent>
      <Separator />
      <CardFooter className='flex justify-center items-center pb-1 mt-0'>
        <Link
          href={linkHref}
          className='border-b-2 text-sm border-dotted border-gray-500 my-1 text-primary hover:text-blue-400 hover:border-blue-400'
        >
          {linkText}
        </Link>
      </CardFooter>
    </Card>
  );
}
