import type { DateRange } from 'react-day-picker';
import { CalendarIcon, Search, X } from 'lucide-react';

import * as React from 'react';
import DropDownComponent from '@/components/dropDown-field';
import { statusItems } from '@/components/panel/trading/terminals/table-statuses';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

import { useProjectsList } from '@/services/api/user/projects';
import { useRegionsFetch } from '@/services/api/user/trading/terminals';
import { getConsistentColor } from '@/utils/avatar';

type IComponentProps = {
  region: string[];
  setRegion: React.Dispatch<React.SetStateAction<string[]>>;
  project: string[];
  setProject: React.Dispatch<React.SetStateAction<string[]>>;
  selectedStatus: string[];
  setSelectedStatus: React.Dispatch<React.SetStateAction<string[]>>;
  setFilterValue: React.Dispatch<React.SetStateAction<string>>;
  selectedDateRange: DateRange | undefined;
  setSelectedDateRange: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  filterValue: string;
};
const TradingTerminalFiltersComponent = ({
  filterValue,
  setFilterValue,
  selectedDateRange,
  setSelectedDateRange,
  selectedStatus,
  setSelectedStatus,
  region,
  setRegion,
  project,
  setProject
}: IComponentProps) => {
  const { data: regionsResponse, isLoading: isRegionsLoading } = useRegionsFetch();
  const { data: projectsResponse, isLoading: isProjectsLoading } = useProjectsList();

  const regionItems = Array.isArray(regionsResponse)
    ? regionsResponse?.map((item) => {
        return { label: item?.label, id: item?.id, icon: item?.icon };
      })
    : [];
  const projectsItems = Array.isArray(projectsResponse)
    ? projectsResponse.map((item) => {
        return { label: item?.name, id: item?.id };
      })
    : [];
  const statuses = statusItems?.map((item) => {
    return { label: item?.value, id: item?.key, icon: item?.icon };
  });
  const projects = React.useMemo(() => {
    return (
      projectsItems?.map((project: any) => ({
        id: project?.id,
        label: project?.label,
        icon: (
          <div
            className={cn(
              'flex h-4.5 w-4.5 shrink-0 items-center justify-center rounded-full text-xs font-medium text-white',
              getConsistentColor(project.label)
            )}
          >
            {project.label.charAt(0).toUpperCase()}
          </div>
        )
      })) || []
    );
  }, [projectsItems]);

  return (
    <div className='w-full flex items-center'>
      <div className='w-1/5'>
        <div className='relative group'>
          <div className='relative flex items-center bg-background/80 backdrop-blur-none border border-primary/10 rounded-md pl-4 pr-2 h-10'>
            <Search className='h-4 w-4 text-muted-foreground/60' />
            <Input
              placeholder='Search Terminals...'
              className='border-0 bg-transparent px-3 h-9 placeholder:text-muted-foreground'
              style={{
                WebkitAppearance: 'none',
                MozAppearance: 'none',
                appearance: 'none',
                outline: 'none',
                boxShadow: 'none'
              }}
              value={filterValue}
              onChange={e => setFilterValue(e.target.value)}
            />
            {filterValue?.trim() ? (
              <button
                type='button'
                onClick={() => setFilterValue('')}
                className='absolute
          hover:scale-105 hover:text-gray-600
           cursor-pointer
           right-3
            top-1/2
            -translate-y-1/2
             text-muted-foreground
             hover-
             '
              >
                x
              </button>
            ) : null}
          </div>
        </div>
      </div>

      <div className='w-4/5 flex justify-end items-center gap-3'>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              className='w-[220px] flex justify-between pl-3 text-left font-normal text-muted-foreground text-sm'
            >
              <p>
                {selectedDateRange?.from && selectedDateRange?.to
                  ? `${selectedDateRange.from.toLocaleDateString()} - ${selectedDateRange.to.toLocaleDateString()}`
                  : 'Select Date Range'}
              </p>

              <div className='flex items-center'>
                {selectedDateRange?.from && (
                  <button
                    type='button'
                    onMouseDown={e => e.stopPropagation()}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedDateRange(undefined);
                    }}
                    className='w-4 h-4 opacity-70 cursor-pointer'
                    aria-label='Clear selected date'
                  >
                    <X className='w-4 h-4' />
                  </button>
                )}

                <CalendarIcon className='w-4 h-4 opacity-70 ms-1' />
              </div>
            </Button>
          </PopoverTrigger>

          <PopoverContent className='w-auto p-0' align='start'>
            <Calendar
              initialFocus
              mode='range'
              defaultMonth={selectedDateRange?.from}
              selected={selectedDateRange}
              onSelect={setSelectedDateRange}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
        <DropDownComponent
          loading={isRegionsLoading}
          title='Region'
          items={regionItems}
          selectedItems={region}
          setSelectedItems={setRegion}
          showIcon={true}
        />
        <DropDownComponent
          loading={isProjectsLoading}
          title='Project'
          items={projects}
          selectedItems={project}
          setSelectedItems={setProject}
          showIcon={true}
        />
        <DropDownComponent
          badgSize={3.5}
          title='Status'
          items={statuses}
          selectedItems={selectedStatus}
          setSelectedItems={setSelectedStatus}
          showIcon={true}
        />
      </div>
    </div>
  );
};

export default TradingTerminalFiltersComponent;
