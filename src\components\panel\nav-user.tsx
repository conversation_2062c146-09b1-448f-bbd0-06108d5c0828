import { useLogout } from 'hooks/user/logout';
import { ChevronsUpDown, CreditCardIcon, FolderIcon, LogOutIcon, UserIcon } from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { PAGE_ROUTES } from '@/configs/page-routes';

type NavUserProps = {
  user: {
    name: string;
    email: string;
    avatar: string;
    avatarFallback: {
      initials: string;
      bgColor: string;
    };
  };
};

export function NavUser({ user }: NavUserProps) {
  const { logout } = useLogout();

  const handleLogout = (event: Event) => {
    event.preventDefault();
    logout();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          type='button'
          className='flex w-full items-center px-3 py-2 hover:bg-accent hover:text-accent-foreground'
        >
          <div className='flex items-center gap-3 flex-1'>
            <Avatar>
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback style={{ backgroundColor: user.avatarFallback.bgColor }}>
                {user.avatarFallback.initials}
              </AvatarFallback>
            </Avatar>
            <div className='flex flex-col items-start flex-1'>
              <span className='text-sm font-medium'>{user.name}</span>
              <span className='text-xs text-muted-foreground'>{user.email}</span>
            </div>
            <ChevronsUpDown className='ml-auto size-4 text-muted-foreground' />
          </div>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-56 mb-4' side='right' align='start' alignOffset={-8} sideOffset={8}>
        <DropdownMenuLabel className='font-normal'>
          <div className='flex flex-col space-y-1'>
            <div className='flex items-center gap-2'>
              <Avatar className='h-8 w-8'>
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback style={{ backgroundColor: user.avatarFallback.bgColor }}>
                  {user.avatarFallback.initials}
                </AvatarFallback>
              </Avatar>
              <div className='flex flex-col space-y-1'>
                <p className='text-sm font-medium leading-none'>{user.name}</p>
                <p className='text-xs leading-none text-muted-foreground'>{user.email}</p>
              </div>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href={PAGE_ROUTES.PANEL_ACCOUNT} className='flex items-center'>
              <UserIcon className='mr-2 h-4 w-4' />
              Account settings
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href={PAGE_ROUTES.PANEL_BILLING} className='flex items-center'>
              <CreditCardIcon className='mr-2 h-4 w-4' />
              Billing
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href={PAGE_ROUTES.PANEL_PROJECTS} className='flex items-center'>
              <FolderIcon className='mr-2 h-4 w-4' />
              Projects
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem className='text-red-600 focus:text-red-600 focus:bg-red-100' onSelect={handleLogout}>
          <LogOutIcon className='mr-2 h-4 w-4' />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
