import * as React from 'react';
import { Button } from '@/components/ui/button';

// type CreateTicketForm = {
//   setCreateNewTicket?: React.Dispatch<React.SetStateAction<boolean>>;
// };
const EmptyList = () => {
  return (
    <div className='hidden  sm:block mt-24 text-center bg-blue-100'>
      <svg
        className='mx-auto h-12 w-12 text-gray-400'
        fill='none'
        viewBox='0 0 24 24'
        stroke='currentColor'
        aria-hidden='true'
      >
        <path d='M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155'></path>
      </svg>
      <h3 className='mt-2 text-sm font-semibold text-form-title'>Select ticket to see messages.</h3>
      <p className='mt-1 text-sm text-form-desc'>
        To see ticket messages, select ticket from tickets list in left panel.
      </p>
      <div className='mt-6 mx-auto w-auto max-w-[6rem]'>
        <Button
          variant='outline'
          type='button'
          //  onClick={() => setCreateNewTicket(true)}
        >
          New Ticket
        </Button>
      </div>
    </div>
  );
};

export default EmptyList;
