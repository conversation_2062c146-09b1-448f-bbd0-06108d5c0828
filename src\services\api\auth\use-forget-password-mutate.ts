import type { APIError, IForgetPasswordParams, IForgetPasswordResponse } from '@tradevpsnet/client';
import type { IUseMutationFactoryProps } from 'hooks/api/use-mutation-factory/use-mutation-factory.type';
import type { IAuthentication } from 'types/auth';
import { useMutation } from '@tanstack/react-query';
import { Client } from '@tradevpsnet/client';
import { API_ROUTES } from 'configs/api-routes';
import { useMutationFactory } from 'hooks/api/use-mutation-factory';

export const useForgetPasswordWithEmailMutate = (
  options?: Pick<IUseMutationFactoryProps<IAuthentication>, 'refetchQueries' | 'onSuccess' | 'onError'>
) => {
  return useMutationFactory<IAuthentication>({
    url: API_ROUTES.AUTH_FORGET_PASSWORD,
    method: 'POST',
    ...options
  });
};

export const useForgetpasswordClientMutate = (options?: {
  onSuccess?: (data: IForgetPasswordResponse) => void;
  onError?: (error: APIError) => void;
}) => {
  const client = new Client('', process.env.NEXT_PUBLIC_SERVER_URL);

  return useMutation<IForgetPasswordResponse, APIError, IForgetPasswordParams>({
    mutationFn: (params: IForgetPasswordParams) => client.auth.forget_password(params),
    ...options
  });
};
