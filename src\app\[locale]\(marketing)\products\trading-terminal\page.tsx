'use client';

import { motion } from 'framer-motion';
import {
  ArrowRight,
  Building2,
  Calendar,
  Check,
  CheckCircle2,
  Cloud,
  Cpu,
  CreditCard,
  Globe,
  LayoutDashboard,
  Lock,
  MonitorSmartphone,
  Phone,
  Server,
  Shield,
  Star,
  Terminal,
  Workflow,
  Zap
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PAGE_ROUTES } from '@/configs/page-routes';
import HeroImage from '@/public/img/forex-trade-vps-metatrader-5-screenshot.png';

type IFeatureCardProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
};

type IBentoCardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
  className?: string;
  size?: 'default' | 'large';
  index: number;
  features?: string[];
  image?: string;
};

const FeatureCard: React.FC<IFeatureCardProps> = ({ icon, title, description }) => {
  return (
    <Card className='p-6 rounded-sm hover:shadow-md transition-shadow'>
      <CardContent>
        <div className='text-primary mb-4'>{icon}</div>
        <h3 className='text-xl font-semibold mb-2'>{title}</h3>
        <p className='text-gray-600'>{description}</p>
      </CardContent>
    </Card>
  );
};

const BentoCard: React.FC<IBentoCardProps> = ({
  title,
  description,
  icon,
  className = '',
  size = 'default',
  index,
  features = [],
  image
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: index * 0.15,
        ease: [0.22, 1, 0.36, 1] as any
      }
    },
    hover: {
      scale: 1.01,
      transition: {
        duration: 0.3,
        ease: [0.22, 1, 0.36, 1] as any
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: 0.2
      }
    }
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: 1.1 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1] as any
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.4,
        ease: [0.22, 1, 0.36, 1] as any
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial='hidden'
      whileInView='visible'
      whileHover='hover'
      viewport={{ once: true, margin: '-100px' }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={`
        group relative overflow-hidden rounded-sm
        bg-white dark:bg-gray-900
        border border-gray-200 dark:border-gray-800
        shadow-sm hover:shadow-md
        transition-shadow duration-300
        ${size === 'large' ? 'md:col-span-2 md:row-span-2' : ''}
        ${className}
      `}
    >
      {/* Subtle gradient overlay */}
      <div className='absolute inset-0 bg-gradient-to-br from-primary/[0.03] via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

      <div className='relative h-full p-6 flex flex-col'>
        {/* Header section with icon */}
        <motion.div
          variants={{
            hidden: { opacity: 0, x: -20 },
            visible: {
              opacity: 1,
              x: 0,
              transition: {
                duration: 0.4,
                ease: 'easeOut'
              }
            }
          }}
          className='flex items-center gap-3 mb-4'
        >
          <div className='relative'>
            <div className='w-10 h-10 rounded-sm bg-primary/5 text-primary flex items-center justify-center'>
              {icon}
            </div>
            <motion.div
              animate={isHovered ? { scale: 1.2, opacity: 0 } : { scale: 1, opacity: 0.5 }}
              transition={{ duration: 0.3 }}
              className='absolute inset-0 bg-primary/10 rounded-sm'
            />
          </div>
          <h3 className='text-lg font-semibold text-gray-900 dark:text-white'>{title}</h3>
        </motion.div>

        {/* Image section */}
        {image && (
          <motion.div variants={imageVariants} className='relative w-full h-48 mb-4 overflow-hidden rounded-sm'>
            <Image
              src={image}
              alt={title}
              fill
              className='object-cover'
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
            />
            <div className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent' />
          </motion.div>
        )}

        {/* Content section */}
        <motion.div variants={contentVariants}>
          <p className='text-gray-600 dark:text-gray-400 text-sm mb-4'>{description}</p>
        </motion.div>

        {/* Features list with staggered animation */}
        {features.length > 0 && (
          <motion.ul className='mt-auto space-y-2' initial='hidden' animate={isHovered ? 'visible' : 'hidden'}>
            {features.map((feature, idx) => (
              <motion.li
                key={idx}
                variants={{
                  hidden: { opacity: 0, x: -10, filter: 'blur(4px)' },
                  visible: {
                    opacity: 1,
                    x: 0,
                    filter: 'blur(0px)',
                    transition: {
                      duration: 0.3,
                      delay: idx * 0.1,
                      ease: [0.22, 1, 0.36, 1] as any
                    }
                  }
                }}
                className='flex items-center text-sm text-gray-600 dark:text-gray-400'
              >
                <CheckCircle2 className='h-4 w-4 mr-2 text-primary' />
                {feature}
              </motion.li>
            ))}
          </motion.ul>
        )}
      </div>
    </motion.div>
  );
};

const CircleFeature = ({
  position,
  icon,
  text,
  delay
}: {
  position: string;
  icon: React.ReactNode;
  text: string;
  delay: number;
}) => (
  <motion.div
    initial={{ scale: 0, opacity: 0 }}
    animate={{ scale: 1, opacity: 1 }}
    transition={{
      type: 'spring',
      stiffness: 260,
      damping: 20,
      delay
    }}
    className={`absolute ${position} z-20`}
  >
    <div className='relative'>
      {/* Circle */}
      <div className='size-12 rounded-full bg-white shadow-lg flex items-center justify-center'>{icon}</div>
      {/* Text */}
      <div className='absolute whitespace-nowrap bg-white rounded-full px-3 py-1 text-sm font-medium shadow-md mt-2'>
        {text}
      </div>
    </div>
  </motion.div>
);

const ConnectingLines = () => (
  <svg className='absolute inset-0 w-full h-full z-10' style={{ transform: 'scale(0.9)' }}>
    <motion.path
      d='M 200,250 L 350,150 L 500,250 L 350,350 L 200,250'
      fill='none'
      stroke='rgba(0,0,0,0.1)'
      strokeWidth='2'
      strokeDasharray='5,5'
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 2, delay: 0.5 }}
    />
  </svg>
);

export default function TradingTerminalPage() {
  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      document.documentElement.style.setProperty('--mouse-x', `${e.clientX}px`);
      document.documentElement.style.setProperty('--mouse-y', `${e.clientY}px`);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className='relative'>
      {/* Hero Section */}
      <section className='relative min-h-[90vh] flex items-center'>
        {/* Background Elements */}
        <div className='absolute inset-0 -z-10'>
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_top_right,_var(--primary)_0%,_transparent_60%)] opacity-10' />
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,_var(--secondary)_0%,_transparent_60%)] opacity-10' />
          <div className='absolute inset-0 bg-grid-primary/[0.02] bg-[size:20px_20px]' />
        </div>

        <div className='container mx-auto px-4 pb-20'>
          <div className='grid lg:grid-cols-2 gap-12 items-center'>
            {/* Left Column - Text Content */}
            <div className='relative z-10'>
              {/* Announcement Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className='inline-flex items-center gap-2 bg-primary/5 border border-primary/10 rounded-full px-4 py-1.5 mb-8'
              >
                <span className='flex h-2 w-2 rounded-full bg-primary animate-pulse' />
                <span className='text-xs font-medium'>
                  New: Advanced MT5 Support Released
                  <Link href='/blog/mt5-release' className='text-primary hover:underline ml-2'>
                    Learn more →
                  </Link>
                </span>
              </motion.div>

              {/* Main Heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className='relative'
              >
                <h1 className='text-5xl md:text-6xl font-bold leading-tight mb-6'>
                  Trade Smarter with
                  <span className='relative whitespace-nowrap'>
                    <span className='relative z-10 gradient-text'> Cloud Terminal</span>
                    <svg
                      aria-hidden='true'
                      viewBox='0 0 418 42'
                      className='absolute left-0 top-2/3 h-[0.6em] w-full fill-primary/20'
                      preserveAspectRatio='none'
                    >
                      <path d='M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z' />
                    </svg>
                  </span>
                </h1>
              </motion.div>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className='text-xl text-gray-600 mb-8'
              >
                Experience professional-grade trading with ultra-low latency and enterprise security. Trade from
                anywhere, on any device.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className='flex flex-wrap gap-4 mb-12'
              >
                <Button size='lg' className='rounded-sm px-8 group'>
                  Start Free Trial
                  <ArrowRight className='ml-2 h-4 w-4 transition-transform group-hover:translate-x-1' />
                </Button>
                <Button size='lg' variant='outline' className='rounded-sm px-8'>
                  View Demo
                </Button>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className='grid grid-cols-3 gap-8'
              >
                <div className='text-center'>
                  <div className='text-3xl font-bold gradient-text mb-2'>0.8ms</div>
                  <div className='text-sm text-gray-600'>Average Latency</div>
                </div>
                <div className='text-center'>
                  <div className='text-3xl font-bold gradient-text mb-2'>99.9%</div>
                  <div className='text-sm text-gray-600'>Uptime</div>
                </div>
                <div className='text-center'>
                  <div className='text-3xl font-bold gradient-text mb-2'>500+</div>
                  <div className='text-sm text-gray-600'>Active Traders</div>
                </div>
              </motion.div>
            </div>

            {/* Right Column - Image with Circles */}
            <div className='relative'>
              {/* Connecting Lines SVG */}
              <ConnectingLines />

              {/* Main Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className='relative z-10'
              >
                <div className='relative rounded-xl shadow-2xl overflow-hidden'>
                  <Image
                    src={HeroImage}
                    alt='Trading Terminal Interface'
                    width={700}
                    height={500}
                    className='w-full h-auto'
                    priority
                  />
                </div>
              </motion.div>

              {/* Circular Features */}
              <CircleFeature
                position='top-0 left-1/4 -translate-y-1/2'
                icon={<Zap className='h-6 w-6 text-primary' />}
                text='Ultra-Fast Execution'
                delay={0.2}
              />

              <CircleFeature
                position='top-1/4 right-0 translate-x-1/2'
                icon={<Shield className='h-6 w-6 text-primary' />}
                text='Enterprise Security'
                delay={0.4}
              />

              <CircleFeature
                position='bottom-1/4 right-0 translate-x-1/2'
                icon={<Cloud className='h-6 w-6 text-primary' />}
                text='Cloud Native'
                delay={0.6}
              />

              <CircleFeature
                position='bottom-0 left-1/4 translate-y-1/2'
                icon={<Globe className='h-6 w-6 text-primary' />}
                text='Global Access'
                delay={0.8}
              />

              <CircleFeature
                position='top-1/4 -left-6'
                icon={<Cpu className='h-6 w-6 text-primary' />}
                text='Advanced Analytics'
                delay={1.0}
              />

              {/* Background Decorative Element */}
              <div className='absolute -z-10 inset-0 transform translate-x-4 translate-y-4'>
                <div className='absolute inset-0 bg-primary/5 rounded-xl' />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className='py-16 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>Why Choose Our Terminal?</h2>
            <p className='text-gray-600'>Experience trading like never before with our advanced features</p>
          </div>
          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-6'>
            <FeatureCard
              icon={<Cloud className='h-6 w-6' />}
              title='Cloud-Native Platform'
              description='Access your terminal from any device, anywhere in the world'
            />
            <FeatureCard
              icon={<Zap className='h-6 w-6' />}
              title='Ultra-Low Latency'
              description='Execute trades with sub-millisecond response times'
            />
            <FeatureCard
              icon={<Lock className='h-6 w-6' />}
              title='Enterprise Security'
              description='Bank-grade encryption and security protocols'
            />
            <FeatureCard
              icon={<CreditCard className='h-6 w-6' />}
              title='Pay As You Go'
              description='Only pay for what you use with flexible pricing plans'
            />
            <FeatureCard
              icon={<MonitorSmartphone className='h-6 w-6' />}
              title='Multi-Device Support'
              description='Seamlessly switch between desktop and mobile devices'
            />
            <FeatureCard
              icon={<Cpu className='h-6 w-6' />}
              title='Advanced Analytics'
              description='Real-time market analysis and trading signals'
            />
            <FeatureCard
              icon={<Globe className='h-6 w-6' />}
              title='Global Access'
              description='Connect to multiple brokers and markets worldwide'
            />
          </div>
        </div>
      </section>

      {/* Bento-style MetaPylot Section */}
      <section className='py-24 relative overflow-hidden'>
        {/* Background gradients */}
        <div className='absolute inset-0'>
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_top_right,_var(--primary-foreground)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,_var(--secondary)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-grid-primary/[0.03] bg-[size:20px_20px]' />
        </div>

        <div className='container mx-auto px-4 relative'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className='text-center mb-16'
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className='inline-block mb-4'
            >
              <span className='bg-primary/10 text-primary px-6 py-2 rounded-full text-sm font-medium'>
                Powered by MetaPylot
              </span>
            </motion.div>
            <h2 className='text-4xl md:text-5xl font-bold mb-6'>
              Advanced
              {' '}
              <span className='gradient-text'>MetaTrader</span>
              {' '}
              Management
            </h2>
            <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
              MetaPylot simplifies MetaTrader instance management with enterprise-grade reliability.
            </p>
          </motion.div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto'>
            <BentoCard
              index={0}
              size='large'
              icon={<Terminal className='h-5 w-5' />}
              title='Trading Terminal'
              description='Advanced trading platform with professional-grade tools and real-time market data analysis.'
              image='/img/trading-terminal-dashboard.jpg'
              features={['Real-time market data', 'Advanced charting', 'Multi-asset trading', 'Risk management tools']}
            />

            <BentoCard
              index={1}
              icon={<Server className='h-5 w-5' />}
              title='VPS Infrastructure'
              description='Enterprise-grade hosting solution optimized for high-frequency trading.'
              image='/img/server-infrastructure.jpg'
              features={['Low latency connections', '99.9% uptime guarantee', 'Global deployment options']}
            />

            <BentoCard
              index={2}
              icon={<Shield className='h-5 w-5' />}
              title='Security First'
              description='Bank-grade security protocols protecting your trading operations.'
              image='/img/security-dashboard.jpg'
              features={['End-to-end encryption', 'Multi-factor authentication', '24/7 monitoring']}
            />

            <BentoCard
              index={3}
              icon={<Globe className='h-5 w-5' />}
              title='Global Access'
              description='Trade from anywhere with our distributed network of servers.'
              image='https://tailwindcss.com/plus-assets/img/component-images/bento-01-network.png'
              features={['Multiple locations', 'Load balancing', 'Geographic redundancy']}
            />

            <BentoCard
              index={4}
              icon={<Workflow className='h-5 w-5' />}
              title='Automated Trading'
              description='Set up and manage automated trading strategies with ease.'
              image='/img/automated-trading.jpg'
              features={['Expert Advisors support', 'Custom script deployment', 'Strategy backtesting']}
            />

            <BentoCard
              index={5}
              icon={<LayoutDashboard className='h-5 w-5' />}
              title='Analytics Dashboard'
              description='Comprehensive trading analytics and performance metrics.'
              image='/img/analytics-dashboard.jpg'
              features={['Performance tracking', 'Risk analysis', 'Custom reporting']}
            />
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
            className='mt-16 text-center'
          >
            <Link href={PAGE_ROUTES.REGISTER}>
              <Button
                size='lg'
                className='rounded-sm px-8 py-6 text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105'
              >
                Start Using MetaPylot
                <ArrowRight className='ml-2 h-5 w-5 animate-pulse' />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className='relative py-32'>
        {/* Subtle background pattern */}
        <div className='absolute inset-0 bg-[linear-gradient(45deg,var(--primary)/0.02_1px,transparent_1px)] bg-[size:32px_32px]' />

        <div className='container mx-auto px-4 relative'>
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className='text-center max-w-3xl mx-auto mb-20'
          >
            <div className='inline-flex items-center gap-2 bg-primary/[0.03] border border-primary/10 rounded-full px-3 py-1 mb-6'>
              <div className='w-2 h-2 rounded-full bg-primary animate-pulse' />
              <span className='text-sm font-medium text-primary'>Enterprise-Grade Trading Solutions</span>
            </div>
            <h2 className='text-4xl font-bold tracking-tight mb-6'>Professional Trading Infrastructure</h2>
            <p className='text-gray-600 text-lg leading-relaxed'>
              Choose a plan that scales with your trading operations. From individual traders to institutional clients,
              we provide the technology you need to succeed in the markets.
            </p>
          </motion.div>

          {/* Pricing Grid */}
          <div className='max-w-7xl mx-auto grid lg:grid-cols-2 gap-8 items-start'>
            {/* Professional Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className='relative group'
            >
              <div
                className='absolute inset-0 bg-gradient-to-b from-white to-primary/[0.02] rounded-2xl transform
                transition-transform duration-300 group-hover:scale-[1.02]'
              />

              <div className='relative p-2'>
                <div className='bg-white rounded-xl p-8 border border-gray-100 shadow-sm'>
                  <div className='flex justify-between items-start mb-8'>
                    <div>
                      <div className='flex items-center gap-2 mb-2'>
                        <Terminal className='w-5 h-5 text-primary' />
                        <h3 className='text-xl font-semibold'>Professional Terminal</h3>
                      </div>
                      <p className='text-gray-600'>Advanced trading capabilities</p>
                    </div>
                    <div className='flex flex-col items-end'>
                      <div className='text-3xl font-bold'>$3.99</div>
                      <div className='text-gray-500 text-sm'>per month</div>
                    </div>
                  </div>

                  <div className='space-y-6 mb-8'>
                    <div>
                      <div className='text-sm font-medium text-gray-900 mb-4'>Included Features</div>
                      <ul className='space-y-4'>
                        {[
                          { title: 'Professional Trading Terminal', desc: 'Full-featured MetaTrader workspace' },
                          { title: 'Real-Time Market Data', desc: 'Live price feeds and market updates' },
                          { title: 'Technical Analysis Tools', desc: 'Advanced charting and indicators' },
                          { title: '24/7 Technical Support', desc: 'Expert assistance whenever you need' },
                          { title: 'Mobile Trading Access', desc: 'Trade from any device, anywhere' }
                        ].map((feature, index) => (
                          <motion.li
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{ delay: index * 0.1 }}
                            className='flex gap-4'
                          >
                            <div className='flex-shrink-0 mt-1'>
                              <div className='w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center'>
                                <Check className='w-3 h-3 text-primary' />
                              </div>
                            </div>
                            <div>
                              <div className='font-medium text-gray-900'>{feature.title}</div>
                              <div className='text-sm text-gray-500'>{feature.desc}</div>
                            </div>
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <Button
                    size='lg'
                    className='w-full bg-primary hover:bg-primary/90 text-white rounded-lg h-12 font-medium
                      transition-all duration-200 group relative overflow-hidden'
                  >
                    <span className='relative z-10 flex items-center justify-center gap-2'>
                      Start Free Trial
                      <ArrowRight className='w-4 h-4 transition-transform group-hover:translate-x-1' />
                    </span>
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Enterprise Plan - Premium Design */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className='relative group lg:mt-8'
            >
              {/* Animated gradient background */}
              <div className='absolute inset-0 bg-[linear-gradient(110deg,#000103,#1E2631,#000103)] rounded-2xl'>
                <div className="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20" />
                <div className='absolute inset-0 bg-gradient-to-t from-primary/10 via-transparent to-transparent' />

                {/* Animated glowing orb effect */}
                <div className='absolute -top-10 -right-10 w-40 h-40 bg-primary/20 rounded-full blur-3xl animate-pulse' />
                <div className='absolute -bottom-10 -left-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000' />
              </div>

              <div className='relative p-2'>
                <div className='relative rounded-xl p-8 backdrop-blur-sm border border-white/10'>
                  {/* Premium badge */}
                  <div className='absolute -top-5 right-8'>
                    <div className='bg-gradient-to-r from-primary to-blue-500 p-[1px] rounded-full'>
                      <div className='bg-gray-900 px-4 py-1 rounded-full'>
                        <div className='flex items-center gap-2'>
                          <Star className='w-4 h-4 text-primary fill-primary' />
                          <span className='text-xs font-semibold bg-gradient-to-r from-primary to-blue-500 text-transparent bg-clip-text'>
                            ENTERPRISE SUITE
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Header */}
                  <div className='mb-12'>
                    <div className='flex items-center gap-3 mb-4'>
                      <div className='bg-primary/10 p-2 rounded-lg'>
                        <Building2 className='w-6 h-6 text-primary' />
                      </div>
                      <h3 className='text-2xl font-bold bg-gradient-to-r from-white to-gray-300 text-transparent bg-clip-text'>
                        Enterprise Suite
                      </h3>
                    </div>
                    <p className='text-gray-400 text-lg'>
                      Institutional-grade infrastructure for professional trading operations
                    </p>
                  </div>

                  {/* Premium features grid */}
                  <div className='grid grid-cols-2 gap-6 mb-12'>
                    {[
                      {
                        icon: <Shield className='w-5 h-5' />,
                        title: 'Enterprise Security',
                        desc: 'Bank-grade protection'
                      },
                      {
                        icon: <Server className='w-5 h-5' />,
                        title: 'Dedicated Hardware',
                        desc: 'High-performance servers'
                      },
                      {
                        icon: <Zap className='w-5 h-5' />,
                        title: 'Ultra-Low Latency',
                        desc: 'Sub-millisecond execution'
                      },
                      {
                        icon: <MonitorSmartphone className='w-5 h-5' />,
                        title: 'Multi-Platform',
                        desc: 'Trade on any device'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: index * 0.1 }}
                        className='group/item relative'
                      >
                        <div
                          className='absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-lg
                          opacity-0 group-hover/item:opacity-100 transition-opacity duration-300'
                        />
                        <div className='relative p-4 rounded-lg border border-white/5'>
                          <div className='flex items-center gap-3 mb-2'>
                            <div className='text-primary'>{feature.icon}</div>
                            <h4 className='font-semibold text-white'>{feature.title}</h4>
                          </div>
                          <p className='text-sm text-gray-400 ml-8'>{feature.desc}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Enterprise capabilities */}
                  <div className='space-y-6 mb-12'>
                    <div className='flex items-center gap-2 text-lg font-semibold text-white mb-6'>
                      <Lock className='w-5 h-5 text-primary' />
                      Enterprise Capabilities
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                      {[
                        'Unlimited Trading Terminals',
                        'Custom Integration APIs',
                        'Dedicated Infrastructure',
                        'Advanced Risk Management',
                        '24/7 Priority Support',
                        'Custom Development',
                        'Real-time Analytics',
                        'Regulatory Compliance'
                      ].map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -10 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ delay: index * 0.1 }}
                          className='flex items-center gap-3'
                        >
                          <div className='w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center'>
                            <Check className='w-3 h-3 text-primary' />
                          </div>
                          <span className='text-sm text-gray-300'>{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* CTA Section */}
                  <div className='space-y-4'>
                    <Button
                      size='lg'
                      className='w-full bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90
                        text-white rounded-sm h-14 font-medium transition-all duration-200 group relative overflow-hidden'
                    >
                      <span className='relative z-10 flex items-center justify-center gap-2 text-base'>
                        Contact Enterprise Sales
                        <Phone className='w-5 h-5 transition-transform group-hover:rotate-12' />
                      </span>
                    </Button>

                    {/* Premium support indicator */}
                    <div className='flex items-center justify-center gap-2 text-sm text-gray-400'>
                      <Calendar className='w-4 h-4' />
                      <span>Dedicated account manager within 24 hours</span>
                    </div>
                  </div>

                  {/* Enterprise clients */}
                  <div className='mt-12 pt-8 border-t border-white/5'>
                    <div className='text-sm text-gray-500 text-center mb-4'>Trusted by Global Institutions</div>
                    <div className='flex justify-center items-center gap-8'>
                      {/* Replace with actual client logos */}
                      <div className='w-12 h-12 rounded-full bg-white/5'></div>
                      <div className='w-12 h-12 rounded-full bg-white/5'></div>
                      <div className='w-12 h-12 rounded-full bg-white/5'></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className='mt-20 text-center'
          >
            <div className='inline-flex items-center gap-8 px-6 py-3 bg-gray-50 rounded-full'>
              <div className='flex items-center gap-2'>
                <Shield className='w-5 h-5 text-primary' />
                <span className='text-sm text-gray-600'>Bank-grade Security</span>
              </div>
              <div className='flex items-center gap-2'>
                <Server className='w-5 h-5 text-primary' />
                <span className='text-sm text-gray-600'>99.9% Uptime</span>
              </div>
              <div className='flex items-center gap-2'>
                <Globe className='w-5 h-5 text-primary' />
                <span className='text-sm text-gray-600'>Global Infrastructure</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* New CTA Section */}
      <section className='relative py-24 overflow-hidden'>
        {/* Animated Background */}
        <div className='absolute inset-0'>
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,_var(--primary)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-[radial-gradient(circle_at_70%_70%,_var(--secondary)_0%,_transparent_70%)] opacity-10' />
          <div className='absolute inset-0 bg-grid-primary/[0.03] bg-[size:20px_20px]' />
        </div>

        <div className='container mx-auto px-4'>
          <div className='relative'>
            {/* Floating Elements */}
            <div className='absolute inset-0 overflow-hidden pointer-events-none'>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
                className='absolute w-full h-full'
              >
                {/* Decorative Elements */}
                <div className='absolute top-10 left-10 w-20 h-20 border border-primary/20 rounded-full' />
                <div className='absolute top-20 right-20 w-32 h-32 border-2 border-primary/20 rounded-full' />
                <div className='absolute bottom-10 left-1/4 w-24 h-24 border border-secondary/20 rounded-full' />

                {/* Animated Dots */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                  className='absolute top-1/3 right-1/3'
                >
                  <div className='w-3 h-3 rounded-full bg-primary/40' />
                </motion.div>
              </motion.div>
            </div>

            {/* Content Grid */}
            <div className='grid lg:grid-cols-2 gap-16 items-center'>
              {/* Left Column */}
              <div className='relative z-10'>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                >
                  <div className='inline-flex items-center gap-2 bg-primary/5 border border-primary/10 rounded-full px-4 py-1.5 mb-8'>
                    <span className='text-sm font-medium text-primary'>Limited Time Offer</span>
                  </div>

                  <h2 className='text-4xl md:text-5xl font-bold mb-6'>
                    Unlock Your
                    <span className='relative whitespace-nowrap mx-2'>
                      <span className='relative z-10 gradient-text'>Trading Potential</span>
                      <svg
                        aria-hidden='true'
                        viewBox='0 0 418 42'
                        className='absolute left-0 top-2/3 h-[0.6em] w-full fill-primary/20'
                        preserveAspectRatio='none'
                      >
                        <path d='M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z' />
                      </svg>
                    </span>
                    Today
                  </h2>

                  <p className='text-xl text-gray-600 mb-8'>
                    Start your 14-day free trial and join thousands of professional traders who have already elevated
                    their trading game.
                  </p>

                  {/* Feature List */}
                  <div className='space-y-4 mb-8'>
                    {[
                      { icon: <Check className='size-5 text-primary' />, text: 'No credit card required' },
                      { icon: <Check className='size-5 text-primary' />, text: 'Full platform access' },
                      { icon: <Check className='size-5 text-primary' />, text: '24/7 premium support' }
                    ].map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: index * 0.1 }}
                        className='flex items-center gap-3'
                      >
                        <div className='flex items-center justify-center size-6 rounded-full bg-primary/10'>
                          {feature.icon}
                        </div>
                        <span className='text-gray-600'>{feature.text}</span>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Buttons */}
                  <div className='flex flex-wrap gap-4 relative'>
                    {/* Animated background effect */}
                    <div
                      className='absolute -inset-10 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 opacity-50 blur-3xl'
                      style={{
                        maskImage: 'radial-gradient(circle at center, black, transparent 80%)',
                        WebkitMaskImage: 'radial-gradient(circle at center, black, transparent 80%)'
                      }}
                    />

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{
                        type: 'spring',
                        stiffness: 400,
                        damping: 25
                      }}
                    >
                      <Button size='lg' className='rounded-sm px-8 relative overflow-hidden group'>
                        <motion.div
                          className='absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100'
                          initial={false}
                          transition={{ duration: 0.3 }}
                        />
                        <span className='relative z-10 flex items-center'>
                          Start Free Trial
                          <ArrowRight className='ml-2 h-4 w-4 transition-transform group-hover:translate-x-1' />
                        </span>
                      </Button>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{
                        type: 'spring',
                        stiffness: 400,
                        damping: 25,
                        delay: 0.1
                      }}
                    >
                      <Button size='lg' variant='outline' className='rounded-sm px-8 relative overflow-hidden group'>
                        <motion.div
                          className='absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100'
                          initial={false}
                          transition={{ duration: 0.3 }}
                        />
                        <span className='relative z-10 flex items-center'>
                          Schedule Demo
                          <Calendar className='ml-2 h-4 w-4 transition-transform group-hover:scale-110' />
                        </span>
                      </Button>
                    </motion.div>
                  </div>

                  {/* Mouse follow effect */}
                  <motion.div
                    className='pointer-events-none fixed inset-0 z-30 transition duration-300'
                    style={{
                      background:
                        'radial-gradient(600px at var(--mouse-x, 0) var(--mouse-y, 0), rgba(var(--primary-rgb), 0.05), transparent 80%)'
                    }}
                  />
                </motion.div>
              </div>

              {/* Right Column */}
              <div className='relative'>
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  className='relative z-10'
                >
                  {/* Testimonial Card */}
                  <div className='bg-white rounded-2xl p-8 shadow-xl'>
                    <div className='flex items-center gap-4 mb-6'>
                      <Image
                        src='/assets/images/testimonial-avatar.jpg'
                        alt='Testimonial'
                        width={56}
                        height={56}
                        className='rounded-full'
                      />
                      <div>
                        <h4 className='font-semibold'>Sarah Thompson</h4>
                        <p className='text-sm text-gray-600'>Professional Trader</p>
                      </div>
                    </div>
                    <blockquote className='text-lg text-gray-700 mb-6'>
                      "Switching to Cloud Terminal was a game-changer for my trading strategy. The platform's speed and
                      reliability are unmatched."
                    </blockquote>
                    <div className='flex items-center gap-1'>
                      {[...Array.from({ length: 5 })].map((_, i) => (
                        <Star key={i} className='size-5 fill-yellow-400 text-yellow-400' />
                      ))}
                    </div>
                  </div>

                  {/* Stats Cards */}
                  <div className='grid grid-cols-2 gap-4 mt-4'>
                    <div className='bg-white rounded-xl p-6 shadow-lg'>
                      <div className='text-3xl font-bold text-primary mb-2'>10x</div>
                      <div className='text-sm text-gray-600'>Faster Execution</div>
                    </div>
                    <div className='bg-white rounded-xl p-6 shadow-lg'>
                      <div className='text-3xl font-bold text-primary mb-2'>24/7</div>
                      <div className='text-sm text-gray-600'>Expert Support</div>
                    </div>
                  </div>
                </motion.div>

                {/* Background Decoration */}
                <div className='absolute -z-10 inset-0 transform translate-x-4 translate-y-4'>
                  <div className='absolute inset-0 bg-primary/5 rounded-2xl' />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
