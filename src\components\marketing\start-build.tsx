'use client';

import { motion, useAnimationControls } from 'framer-motion';
import { ArrowRight, Building, CheckCircle2, Server, Shield, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

export const StartBuild = () => {
  const router = useRouter();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const control1 = useAnimationControls();
  const control2 = useAnimationControls();
  const control3 = useAnimationControls();
  const control4 = useAnimationControls();
  const controls = [control1, control2, control3, control4];

  useEffect(() => {
    controls.forEach((control, index) => {
      control.start({
        scale: [1, 1.02, 1],
        transition: {
          duration: 4,
          delay: index * 0.5,
          repeat: Infinity,
          repeatType: 'reverse',
          ease: 'easeInOut'
        }
      });
    });
  }, []);

  const stats = [
    { number: '10K+', label: 'Active Developers', icon: Users },
    { number: '99.9%', label: 'System Uptime', icon: Server },
    { number: '150+', label: 'Enterprise Clients', icon: Building },
    { number: '24/7', label: 'Expert Support', icon: Shield }
  ];

  return (
    <div className='w-full bg-gradient-to-b from-transparent to-background/80 backdrop-blur-sm mt-20 py-24'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-16 items-start'>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='space-y-8'
          >
            <div className='space-y-4'>
              <h2 className='text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent'>
                Start Building Today
              </h2>
              <p className='text-foreground/70 leading-relaxed max-w-lg'>
                Join thousands of developers and companies who trust our platform for their fintech infrastructure
                needs.
              </p>
            </div>

            <div className='space-y-4'>
              {[
                { icon: CheckCircle2, text: 'Zero setup time, instant deployment' },
                { icon: Shield, text: 'Enterprise-grade security protocols' },
                { icon: Server, text: 'Scalable infrastructure, pay as you grow' }
              ].map((item, index) => (
                <div key={index} className='flex items-center gap-3'>
                  <item.icon className='w-5 h-5 text-primary' />
                  <span className='text-foreground/80'>{item.text}</span>
                </div>
              ))}
            </div>

            <div className='flex items-center gap-6'>
              <Button
                size='lg'
                onClick={() => router.push('/signup')}
                className='group relative overflow-hidden px-6 py-3 bg-primary hover:bg-primary/90 transition-all duration-300'
              >
                <span className='relative z-10 flex items-center gap-2'>
                  Start Building
                  <ArrowRight className='w-4 h-4 transition-transform group-hover:translate-x-1' />
                </span>
                <div
                  className='absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                    translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700'
                />
              </Button>
              <Link
                href='/documentation'
                className='text-foreground/70 text-sm hover:text-primary transition-colors duration-200 flex items-center gap-2'
              >
                View Documentation
              </Link>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className='relative'
          >
            <div className='grid grid-cols-2 gap-8'>
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  animate={controls[index]}
                  whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
                  onHoverStart={() => setHoveredIndex(index)}
                  onHoverEnd={() => setHoveredIndex(null)}
                  style={{
                    marginTop: index % 2 === 0 ? '0px' : '-35px',
                    ...(index >= 2 ? { marginTop: index % 2 === 0 ? '35px' : '0px' } : {})
                  }}
                  className='relative p-6 rounded-2xl bg-background/40 border border-border/85
                    backdrop-blur-sm group cursor-pointer overflow-hidden
                    transition-all duration-300 ease-in-out'
                >
                  <div
                    className='absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5
                    animate-gradient-move'
                  />

                  <div
                    className='absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r
                    from-primary/20 via-secondary/20 to-primary/20 animate-border-flow opacity-0
                    group-hover:opacity-100 transition-opacity duration-300'
                  />

                  <div className='absolute inset-0 overflow-hidden'>
                    {[...Array.from({ length: 3 })].map((_, i) => (
                      <div
                        key={i}
                        className='absolute w-20 h-20 rounded-full bg-primary/5 animate-float'
                        style={{
                          left: `${Math.random() * 100}%`,
                          top: `${Math.random() * 100}%`,
                          animationDelay: `${i * 0.5}s`
                        }}
                      />
                    ))}
                  </div>

                  <motion.div className='relative z-10 flex flex-col items-start gap-3'>
                    <motion.div
                      animate={{
                        rotate: hoveredIndex === index ? [0, -10, 10, 0] : 0
                      }}
                      transition={{ duration: 0.5 }}
                      className='p-2 rounded-xl bg-primary/10 backdrop-blur-sm'
                    >
                      <stat.icon className='w-6 h-6 text-primary transition-colors duration-300' />
                    </motion.div>

                    <div>
                      <div
                        className='text-3xl font-bold bg-gradient-to-r from-primary to-secondary
                        bg-clip-text text-transparent'
                      >
                        {stat.number}
                      </div>
                      <div className='text-sm text-foreground/80 mt-1'>{stat.label}</div>
                    </div>
                  </motion.div>

                  <div
                    className='absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                    translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000'
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default StartBuild;
