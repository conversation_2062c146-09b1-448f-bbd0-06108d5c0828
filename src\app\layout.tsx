import type { Metadata } from 'next';

import { GoogleTagManager } from '@next/third-parties/google';
import { setRequestLocale } from 'next-intl/server';
import { Figtree } from 'next/font/google';
import { routing } from '@/libs/i18nNavigation';
import { MainProvider } from '@/providers/main';
import '@/styles/global.css';
import '@/styles/marketing.css';

const figtree = Figtree({
  weight: ['300', '500', '600', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  variable: '--font-figtree',
  display: 'swap'
});

export const metadata: Metadata = {
  icons: [
    {
      rel: 'apple-touch-icon',
      url: '/apple-touch-icon.png'
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '32x32',
      url: '/favicon-32x32.png'
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '16x16',
      url: '/favicon-16x16.png'
    },
    {
      rel: 'icon',
      url: '/favicon.ico'
    }
  ]
};

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}
export default async function RootLayout(props: { children: React.ReactNode; params: Promise<{ locale: string }> }) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <html lang={locale} className={figtree.variable} suppressHydrationWarning>
      <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID || ''} />
      <body className={figtree.className} suppressHydrationWarning>
        <MainProvider>{props.children}</MainProvider>
      </body>
    </html>
  );
}
