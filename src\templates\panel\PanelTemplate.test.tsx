import { render } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import messages from '@/locales/en.json';
import { PanelTemplate } from './PanelTemplate';
import '../../../test/setup';

describe('Panel template', () => {
  describe('Render method', () => {
    it('should render main content', () => {
      const testContent = <div data-testid='test-content'>Test Content</div>;

      const { getByTestId } = render(
        <NextIntlClientProvider locale='en' messages={messages}>
          <PanelTemplate>{testContent}</PanelTemplate>
        </NextIntlClientProvider>
      );

      expect(getByTestId('test-content')).toBeInTheDocument();
    });

    it('should render toaster component', () => {
      const { container } = render(
        <NextIntlClientProvider locale='en' messages={messages}>
          <PanelTemplate>{null}</PanelTemplate>
        </NextIntlClientProvider>
      );

      // Toaster has a role of 'region'
      expect(container.querySelector('[role="region"]')).toBeInTheDocument();
    });
  });
});
