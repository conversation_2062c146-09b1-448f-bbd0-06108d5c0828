'use client';
import * as React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

type DetailInfo = {
  name?: string;
  region?: string;
  server_name?: string;
  login?: string;
  status?: number;
};

const Overview = ({ detailInfo }: { detailInfo?: DetailInfo }) => {
  if (!detailInfo) {
    return null;
  }
  const { name = '', region = '', server_name = '', login = '', status = 0 } = detailInfo;

  const statusFormater = () => {
    let statusText: string;
    let statusColor: string;
    switch (status) {
      case 5:
        statusText = 'Active';
        statusColor = 'green-600';
        break;
      case 11:
        statusText = 'Inactive';
        statusColor = 'red-600';
        break;
      default:
        statusText = 'Pending';
        statusColor = 'yellow-600';
    }
    return (
      <div className={`bg-${statusColor} opacity-60  w-[80px] rounded-md px-2 text-sm text-center text-white`}>
        {statusText}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-primary'>Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid gap-4'>
          <div className='space-y-2'>
            <p className='text-sm font-medium text-primary'>Name</p>
            <p className='text-sm text-muted-foreground'>{name}</p>
          </div>
          <div className='space-y-2'>
            <p className='text-sm font-medium text-primary'>Region</p>
            <p className='text-sm text-muted-foreground'>{region}</p>
          </div>
          <div className='space-y-2'>
            <p className='text-sm font-medium text-primary'>Server</p>
            <p className='text-sm text-muted-foreground'>{server_name}</p>
          </div>
          <div className='space-y-2'>
            <p className='text-sm font-medium text-primary'>Login</p>
            <p className='text-sm text-muted-foreground'>{login}</p>
          </div>
          <div className='space-y-2'>
            <p className='text-sm font-medium text-primary'>Status</p>
            {statusFormater()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Overview;
