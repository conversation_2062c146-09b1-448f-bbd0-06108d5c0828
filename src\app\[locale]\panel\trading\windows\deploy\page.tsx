import { getTranslations, setRequestLocale } from 'next-intl/server';
import { DeployServer } from '@/components/panel/windows-server/deploy/deploy-from';

type IDeployServerPageProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IDeployServerPageProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'DeployServer'
  });

  return {
    title: t('meta_title'),
    description: t('meta_description')
  };
}

export default async function ServerDeployPage(props: IDeployServerPageProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div className='container mx-auto'>
      <DeployServer />
    </div>
  );
}
